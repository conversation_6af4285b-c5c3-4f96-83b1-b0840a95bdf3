#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const process = require('process')

// 将命令行参数解析为对象
function resolveArgv(args) {
  const res = {}
  for (let i = 0; i < args.length; i++) {
    const c = args[i]
    if (c.startsWith('-')) {
      const n = args[i + 1]
      if (typeof n === 'undefined') {
        throw new Error(`the argv [${c}] missing value`)
      }
      res[c.slice(1)] = n
    } else {
      res[c] = true
    }
  }

  return res
}

/*
 * @param s: source, building project output dir
 * @param t: target, need clean has old hash file dir
 * */
const argv = Object.assign({}, resolveArgv(process.argv.slice(2)))

if (!argv.s) {
  throw new Error('arguments must has build source dir! e.g. -s path/to/file')
}
if (!argv.t) {
  throw new Error('arguments must has delete target dir! e.g. -t path/to/file')
}

// 匹配拥有hash值的文件正则
const reg = /(?<name>.*?)\.(?<hash>[0-9a-f]{8})\.(?<ext>\S.*?)$/

// 将编译输出目录中拥有hash值的文件解析为对象缓存
function readDirSync(p, result = {}) {
  try {
    const res = fs.readdirSync(p)

    res.forEach((f, index) => {
      const _p = path.resolve(`${p}/${f}`)
      const info = fs.statSync(_p)
      if (info.isDirectory()) {
        readDirSync(_p, result)
      } else {
        const m = f.match(reg)
        if (!m || !m.groups) {
          return false
        }
        // 保存拥有hash值的文件
        result[`${m.groups.name}.${m.groups.ext}`] = {
          ...m.groups,
        }
      }
    })

    return result
  } catch (e) {
    throw new Error(`can not readDir ${p} ${e}`)
  }
}

const files = readDirSync(argv.s)

let delFileCount = 0
let delTotalSize = 0

// 删除一个月前的缓存文件
function deleteFile(p) {
  const stat = fs.statSync(p)
  // 文件最后修改时间
  const statMTime = new Date(stat.mtime).getTime()
  // 计算上个月的时间
  let date = new Date()
  const prevMonth = date.getMonth() - 1
  date = date.setMonth(prevMonth)
  const prevMonthTime = new Date(date).getTime()
  // 如果文件最后修改时间为一个月内，则保留缓存
  if (statMTime > prevMonthTime) {
    return false
  }

  // 开始删除文件
  delTotalSize += stat ? stat.size : 0
  fs.unlinkSync(p)
  delFileCount++
  console.log('delete file:', p)
}

function getSize(m) {
  let size = m / (2 ** 10) ** 2
  // 取两位小数
  size = Math.round(size * 100) / 100

  return size
}

const excludes = ['new-ui']

function isIgnoreDir(dir) {
  return excludes.some(item => dir.includes(item))
}

// 递归删除旧的hash文件
function deleteOldHashFile(p) {
  try {
    const res = fs.readdirSync(p)
    res.forEach((f, index) => {
      const _p = path.resolve(`${p}/${f}`)
      const info = fs.statSync(_p)
      if (info.isDirectory()) {
        if (isIgnoreDir(f)) {
          console.log('isIgnoreDir res:', f, index)
          return false
        }
        deleteOldHashFile(_p)
      } else {
        const m = f.match(reg)

        // 跳过没有hash值的文件
        if (!m || !m.groups || !m.groups.hash) {
          return false
        }

        // 有hash值，但不在最新编译的文件路径中，需要删除
        const cacheFileName = `${m.groups.name}.${m.groups.ext}`
        if (!(cacheFileName in files)) {
          deleteFile(_p)
          return false
        }

        // 跳过相同的文件
        if (m.groups.hash === files[cacheFileName].hash) {
          return false
        }

        // 删除旧的hash文件
        deleteFile(_p)
      }
    })
  } catch (e) {
    throw new Error('deleteOldHashFile ' + e)
  }
}

deleteOldHashFile(argv.t)
console.log('delete file: %d', delFileCount)
console.log('delete size: %d MB', getSize(delTotalSize))
