import { nextTick } from 'vue'

// 自定义 Vue 指令,最大化、最小化、拖动等指令集合
export const bfDirective = {
  mounted(el, binding, vnode) {
    const $elm = $(el)
    const $dialog = $elm.parent().parent()
    const $header = $elm.parent()

    const addElm = (opts, clickCallback) => {
      const btn = document.createElement('button')
      btn.type = 'button'
      btn.className = `el-dialog__headerbtn ${opts.btnClassName}`
      btn.innerHTML = `<i class='el-dialog__close el-icon ${opts.iconClassName}'></i>`
      btn.onclick = clickCallback
      $header.append(btn)
    }

    // 添加最小化按钮
    if (binding.modifiers.min === true) {
      const minOpts = {
        btnClassName: 'bf-btn-minus',
        iconClassName: 'el-icon-minus',
      }
      const minCb = () => {
        const instance = binding.instance
        instance.visible = false
        instance.isMini = true
        bfglob.emit('minusEvent', instance.bfmini || {})
        instance.$emit('bf-mini')
      }
      addElm(minOpts, minCb)
    }

    // 添加最大化和还原按钮
    if (binding.modifiers.max === true) {
      const maxOpts = {
        btnClassName: 'bf-btn-plus',
        iconClassName: 'el-icon-plus',
      }
      const maxCb = () => {
        $.fn.dataTable &&
          $.fn.dataTable
            .tables({
              visible: true,
              api: true,
            })
            .columns.adjust()

        const instance = binding.instance
        instance.bfmaxi = !instance.bfmaxi
        instance.isMini = false
        instance.$nextTick(() => {
          if (instance.bfmaxi) {
            $dialog.css({
              left: '0',
              top: '0',
            })
          } else {
            $dialog.css({
              left: (window.innerWidth - $dialog[0].offsetWidth) / 2 + 'px',
              top: '80px',
            })
          }

          instance.$emit('bf-max-restore', instance.bfmaxi)
        })
      }
      addElm(maxOpts, maxCb)
    }

    // 对话框可拖动功能
    if (binding.modifiers.drag === true) {
      // 获取对应的dialog和header元素，并设置相关样式
      const dlg = $dialog[0]
      const header = $header[0]

      header.style.userSelect = 'none'
      header.style['-ms-user-select'] = 'none'
      header.style['-moz-user-select'] = 'none'
      header.style.cursor = 'default'

      // 添加并监听drag相关移动事件
      const move = function (e) {
        // 限制dialog在可视区域内拖动
        if (e.pageX >= 0 && e.pageX <= window.innerWidth) {
          dlg.style.left = e.pageX - dlg.offsetX + 'px'
        }
        if (e.pageY >= 0 && e.pageY <= window.innerHeight) {
          dlg.style.top = e.pageY - dlg.offsetY + 'px'
        }
      }
      const up = function () {
        removeEventListener('mousemove', move)
        removeEventListener('mouseup', up)
      }
      const down = function (e) {
        dlg.offsetX = e.pageX - dlg.offsetLeft
        dlg.offsetY = e.pageY - dlg.offsetTop

        addEventListener('mousemove', move)
        addEventListener('mouseup', up)

        // displayFirst(dlg)
      }
      header.addEventListener('mousedown', down)

      // 在dialog初始化后添加对应指令的className
      // vnode.context.$nextTick(() => {
      //   dlg.parentNode.className = `${dlg.parentNode.className} bf-drag`
      //   if (!dlg.className.includes('is-fullscreen')) {
      //     dlg.style.top = '60px'
      //   }
      // })

      // 给dialog组件添加mounted回调事件，在挂载组件后，重新计算组件地left初始值
      // if (!vnode.context.$options.mounted) {
      //   vnode.context.$options.mounted = []
      // }
      // vnode.context.$options.mounted.push(() => {
      //   setTimeout(() => {
      //     if (!dlg.className.includes('is-fullscreen')) {
      //       dlg.style.left = (window.innerWidth - dlg.offsetWidth) / 2 + 'px'
      //       dlg.style.top = Math.max(0, (window.innerHeight - dlg.offsetHeight) / 2) + 'px'
      //     }
      //   }, 0)
      // })

      // 初始化dialog位置
      nextTick(() => {
        if (!dlg.className.includes('is-fullscreen')) {
          dlg.style.left = (window.innerWidth - dlg.offsetWidth) / 2 + 'px'
          dlg.style.top =
            Math.max(0, (window.innerHeight - dlg.offsetHeight) / 2) + 'px'
        }

        if (!dlg.parentNode.className.includes('bf-drag')) {
          dlg.parentNode.className = `${dlg.parentNode.className} bf-drag`
        }
      })

      // $dialog.click((e) => {
      //   displayFirst(dlg)
      // })
    }
  },
  // 报警提时，update没有执行，添加“bf-drag”class放在mounted中执行示弹窗
  // updated(el, binding) {
  //   if (binding.modifiers.drag === true) {
  //     nextTick(() => {
  //       // 获取dialog元素
  //       const dlg = $(el).parent().parent()[0]
  //       if (!dlg.parentNode.className.includes('bf-drag')) {
  //         dlg.parentNode.className = `${dlg.parentNode.className} bf-drag`
  //       }
  //     })
  //   }
  // },
}

/* // 添加最大化和还原按钮
Vue.directive('bf-maximize', {
  bind(el, binding, vnode) {
    const vm = vnode.context
    const $elm = $(vnode.elm)
    const $dialog = $elm.find('.el-dialog')
    const $header = $elm.find('.el-dialog__header')
    const addElm = (opts, clickCallback) => {
      const btn = document.createElement('button')
      btn.type = 'button'
      btn.className = `el-dialog__headerbtn ${opts.btnClassName}`
      btn.innerHTML = `<i class='el-dialog__close el-icon ${opts.iconClassName}'></i>`
      btn.onclick = clickCallback
      $header.append(btn)
    }

    const maxOpts = {
      btnClassName: 'bf-btn-plus',
      iconClassName: 'el-icon-plus',
    }
    const maxCb = () => {
      $.fn.dataTable && $.fn.dataTable.tables({
        visible: true,
        api: true,
      }).columns.adjust()
      vm.bfmaxi = !vm.bfmaxi
      vm.isMini = false
      vm.$nextTick(() => {
        if (vm.bfmaxi) {
          $dialog.css({
            left: '0',
            top: '0',
          })
        } else {
          $dialog.css({
            left: (window.innerWidth - $dialog[0].offsetWidth) / 2 + 'px',
            top: '80px',
          })
        }
        if (typeof vm[binding.expression] === 'function') {
          vm[binding.expression]()
        }
      })
    }
    addElm(maxOpts, maxCb)
  },
}) */

// export default null

// 自定义指令: 拖拽
export const bfDrag = {
  mounted(el, binding) {
    const $elm = $(el)
    const $dialog = $elm.parent().parent()
    const $header = $elm.parent()

    // 获取对应的dialog和header元素，并设置相关样式
    const dilg = $dialog[0]
    const header = $header[0]

    header.style.userSelect = 'none'
    header.style['-ms-user-select'] = 'none'
    header.style['-moz-user-select'] = 'none'
    header.style.cursor = 'default'

    // 添加并监听drag相关移动事件
    const move = function (e) {
      // 限制dialog在可视区域内拖动
      if (e.pageX >= 0 && e.pageX <= window.innerWidth) {
        dilg.style.left = e.pageX - dilg.offsetX + 'px'
      }
      if (e.pageY >= 0 && e.pageY <= window.innerHeight) {
        dilg.style.top = e.pageY - dilg.offsetY + 'px'
      }
    }

    const up = function () {
      removeEventListener('mousemove', move)
      removeEventListener('mouseup', up)
    }
    const down = function (e) {
      dilg.offsetX = e.pageX - dilg.offsetLeft
      dilg.offsetY = e.pageY - dilg.offsetTop

      addEventListener('mousemove', move)
      addEventListener('mouseup', up)
    }

    header.addEventListener('mousedown', down)

    // 初始化dialog位置
    nextTick(() => {
      if (!dilg.className.includes('is-fullscreen')) {
        dilg.style.left = (window.innerWidth - dilg.offsetWidth) / 2 + 'px'
        dilg.style.top =
          Math.max(0, (window.innerHeight - dilg.offsetHeight) / 2) + 'px'
      }

      if (!dilg.parentNode.className.includes('bf-drag')) {
        dilg.parentNode.className = `${dilg.parentNode.className} bf-drag`
      }
    })
  },
}
