<template>
  <el-dialog
    v-model="pocVisible"
    :title="pocDialogTitle"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :fullscreen="fullscreen"
    append-to-body
    class="poc-settings-dialog"
  >
    <el-form
      ref="pocForm"
      :label-width="pocManagerLabelWidth"
      :model="pocSettingData"
      :rules="rules"
      label-position="top"
      :class="[
        'flex',
        'gap-3',
        'poc-page-form',
        'px-2',
        ...(isMobile ? ['flex-col-reverse', 'h-100'] : []),
      ]"
    >
      <div :class="isMobile ? 'w-full' : 'poc-form-container'">
        <el-row :gutter="20" :class="isMobile ? 'mr-0' : 'mb-2'" align="middle">
          <el-col :xs="24" :sm="12">
            <el-form-item
              class="mb-3 send-group form-item-ellipsis"
              prop="txGroupDmrid"
            >
              <template #label>
                <span class="form-item-label">{{
                  $t('dialog.sendGroup')
                }}</span>
              </template>
              <el-select
                v-model="pocSettingData.txGroupDmrid"
                :placeholder="$t('dialog.select')"
                filterable
                clearable
                @change="txGroupDmridChange"
              >
                <el-option
                  v-for="item in txGroupsData"
                  :key="item.key"
                  :label="item.label"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12">
            <el-form-item
              :label="$t('loginDlg.password')"
              prop="password"
              class="password mb-3"
            >
              <el-input
                v-model="pocSettingData.password"
                type="password"
                :maxlength="16"
                clearable
                show-password
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12">
            <el-form-item prop="pocConfig.canEditSubscriptionLocal">
              <el-checkbox
                v-model="pocSettingData.pocConfig.canEditSubscriptionLocal"
                :true-value="1"
                :false-value="0"
              >
                <span v-text="$t('dialog.localEditRxGroup')" />
              </el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item
          :class="['form-item-transfer', isMobile ? 'mobile-transfer' : '']"
        >
          <template #label>
            <span class="form-item-label">{{
              $t('dialog.rxGroupsSetting')
            }}</span>
          </template>
          <el-transfer
            v-model="pocSettingData.rxGroupDmrids"
            class="flex md:flex-row flex-col gap-3 justify-between items-center"
            :titles="transferTitles"
            :format="transferLabelFormat"
            :data="availableRxGroup"
            target-order="push"
            @change="checkChange"
          />
        </el-form-item>
      </div>
      <div
        :class="[
          'overflow-auto',
          'flex',
          'flex-col',
          'flex-grow',
          'form-item-tree',
          ...(isMobile ? ['h-52', 'w-full'] : []),
        ]"
      >
        <div class="flex justify-between pb-2">
          <span :class="isMobile ? 'ml-0' : 'poc-tree-title'">{{
            $t('dialog.pocTreeTitle')
          }}</span>
          <div>
            <span v-text="selectedNodes.length || 0" />
            <span>/</span>
            <span v-text="maxSize" />
          </div>
        </div>
        <TableTree
          :ref="treeId"
          :treeId="treeId"
          :option="treeOpts"
          :filterOption="filterOption"
          @loaded="toDictDefaultTreeNodes"
        />
      </div>
    </el-form>
    <template #footer>
      <section class="data_btns">
        <el-button @click="pocVisible = false" v-text="$t('dialog.cancel')" />
        <el-button
          type="primary"
          @click="confirmEdit()"
          v-text="$t('dialog.confirm')"
        />
      </section>
    </template>
  </el-dialog>
</template>

<script>
import { SupportedLang } from '@/modules/i18n'
import bfutil, { checkDmrIdIsGroup, getCommonOrgType } from '@/utils/bfutil'
import TableTree from '@/components/common/tableTree'
import bfTime from '@/utils/time'
import bfNotify from '@/utils/notify'
import validateRules from '@/utils/validateRules'
import bfCrypto from '@/utils/crypto'
import bfproto from '@/modules/protocol'
import dbCmd from '@/modules/protocol/db.pb.cmd'
import { cloneDeep } from 'lodash'

const defaultPocSetting = {
  txGroupDmrid: '', // 发射组
  rxGroupDmrids: [], // 接收组
  password: '', // poc终端密码
  pocIndividualContacts: [], // 单呼
  pocGroupContacts: [], // 组呼
  pocConfig: {
    // 0:不可编辑 1:可编辑
    canEditSubscriptionLocal: 0,
  },
}

const commonOrgType = getCommonOrgType()
const filterDynamicGroup = dataList => {
  return dataList.filter(data => commonOrgType.includes(data.orgIsVirtual))
}
let selectedTimer = 0
let passwordCache = ''

export default {
  name: 'PocSettings',
  emits: ['update:visible', 'syncPocSetting', 'select'],
  components: { TableTree },
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    device: {
      type: Object,
      required: true,
    },
    isNewStatus: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      pocSettingData: cloneDeep(defaultPocSetting),
      // 默认发射组数据源
      txGroupsData: [],
      treeId: 'pocDeviceTree',
      maxSize: 128,
      selectedNodesLen: 0,
    }
  },
  computed: {
    isMobile() {
      return this.$root.layoutLevel === 0
    },
    dbSubject() {
      return `db.${bfglob.sysId}`
    },
    rules() {
      if (this.isNewStatus) {
        return {
          password: [validateRules.required()],
          txGroupDmrid: [validateRules.required()],
        }
      } else {
        return {
          txGroupDmrid: [validateRules.required()],
        }
      }
    },
    pocVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
    fullscreen() {
      return this.$root.layoutLevel === 0
    },
    locale() {
      return this.$i18n.locale
    },
    isEN() {
      return this.locale === SupportedLang.enUS
    },
    isEn() {
      return this.isEN
    },
    isFR() {
      return this.locale === SupportedLang.fr
    },
    deviceName() {
      return this.device.selfId || this.device.dmrId || ''
    },
    pocDialogTitle() {
      const name = this.deviceName
      const title = this.$t('dialog.pocDeviceManage')
      if (!name) return title
      return `${title} - ${name}`
    },
    pocManagerLabelWidth() {
      return this.isFR || this.isEN ? '130px' : '100px'
    },
    treeOpts() {
      return {
        selectMode: 2,
        select: this.selectNodes,
        beforeSelect: this.beforeSelect,
        dblclick: this.dblclickNode,
        click: this.clickNode,
      }
    },
    filterOption() {
      return {
        leavesOnly: false,
      }
    },
    pocTableTree() {
      return this.$refs.pocDeviceTree
    },
    selectedNodes() {
      return [
        ...this.pocSettingData.pocGroupContacts,
        ...this.pocSettingData.pocIndividualContacts,
      ]
    },
    transferTitles() {
      return [this.$t('dialog.unListenGroup'), this.$t('dialog.listenGroup')]
    },
    transferLabelFormat() {
      return {
        noChecked: '${total}',

        hasChecked: '${checked}/${total}',
      }
    },
    availableRxGroup() {
      const availableRx = []
      for (let i = 0; i < this.pocSettingData.pocGroupContacts.length; i++) {
        const dmrId = this.pocSettingData.pocGroupContacts[i]
        const org = bfglob.gorgData.getDataByIndex(dmrId)
        if (org) {
          availableRx.push({
            key: org.dmrId,
            label: org.orgShortName,
            disabled:
              this.pocSettingData.rxGroupDmrids.length === 1 &&
              org.dmrId === this.pocSettingData.rxGroupDmrids[0],
          })
        }
      }
      return availableRx
    },
  },
  methods: {
    // 初始化默认发射组的数据源
    initTxGroupsOptions() {
      const list = []
      // 需要过滤掉动态组的数据
      const orgs = filterDynamicGroup(
        bfutil.objToArray(bfglob.gorgData.getAll()),
      )

      for (const k in orgs) {
        const org = orgs[k]
        list.push({
          key: org.dmrId,
          label: org.orgShortName,
        })
      }
      this.txGroupsData = list
    },
    initPocSettingData() {
      if (!this.device) {
        bfglob.console.warn('initPocSettingData not found device')
        return
      }
      try {
        const pocSettingData = JSON.parse(this.device.pocSetting || '{}')
        this.pocSettingData = {
          ...defaultPocSetting,
          ...pocSettingData,
        }
      } catch (e) {
        bfglob.console.warn(
          'initPocSettingData JSON.parse falid',
          e,
          this.device.pocSetting,
        )
      }
      if (!this.isNewStatus) {
        // 编辑状态的时候，缓存密码，未更新密码时使用原密码
        passwordCache = this.pocSettingData.password
        this.pocSettingData.password = ''
        // 判断组呼和单呼的数据都是否存在 不存在则提示用户是否同步
        const address = [
          ...this.pocSettingData.pocGroupContacts,
          ...this.pocSettingData.pocIndividualContacts,
        ]
        const group = []
        const individual = []
        const notInSys = []
        for (let i = 0; i < address.length; i++) {
          const dmrId = address[i]
          const isGroup = checkDmrIdIsGroup(dmrId)
          const target = isGroup
            ? bfglob.gorgData.getDataByIndex(dmrId)
            : bfglob.gdevices.getDataByIndex(dmrId)
          if (target) {
            isGroup ? group.push(dmrId) : individual.push(dmrId)
          } else {
            notInSys.push(dmrId)
          }
        }
        if (notInSys.length > 0) {
          this.pocSettingData.pocGroupContacts = group
          this.pocSettingData.pocIndividualContacts = individual
          this.pocSettingData.rxGroupDmrids =
            this.pocSettingData.rxGroupDmrids.filter(i => !notInSys.includes(i))
          // 默认发射组已被删除? 则使用设备所属组织未作默认组 并添加到联系人 接收组
          if (notInSys.includes(this.pocSettingData.txGroupDmrid)) {
            this.txSetDefaultDeviceOrg()
          }
        }
      }
    },
    txSetDefaultDeviceOrg() {
      if (!this.$refs?.pocDeviceTree?.getRootNode()) {
        setTimeout(() => {
          this.txSetDefaultDeviceOrg()
        }, 50)
        return
      }
      this.pocSettingData.txGroupDmrid = bfglob.gorgData.get(
        this.device.orgId,
      ).dmrId
      this.$refs.pocDeviceTree.setNodeSelected(this.device.orgId, true)
      if (this.pocSettingData.rxGroupDmrids.length === 0) {
        const deviceOrgDmrId = bfglob.gorgData.get(this.device.orgId).dmrId
        deviceOrgDmrId && this.pocSettingData.rxGroupDmrids.push(deviceOrgDmrId)
      }
    },
    initPocSetting() {
      this.initTxGroupsOptions()
      this.initPocSettingData()
    },
    toDictDefaultTreeNodes() {
      if (!this.$refs.pocDeviceTree) {
        setTimeout(() => {
          this.toDictDefaultTreeNodes()
        }, 50)
        return
      }
      this.$refs.pocDeviceTree
        .toDictTree('bftree', dict => {
          dict.partsel = false
          dict.selected = false
          dict.expanded = true
          if (
            !!dict.data &&
            dict.data.isOrg &&
            [100, 101, 102].includes(dict.data.orgIsVirtual)
          ) {
            return false
          }

          return true
        })
        .then(() => {
          this.$refs.pocDeviceTree.updateViewport(
            this.$refs.pocDeviceTree.getLocalTree(),
          )
        })

      if (this.isNewStatus && this.device.orgId) {
        const deviceOrg = bfglob.gorgData.get(this.device.orgId)
        this.pocSettingData.txGroupDmrid = deviceOrg?.dmrId ?? ''
        this.txGroupDmridChange(deviceOrg?.dmrId ?? '')
      }
    },
    clickNode(event, data) {
      const excludeList = ['expander', 'prefix', 'checkbox']
      if (excludeList.includes(data.targetType)) {
        return true
      }
      const node = data.node
      if (!node) {
        return false
      }
      node.setActive()
      node.setSelected(!node.isSelected())
    },
    // 深度选中节点，如果当前节点已经选中，则取消当前节点及所有子级节点的选中状态，反之选中节点
    deepSelectedNodes(node, isSelected = false, selectedCount = 0) {
      const children = [node]
      while (children.length > 0 && selectedCount < this.maxSize) {
        const child = children.shift()
        if (!child) {
          continue
        }

        child.data.dblClickSelected = isSelected
        child.setSelected(isSelected)
        if (isSelected) {
          selectedCount++
        } else {
          selectedCount--
        }
        // 执行完当前节点后，判断是否存在子节点，把子节点添加在数组最后，达到遍历该树的所有子孙节点
        if (child.hasChildren()) {
          children.push(...child.getChildren())
        }
      }
    },
    dblclickNode(event, data) {
      const node = data.node
      if (!node) {
        return false
      }

      node.setActive()
      const dblClickSelected = node.data.dblClickSelected
      this.deepSelectedNodes(
        node,
        node.unselectable ? !dblClickSelected : !node.selected,
        this.selectedNodes.length - 1,
      )
      return false
    },
    beforeSelect(event, data) {
      const node = data.node
      if (!node) {
        return true
      }
      if (node.isSelected()) {
        return true
      }
      return !(this.selectedNodesLen >= this.maxSize)
    },
    selectNodes(event, data) {
      clearTimeout(selectedTimer)
      selectedTimer = setTimeout(() => {
        const nodes = data.tree.getSelectedNodes()
        const booksCache = {}
        const individualContacts = []
        const groupContacts = []
        const oldDmrId = this.pocSettingData.txGroupDmrid
        this.pocSettingData.txGroupDmrid =
          bfglob.gorgData.get(this.device?.orgRid)?.dmrId || ''
        for (let i = 0; i < nodes.length; i++) {
          if (
            individualContacts.length + groupContacts.length >=
            this.maxSize
          ) {
            break
          }

          const node = nodes[i]
          const dmrId = node.data.dmrId ?? node.data.origin.dmrId
          if (dmrId === oldDmrId) {
            this.pocSettingData.txGroupDmrid = dmrId
          }
          const isGroup = checkDmrIdIsGroup(dmrId)
          let target = null
          if (isGroup) {
            target = bfglob.gorgData.getDataByIndex(dmrId)
          } else {
            target = bfglob.gdevices.getDataByIndex(dmrId)
          }

          // 非本系统权限内的数据，不可选, 跳过不处理
          if (!target && dmrId) {
            continue
          }

          if (booksCache[dmrId]) {
            continue
          }
          if (isGroup) {
            groupContacts.push(dmrId)
          } else {
            individualContacts.push(dmrId)
          }
          booksCache[dmrId] = 1
        }
        this.pocSettingData.pocGroupContacts = groupContacts
        this.pocSettingData.rxGroupDmrids =
          this.pocSettingData.rxGroupDmrids.filter(i =>
            groupContacts.includes(i),
          )
        this.pocSettingData.pocIndividualContacts = individualContacts
        this.selectedNodesLen = this.selectedNodes.length
      }, 50)
    },
    syncPocSettingToDevice(pocSettingJson, lastModifyTime) {
      this.$emit('syncPocSetting', pocSettingJson, lastModifyTime)
    },
    confirmEdit() {
      if (this.isNewStatus) {
        this.$refs.pocForm.validate(valid => {
          if (!valid) {
            return
          }
          // 同步poc setting数据到父组件
          this.syncPocSettingToDevice(
            JSON.stringify(this.pocSettingData),
            bfTime.nowUtcTime(),
          )
          this.pocVisible = false
        })
      } else {
        // 更新poc终端settings
        this.updatePocSetting()
      }
    },
    updateAddrBookTree(data) {
      this.treeReload()
      this.redrawViewport()
    },
    treeReload(clearCache = false) {
      // this.pocTableTree.clearTree()
      this.toDictDefaultTreeNodes()
      this.$nextTick(() => {
        if (clearCache) {
          this.pocSettingData.pocGroupContacts = []
          this.pocSettingData.pocIndividualContacts = []
          this.pocSettingData.rxGroupDmrids = []
          this.$emit('select', this.selectedNodes)
        } else {
          this.restoreNodeSelectedStatus()
        }
      })
    },
    restoreNodeSelectedStatus() {
      if (!this.$refs.pocDeviceTree) {
        setTimeout(() => {
          this.restoreNodeSelectedStatus()
        }, 50)
        return
      }
      // 重置选中的节点数据
      const contact = [
        ...this.pocSettingData.pocIndividualContacts,
        ...this.pocSettingData.pocGroupContacts,
      ]
      for (let i = 0; i < contact.length; i++) {
        const isGroup = checkDmrIdIsGroup(contact[i])
        const target = isGroup
          ? bfglob.gorgData.getDataByIndex(contact[i])
          : bfglob.gdevices.getDataByIndex(contact[i])
        target && this.$refs.pocDeviceTree.setNodeSelected(target.rid, true)
      }
      // 将树中当前设备的节点禁用
      const deviceTarget = bfglob.gdevices.getDataByIndex(this.device?.dmrId)
      if (deviceTarget) {
        const node = this.$refs.pocDeviceTree.getNodeByKey(deviceTarget.rid)
        node.unselectable = true
      }
      const orgNode = this.$refs?.pocDeviceTree?.getNodeByKey(this.device.orgId)
      if (orgNode) {
        orgNode.unselectable = true
      }
    },
    redrawViewport(retry = 0) {
      if (retry > 10) {
        return
      }
      this.pocTableTree.updateViewport()
      setTimeout(() => {
        const viewport = this.pocTableTree.getTree().viewport
        const count = this.pocTableTree.getViewportCount()
        if (
          isNaN(viewport.count) ||
          isNaN(viewport.start) ||
          count !== viewport.count
        ) {
          this.redrawViewport(++retry)
        }
      }, 0)
    },
    txGroupDmridChange(val) {
      if (val) {
        // 同步勾选上通讯录
        const target = bfglob.gorgData.getDataByIndex(val)
        this.pocTableTree.setNodeSelected(target.rid, true)
        // 默认发射组不在接收组中，则添加到接收组中
        if (!this.pocSettingData.rxGroupDmrids.includes(val)) {
          this.pocSettingData.rxGroupDmrids.push(val)
        }
      }
    },
    updatePocSetting() {
      this.$refs.pocForm.validate(valid => {
        if (valid) {
          const pocSettingObj = Object.assign({}, this.pocSettingData)
          if (pocSettingObj.password) {
            pocSettingObj.password = bfCrypto.sha256(
              this.device.dmrId + this.pocSettingData.password,
            )
          } else {
            pocSettingObj.password = passwordCache
          }
          const pocSetting = JSON.stringify(pocSettingObj)

          // const msgObj = {
          //   ...this.device,
          //   pocSetting,
          //   pocSettingLastModifyTime: bfTime.nowUtcTime(),
          // }

          const msgObj = {
            orgId: this.device.orgId,
            rid: this.device.rid,
            dmrId: this.device.dmrId,
            pocSetting,
            pocSettingLastModifyTime: bfTime.nowUtcTime(),
          }

          const msgOpts = {
            rpcCmdFields: {
              origReqId: 'rid',
              resInfo: 'org_id,poc_setting,poc_setting_last_modify_time',
            },
          }

          return bfproto
            .sendMessage(
              dbCmd.DB_DEVICE_PUPDATE,
              msgObj,
              'db_device',
              this.dbSubject,
              msgOpts,
            )
            .then(rpc_cmd_obj => {
              bfglob.console.log('update poc setting res:', rpc_cmd_obj)
              const isOk = rpc_cmd_obj.resInfo === '+OK'
              if (isOk) {
                bfNotify.messageBox(this.$t('msgbox.upSuccess'), 'success')
                // 添加查询日志
                const note =
                  this.$t('dialog.update') +
                  msgObj.selfId +
                  ' / ' +
                  msgObj.dmrId +
                  this.$t('dialog.pocDevice') +
                  this.$t('msgbox.deviceData')
                bfglob.emit('addnote', note)
                // 更新全局组织机构数据
                bfglob.emit('update_global_deviceData', {
                  ...this.device,
                  ...msgObj,
                })
                // 同步到父组件pocDeviceData
                this.syncPocSettingToDevice(
                  msgObj.pocSetting,
                  msgObj.pocSettingLastModifyTime,
                )
                this.pocVisible = false
              } else {
                bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
              }
              return Promise.resolve(isOk)
            })
            .catch(err => {
              bfglob.console.warn('update device timeout:', err)
              bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
              return Promise.resolve(false)
            })
        }
      })
    },
    // 三个参数： 当前值、数据移动的方向（'left' / 'right'）、发生移动的数据 key 数组
    checkChange(_, direction, __) {
      if (
        direction === 'left' &&
        this.pocSettingData.rxGroupDmrids.length === 0 &&
        this.pocSettingData.txGroupDmrid
      ) {
        this.pocSettingData.rxGroupDmrids.push(this.pocSettingData.txGroupDmrid)
      }
    },
  },
  mounted() {
    this.initPocSetting()
    this.restoreNodeSelectedStatus()
  },
}
</script>

<style lang="scss">
.el-dialog.poc-settings-dialog {
  width: 900px;
  //margin-top: 12vh !important;

  .el-dialog__footer {
    border-top: 1px solid #ddd;
  }

  .el-form.poc-page-form {
    height: 466px;

    .poc-form-container {
      width: 480px;
    }

    .poc-tree-title {
      margin-left: 20px;
    }

    .el-form-item {
      margin-bottom: unset;

      label.el-form-item__label {
        padding: 0;
      }
    }

    .form-item-tree {
      .fancytree-grid-container {
        border: 1px solid #ebeef5;
        border-radius: 4px;
        height: calc(100% - 30px - 18px);

        table {
          table-layout: fixed;

          tbody tr {
            height: 22px !important;
          }
        }
      }
    }

    .form-item-transfer {
      .el-form-item__content .el-transfer {
        .el-transfer__buttons {
          display: flex;
          flex-direction: column;
          padding: 0;
          gap: 12px;

          .el-button {
            border-radius: 50%;
            padding: 12px;
            margin: 0;
            width: 40px;
            height: 40px;
          }
        }

        .el-transfer-panel {
          .el-transfer-panel__body {
            height: 284px;

            .el-transfer-panel__list {
              height: 100%;

              label.el-transfer-panel__item {
                height: 24px;
              }
            }
          }
        }
      }
    }

    .mobile-transfer {
      .el-transfer {
        .el-transfer-panel {
          width: 100%;
        }

        .el-transfer__buttons {
          transform: rotate(90deg);
        }
      }
    }

    .el-form-item.password {
      .el-input .el-input__suffix .el-input__suffix-inner {
        display: flex;
      }
    }
  }

  .data_btns button {
    width: 20%;
  }
}

.el-message-box.poc-alert-message {
  .el-message-box__container {
    .el-message-box__status {
      top: 12px;
    }

    .el-message-box__status::before {
      font-size: 22px;
    }

    .el-message-box__message {
      padding-left: 28px;
    }
  }
}
</style>
