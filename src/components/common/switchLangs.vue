<template>
  <!-- 根元素：相对定位，并监听鼠标进入和离开事件 -->
  <div class="relative inline-block" @mouseenter="isMenuVisible = true" @mouseleave="isMenuVisible = false">
    <img src="@/assets/images/loginBg/switch_langs.svg" class="cursor-pointer self-end" />

    <transition
      enter-active-class="transition-all duration-200 ease-out"
      enter-from-class="opacity-0 translate-y-2"
      leave-active-class="transition-all duration-150 ease-in"
      leave-to-class="opacity-0 translate-y-2"
    >
      <div
        v-show="isMenuVisible"
        class="absolute top-full left-1/2 -translate-x-1/2 mt-2 w-[120px] box-border pt-6 px-3 pb-3 bg-no-repeat bg-[length:100%_100%] bg-[url('@/assets/images/loginBg/1x/list_dlg.webp')]"
      >
        <!-- 列表容器 -->
        <ul class="list-none m-0 p-0 h-full overflow-y-auto">
          <li
            v-for="lang in languageList"
            :key="lang.code"
            class="text-white text-center text-base py-2 rounded-md cursor-pointer transition-colors hover:bg-white/20"
            :class="{ '!text-[#FF811D] font-bold': lang.code === currentLang }"
            @click="selectLanguage(lang.code)"
          >
            {{ lang.name }}
          </li>
        </ul>
      </div>
    </transition>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import { SupportedLangList, getDisplayLabel, loadLanguageAsync } from '@/modules/i18n'
  import bfutil from '@/utils/bfutil'
  import i18n from '@/modules/i18n'

  const isMenuVisible = ref(false)
  const currentLang = computed(() => i18n.global.locale)

  const languageList = computed(() => {
    return SupportedLangList.map(lang => {
      return {
        code: lang,
        name: getDisplayLabel(lang),
      }
    })
  })

  const emit = defineEmits(['change'])

  const selectLanguage = langCode => {
    isMenuVisible.value = false
    emit('change', langCode)
    if (currentLang.value === langCode) return
    loadLanguageAsync(langCode).then(bfutil.saveLang)
  }
</script>

<style scoped>
  ul::-webkit-scrollbar {
    width: 4px;
  }
  ul::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
  }
</style>
