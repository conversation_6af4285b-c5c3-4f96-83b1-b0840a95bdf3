<template>
  <DataTable
    ref="dataTable"
    :key="dataTableKey"
    :columns="finalColumns"
    :data="tableData"
    :options="options"
    :class="tableClass"
    class="display compact table-hover table-striped table-bordered dataTable bfdataTable"
    width="100%"
  />
</template>

<script setup>
import {
  ref,
  computed,
  effect,
  nextTick,
  onMounted,
  onActivated,
  onBeforeUnmount,
} from 'vue'
import i18n from '@/modules/i18n'
import DataTable from 'datatables.net-vue3'
import DataTablesCore from 'datatables.net-bs4'
import 'datatables.net-scroller-bs4'
import 'datatables.net-buttons-bs4'
import 'datatables.net-staterestore-bs4'
import $ from 'jquery'
import { cloneDeep, debounce } from 'lodash'
import bfutil from '@/utils/bfutil'
import '@/utils/dataTables.cellEdit'
import bfNotify from '@/utils/notify'
import bfTime from '@/utils/time'
import { writeFile } from 'xlsx'

DataTable.use(DataTablesCore)
const excludesDisable = ['dynamicGroupTable']

const emit = defineEmits(['row-click', 'row-dbclick'])
const props = defineProps({
  tableClass: {
    type: String,
    default: '',
  },
  name: {
    type: String,
    required: true,
  },
  head: {
    type: Array,
    required: true,
  },
  data: {
    type: Array,
    required: true,
  },
  order: {
    type: Array,
    default: () => {
      return [[2, 'asc']]
    },
  },
  detailHead: {
    type: Array,
  },
  detailBodyName: {
    type: String,
  },
  exportNamePrefix: {
    type: String,
    default: 'bfdx',
  },
  buttons: {
    type: Array,
    default: () => [],
  },
  columnDefs: {
    type: Array,
  },
  detailBodyIndex: {
    type: Boolean,
    default: true,
  },
  edit: {
    type: Boolean,
    default: false,
  },
  detailRender: {
    type: Function,
  },
  getDetailData: {
    type: Function,
  },
  historyTable: {
    type: Boolean,
    default: false,
  },
  scrollY: {
    type: String,
    default: '50vh',
  },
  // dataTables v2+版本表格布局参数
  layout: {
    type: Object,
  },
  rowId: {
    type: String,
    default: 'rid',
  },
})
const dataTable = ref()
const dt = computed(() => dataTable.value?.dt)
// 不直接将props.data传递到DataTable组件，因为上层数据管理类组件更新data都是直接替换对象赋值的，整个对象替换会导致整个表格重新渲染
const tableData = ref(cloneDeep(props.data))

// 事件驱动的表格更新函数
function addTableRow(rowData) {
  if (!dt.value) return
  tableData.value.push(rowData)
}

function updateTableRow(rowData) {
  if (!dt.value) return
  const rowId = rowData[props.rowId]
  const row = dt.value.row(`#${rowId}`)
  if (row.length > 0) {
    // invalidate方法是告诉dataTable该行数据缓存已经失效，需要更新
    // https://datatables.net/reference/api/row().invalidate()
    row.data(rowData).invalidate().draw(false)
  } else {
    bfglob.console.warn(`DataTable[${props.name}] 未找到要更新的行:`, rowId)
  }
}

function deleteTableRow(rowData) {
  if (!dt.value) return
  const index = tableData.value.findIndex(
    item => item[props.rowId] === rowData[props.rowId],
  )
  if (index !== -1) {
    tableData.value.splice(index, 1)
  } else {
    bfglob.console.warn(
      `DataTable[${props.name}] 未找到要删除的行:`,
      rowData[props.rowId],
    )
  }
}

function replaceTableData(rows) {
  tableData.value = rows
}

const tableLanguages = computed(() => {
  return {
    all: i18n.global.t('dtable.all'),
    'buttons.colvis': i18n.global.t('dtable.buttons.colvis'),
    emptyTable: i18n.global.t('dtable.emptyTable'),
    info: i18n.global.t('dtable.info'),
    infoEmpty: i18n.global.t('dtable.infoEmpty'),
    infoFiltered: i18n.global.t('dtable.infoFiltered'),
    lengthMenu: i18n.global.t('dtable.lengthMenu'),
    loadingRecords: i18n.global.t('dtable.loadingRecords'),
    processing: i18n.global.t('dtable.processing'),
    zeroRecords: i18n.global.t('dtable.zeroRecords'),
  }
})
const finalColumns = computed(() => {
  const index_col = {
    title: i18n.global.t('dialog.index'),
    data: null,
    // orderable: false,
    defaultContent: '',
    width: '80px',
    render: (data, type, row, meta) => {
      return meta.row + 1
    },
  }
  if (props.detailHead) {
    index_col.class = 'details-control'
    index_col.width = '80px'
  }
  const _columns = [index_col]
  return _columns.concat(props.head)
})

const { exportMethod } = useExport()

const finalButtons = computed(() => {
  return [
    {
      text: `<i class='mdi mdi-download'></i>${i18n.global.t('dialog.export')}`,
      action: exportMethod,
      enabled: props.data.length > 0,
      className: 'el-button el-button--default',
    },
    ...props.buttons,
  ]
})

effect(() => {
  for (let i = 0; i < finalButtons.value.length; i++) {
    const btnOpts = finalButtons.value[i]
    dt.value?.button(i).enable(btnOpts.enabled ?? true)
  }
})

const layout = computed(() => {
  if (props.layout) {
    return props.layout
  }

  const search = {
    /* 自定义过滤输入表单参数 */
    text: '',
    placeholder: i18n.global.t('dataTable.inputcontent'),
  }

  if (props.historyTable) {
    return {
      topStart: null,
      topEnd: null,
      bottomStart: null,
      bottomEnd: null,
      bottom: [
        {
          buttons: finalButtons.value,
        },
        {
          search: search,
        },
        'info',
      ],
    }
  }

  return {
    topStart: {
      buttons: finalButtons.value,
    },
    topEnd: {
      search: search,
    },
    bottomStart: 'info',
    bottomEnd: null,
  }
})

const options = computed(() => {
  return {
    // destroy: true,
    stateSave: true,
    autoWidth: false,
    searching: true,
    info: true,
    deferRender: true,
    scroller: true,
    scrollCollapse: true,
    scrollX: true,
    fixedHeader: true,
    rowId: props.rowId,
    scrollY: props.scrollY,
    columnDefs: props.columnDefs || [],
    language: tableLanguages.value,
    // 详细参考：https://datatables.net/reference/option/layout
    layout: layout.value,
    initComplete: settings => {
      // console.log('initComplete', settings)
      // loadRestoreState()
      initEvents()
      props.detailHead && loadChildTable()
      if (props.data.length > 0) {
        nextTick(() => {
          const tr = $(dataTable.value.$el).find(
            '.dt-scroll-body tbody tr:first-child',
          )
          tr?.click()
        })
      }
    },
    createdRow: (row, data, dataIndex) => {
      if (excludesDisable.includes(props.name)) {
        return
      }
      // 判断数据是否为权限内数据，以禁用行点击/双击/高亮的事件
      const noPermOrg = bfglob.noPermOrgData.get(data.orgId || data.parentOrgId)
      if (noPermOrg) {
        $(row).addClass('row-disabled')
      }
    },
    drawCallback: settings => {
      // 表格在其他标签(tabs)隐藏下，无法正确得到列宽，不设置tooltip
      if (!settings?.nTBody?.offsetWidth) {
        return
      }

      // this.drawTableTdTooltips()
    },
    stateSaveCallback: debounce((settings, data) => {
      // 切换语言时，非当前标签页的组件的$el不再dom上，不会滚动，所有保存的状态会丢失滚动距离
      // 判断$el不再dom上时，则不执行保存state, sessionStorage中还是保存着切换语言之前的state, 标签页在切换回去时，会恢复原来的状态
      if (!document.documentElement.contains(dataTable.value.$el)) {
        return
      }
      sessionStorage.setItem(props.name, JSON.stringify(data))
    }, 300),
  }
})

const customRender = computed(() => {
  return typeof props.detailRender === 'function'
})

function loadRestoreState() {
  try {
    const sessionState = sessionStorage.getItem(props.name)
    if (sessionState) {
      const state = JSON.parse(sessionState)
      nextTick(() => {
        dt.value.state(state).draw()
        // 状态恢复后，如果有展开的子行，需要重置表格高度
        nextTick(() => {
          resetTableHeight()
        })
      })
    }
  } catch (e) {
    // no-empty
    console.error('loadRestoreState catch error:', e)
  }
}

function useExport() {
  function generateChildHeader() {
    if (!props.detailHead) {
      return []
    }

    const header = props.detailHead.map(v => {
      return v.title
    })
    // 缩进一个单元格
    header.unshift('')

    return header
  }

  function generateChildData(childData) {
    // index为子表索引序号
    let index = 1
    const childBody = []

    for (const k in childData) {
      const child = childData[k]
      // 设置子表每行的单元格数据
      const data = props.detailHead.map((v, i) => {
        if (typeof v.render === 'function') {
          return v.render(child[v.data], 'string', child, {
            row: index,
            col: i,
          })
        }
        // 替换掉无用的标签元素字符串
        if (v.data === '_checkResult') {
          return child[v.data].replace(/<span .*?>|<\/span>.*?/gi, '')
        }
        return child[v.data]
      })
      // 重置子表索引
      if (props.detailBodyIndex) {
        data[0] = index
      }
      // 子表缩进一个单元格
      data.unshift('')
      // 将子表的每行都push到_body中
      childBody.push(data)
      // 子表索引递增
      index++
    }

    return childBody
  }

  function generateChildTable(originData, exportDataBody) {
    if (!props.detailHead) {
      return []
    }
    const body = []

    // 从表格数据中读取源数据索引，查找对应的源数据，生成子表数据
    for (let i = 0; i < exportDataBody.length; i++) {
      const index = exportDataBody[i][0] - 1
      const childData =
        customRender.value && props.getDetailData
          ? props.getDetailData(originData[index].rid)
          : originData[index][props.detailBodyName]
      const hasChildData = Object.keys(childData || {}).length > 0
      // 跳过没有子表数据的表格行生成逻辑
      if (hasChildData) {
        body.push(generateChildData(childData))
      } else {
        body.push([])
      }
    }
    return body
  }

  function exportMethod(event, api) {
    // 创建 Web Worker
    const exportExcelWorker = new Worker(
      new URL('@/worker/exportExcel.worker.js?worker', import.meta.url),
      {
        type: 'module',
      },
    )
    exportExcelWorker.onmessage = event => {
      // 处理完数据，关闭当前worker进程
      exportExcelWorker.terminate()

      // 导出 Excel
      const time = bfTime.nowLocalTime().replace(' ', '_').replace(/-|:/gi, '')
      const filename = `${props.exportNamePrefix}_${time}.xlsx`
      writeFile(event.data, filename)
    }
    exportExcelWorker.onerror = () => {
      // worker进程创建失败，提示用户导出数据失败
      bfNotify.messageBox(i18n.global.t('msgbox.exportError'), 'error')
    }
    exportExcelWorker.postMessage({
      command: 'sheetName',
      sheetName: props.exportNamePrefix,
    })

    const exportData = dt.value.buttons.exportData()
    const originData = Array.prototype.slice.call(dt.value.data())
    const data = {
      data: {
        header: exportData.header,
        body: exportData.body,
        footer: exportData.footer,
      },
      child: {
        header: generateChildHeader(),
        data: generateChildTable(originData, exportData.body),
      },
    }
    exportExcelWorker.postMessage(data)
  }

  return {
    exportMethod,
  }
}

// 更新组件的key,以达到实现重新渲染表格的需求
const dataTableKey = computed(() => 'data-table--' + i18n.global.locale)

function useDataTableEvents() {
  let $tbody = null

  function findTbody() {
    return $(dataTable.value.$el).find('.dt-scroll-body')
  }

  function processRowClick(tr, e) {
    if (!$tbody) {
      $tbody = findTbody()
    }
    const $tr = $(tr)
    // 点击子行自动选择子行的父元素
    // if (!$tr[0].classList.contains('odd') && !$tr[0].classList.contains('even')) {
    //   $tr = $($tr[0].previousSibling || $tr)
    // }

    $($tbody).find('tbody tr.selected').removeClass('selected')
    $tr.addClass('selected')
    const data = dt.value.row(tr).data()
    emit('row-click', data, e)
  }

  function resetTableHeight() {
    setTimeout(() => {
      if (!$tbody) {
        $tbody = findTbody()
      }

      const tableEl = $tbody.find('table.bfdataTable')?.[0]
      if (!tableEl) return

      $tbody.find('table.bfdataTable+div').height(tableEl.scrollHeight)
    }, 0)
  }

  function initEvents() {
    $tbody = findTbody()
    if (!$tbody) return

    // 行单击事件
    $tbody.on('click', 'tr', function (e) {
      // 表格行点击事件
      processRowClick(this, e)
    })

    // 行双击
    $tbody.on('dblclick', 'tr', function (e) {
      const data = dt.value.row(this).data()
      emit('row-dbclick', data, e)
    })
    nextTick(() => {
      dt.value.on('draw.dt', resetTableHeight)
    })
  }

  function setTooltip(el) {
    if (el.offsetWidth >= el.scrollWidth) {
      return
    }

    const $el = $(el)
    const text = $el.text()
    if (!text) {
      return
    }

    $el.attr('title', text)
  }

  function drawTableTdTooltips() {
    // 表头内容溢出，设置title属性
    const dataTableParents = $(dataTable.value).parents('.dt-scroll')
    const thList = dataTableParents.find('.dt-scroll-head thead th')
    thList.each(function () {
      setTooltip(this)
    })

    // 表格内容溢出，设置title属性
    const tdList = $(dataTable.value).find('tbody td')
    tdList.each(function () {
      setTooltip(this)
    })
  }

  function columnsAdjust() {
    dt.value?.columns.adjust()
    drawTableTdTooltips()
  }

  return {
    processRowClick,
    resetTableHeight,
    initEvents,
    columnsAdjust,
  }
}

const { initEvents, resetTableHeight, columnsAdjust } = useDataTableEvents()

function useRowChild() {
  const { processRowClick, resetTableHeight } = useDataTableEvents()

  function defaultRender(rowData) {
    if (Object.keys(rowData[props.detailBodyName] || {}).length > 0) {
      const format_row_child_table = data => {
        let html = `<table cellpadding='0' cellspacing='0' border='0'
                class='display table table-striped table-bordered row-details'
                ><thead>`
        html += bfutil.getTableHead(props.detailHead)
        html += '</thead><tbody>'

        let detailBodyName = props.detailBodyName
        if (Array.isArray(props.detailBodyName)) {
          detailBodyName = 'detailBodyNameTmp'
          data[detailBodyName] = {}
          data[detailBodyName].default = this.detailBodyName
            .map(name => {
              return { [name]: data[name] }
            })
            .reduce((p, c) => {
              return Object.assign(p, c)
            }, {})
        }

        const detailBody =
          customRender.value && props.getDetailData
            ? props.getDetailData(data.rid)
            : data[detailBodyName]
        html += bfutil.getTableBody(props.detailHead, detailBody)
        html += '</tbody></table>'

        return html
      }

      return format_row_child_table(rowData)
    }

    return ''
  }

  function getChildTable(rowData) {
    let childTable = ''
    // 有给子表渲染函数
    if (typeof props.detailRender === 'function') {
      childTable = props.detailRender(rowData, () => defaultRender(rowData))
    } else {
      // 采用默认的渲染逻辑
      childTable = defaultRender(rowData)
    }

    return childTable
  }

  function loadChildTable() {
    $(dataTable.value.$el)
      .find('.dt-scroll-body tbody')
      .off('click', 'td.details-control')
      .on('click', 'td.details-control', function (e) {
        e.stopPropagation()

        const $this = $(this)
        const tr = $this.closest('tr')

        // 上面已经阻止了事件冒泡，就必须再次处理，否则点击控件单元格无法选择表格行
        processRowClick(tr, e)

        const row = dt.value.row(tr)
        const rowData = row.data()
        const childTable = getChildTable(rowData)
        if (!childTable) {
          ElMessage({
            message: i18n.global.t('dialog.childRowIsEmpty'),
            grouping: true,
            // type: 'warning',
          })
          return
        }
        if (row.child.isShown()) {
          row.child.hide()
        } else {
          row.child(childTable).show()
        }
        nextTick(() => {
          resetTableHeight()
        })
      })
  }

  function removeState() {
    sessionStorage.removeItem(props.name)
  }

  function bindDtRequestChild() {
    dt.value.on('requestChild', (e, row) => {
      row.child(getChildTable(row.data())).show()
      nextTick(() => {
        // 展开了子行，但是没有在上层tr添加dt-hasChild类，导致上层tr不是显示的展开按钮样式
        const tr = document.getElementById(row.data().rid)
        tr?.setAttribute('class', 'dt-hasChild')
      })
    })
  }

  return {
    loadChildTable,
    getChildTable,
    removeState,
    bindDtRequestChild,
  }
}

const { loadChildTable, removeState, bindDtRequestChild } = useRowChild()

onMounted(() => {
  // 需要在onMounted中执行，在initDataTable中绑定不了，此时dt.value还不存在
  bindDtRequestChild()

  window.addEventListener('beforeunload', removeState)

  bfglob.on('change_lang', lang => {
    // 语言切换后，表格重新渲染了，此时计算属性dt.value已经改变了， 原来绑定的请求子行的事件失效了，需要重新绑定
    bindDtRequestChild()
    loadRestoreState()
  })
  if (props.name) {
    // 监听添加行事件
    bfglob.on(`add-${props.name}`, addTableRow)

    // 监听更新行事件
    bfglob.on(`update-${props.name}`, updateTableRow)

    // 监听删除行事件
    bfglob.on(`delete-${props.name}`, deleteTableRow)
  }
})

onActivated(() => {
  loadRestoreState()
})

onBeforeUnmount(() => {
  window.removeEventListener('beforeunload', removeState)
  if (props.name) {
    bfglob.off(`add-${props.name}`, addTableRow)
    bfglob.off(`update-${props.name}`, updateTableRow)
    bfglob.off(`delete-${props.name}`, deleteTableRow)
  }
})

// 暴露给父组件的方法
defineExpose({
  instance: dt,
  resetTableHeight,
  columnsAdjust,
  replaceTableData,
})
</script>

<style lang="scss">
@import url('bootstrap');
@import url('datatables.net-bs4');
@import url('datatables.net-buttons-bs4');
@import url('datatables.net-scroller-bs4');
@import url('datatables.net-staterestore-bs4');

.dt-container {
  font-size: 14px;

  .dt-buttons.btn-group {
    @media (min-width: 2560px) {
      .btn {
        line-height: 1;
        padding: 12px 20px;
      }
    }

    .btn {
      outline: none;
      color: #fff;
      background-color: #409eff;
      border-color: #409eff;
      font-size: 14px;
      box-shadow: none !important;

      &:first-child {
        border-right-color: #66b1ff;
      }

      &:last-child {
        border-left-color: #66b1ff;
      }

      &:not(:first-child):not(:last-child) {
        border-left-color: #66b1ff;
        border-right-color: #66b1ff;
      }

      &:focus,
      &:hover {
        background-color: #66b1ff !important;
        border-color: #66b1ff !important;
      }

      &:active {
        background-color: #3a8ee6 !important;
        border-color: #3a8ee6 !important;
      }

      &.disabled {
        cursor: not-allowed;
      }
    }

    .btn:last-child:not(:first-child),
    .dropdown-toggle:not(:first-child) {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }

    .btn:first-child:not(:last-child):not(.dropdown-toggle) {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    .btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
      border-radius: 0;
    }
  }

  .dt-scroll {
    .dt-scroll-head table.dataTable thead > tr > th,
    .dt-scroll-body table.dataTable tbody > tr > td,
    .dt-scroll-body table.row-details thead > tr > th,
    .dt-scroll-body table.row-details tbody > tr > td {
      padding: 2px 4px;
      height: 36px;
      line-height: 1;
      vertical-align: middle;
      word-break: break-all;
      // text-align: center;
      // background-color: transparent;
      // overflow: hidden;
      // text-overflow: ellipsis;
      // white-space: nowrap;
    }

    .dt-scroll-head {
      table.dataTable thead tr > th {
        padding-right: 20px;

        .dt-column-order {
          right: 4px;
        }
      }
    }

    .dt-scroll-body {
      & > table.dataTable.table-striped > tbody > tr {
        td.details-control {
          cursor: pointer;
          min-width: 20px;
          text-align: right;
          background: url(@/images/sysImg/details_open.png) no-repeat left;
          padding-left: 22px;
        }

        &.selected > td {
          background-color: rgb(var(--dt-row-selected));
          box-shadow: none;
          color: #fff;
        }

        &.row-disabled {
          background-color: unset !important;
          color: #e6a23c !important;
          font-style: italic;
          cursor: default;
        }

        &.dt-hasChild td.details-control {
          background-image: url(@/images/sysImg/details_close.png);
          padding-left: 22px;
        }

        &.dt-hasChild + tr {
          td table {
            margin-bottom: 0;
            min-width: 150px;
          }
        }
      }

      table.row-details thead > tr > th,
      table.row-details tbody > tr > td {
        height: 36px !important;
      }

      table.row-details thead > tr {
        background-color: #fff;
      }
    }
  }

  .el-button + .el-button {
    margin-left: unset;
  }

  @media (max-width: 767px) {
    & > div:last-child > div:first-child {
      display: flex;
      flex-wrap: wrap;
      width: 100%;

      div {
        box-sizing: border-box;
      }

      .dt-buttons.btn-group {
        flex: 0 0 30%;
      }
      .dt-search {
        flex: 0 0 70%;
        margin-bottom: 0.5em;
        input {
          width: calc(100% - 10px);
        }
      }
      .dt-info {
        flex-basis: 100%;
        text-align: left;
      }
    }
  }
}
</style>
