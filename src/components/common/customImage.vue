<template>
  <el-dialog
    v-model="dlgVisible"
    :title="$t('dialog.customImg')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :fullscreen="fullscreen"
    class="select-image-dialog"
    append-to-body
  >
    <section class="flex flex-wrap image-container">
      <div
        v-for="(item, index) in imgData"
        :key="index"
        class="image-item"
        :data-fileName="item.name"
        @click="clickImageListItem(item)"
      >
        <img :src="item.src" class="h-full w-full image" />
      </div>
    </section>

    <section class="mt-3 center upload_box">
      <el-upload
        action=""
        :multiple="false"
        :before-upload="before_upload"
        :show-file-list="false"
      >
        <el-button type="primary" v-text="$t('dialog.selComputer')" />
        <template #tip>
          <div class="el-upload__tip" v-text="$t('dialog.selImgPrompt')" />
        </template>
      </el-upload>
    </section>
  </el-dialog>
</template>

<script>
import bfutil from '@/utils/bfutil'
import bfNotify from '@/utils/notify'
import bfCrypto from '@/utils/crypto'

// 内置图片加载缓存，避免重复发起请求
const imgDataCache = {}

const imagesCtx = import.meta.glob(
  [
    '@/images/browser/*.png',
    '@/images/mapImg/*_cir.png',
    '@/images/sysImg/*_per*.png',
    '@/images/sysImg/u*.jpg',
    '@/images/sysImg/(budui|gongan|red_star|tielu|xiaofang|tower|province|city|county).png',
  ],
  { eager: true },
)

export default {
  name: 'BfCustomImage',
  emits: ['update:modelValue', 'update:visible'],
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    modelValue: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      imgData: [
        ...Object.keys(imagesCtx).map(key => {
          const name = key.replace(/.*\/(.*\.(png|jpg))$/, '$1')
          const item = imagesCtx[key]
          return {
            name,
            src: item.default,
          }
        }),
      ],
    }
  },
  methods: {
    emitImage(imgFile) {
      this.$emit('update:modelValue', imgFile)
      this.dlgVisible = false
      this.name = imgFile.fileName
    },
    clickImageListItem(item) {
      // 已经加载处理过，使用缓存
      if (imgDataCache[item.name]) {
        this.emitImage(imgDataCache[item.name])
        return
      }

      // 使用image功能，加载图片
      const image = new Image()
      image.crossOrigin = ''
      image.src = item.src
      image.onload = function (e) {
        const dataURL = bfutil.getBase64Image(image)
        const imgFile = {
          fileName: item.name,
          fileContent: dataURL,
          hash: bfCrypto.sha256(dataURL),
        }
        imgDataCache[item.name] = imgFile
        this.emitImage(imgFile)
      }.bind(this)
    },
    before_upload(file) {
      if (file.size > 102400) {
        bfNotify.messageBox(this.$t('msgbox.selImgError'), 'error')
        return false
      }

      const reader = new FileReader()
      reader.onload = function () {
        const dataURL = reader.result
        const imgFile = {
          fileName: file.name,
          fileContent: dataURL,
          hash: bfCrypto.sha256(dataURL),
        }
        this.emitImage(imgFile)
      }.bind(this)
      reader.readAsDataURL(file)
      return false
    },
  },
  computed: {
    dlgVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
    fullscreen() {
      return !(this.$root.layoutLevel > 0)
    },
  },
}
</script>

<style lang="scss">
.select-image-dialog {
  width: 442px;

  .image-container {
    gap: 8px;

    .image-item {
      width: 30px;
      height: 30px;
      border: 1px solid transparent;
      border-radius: 4px;
      padding: 1px;
      cursor: pointer;

      &:hover {
        border-color: #f00;
      }
    }
  }
}
</style>
