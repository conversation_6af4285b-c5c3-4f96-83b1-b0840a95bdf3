<template>
  <el-tooltip
    :disabled="!isOverflow"
    :content="props.content"
    placement="top"
    effect="dark"
  >
    <p
      ref="textRef"
      class="truncate"
      :class="finalClass"
      :style="props.style"
      @mouseenter="checkOverflow"
    >
      {{ props.content }}
    </p>
  </el-tooltip>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, computed } from 'vue'

const props = defineProps({
  content: {
    type: String,
    required: true,
  },
  class: {
    type: [String, Array, Object],
    default: '',
  },
  style: {
    type: [String, Object],
    default: '',
  },
  notWfull: {
    type: Boolean,
    default: false,
  },
})

const defaultClass = 'w-full'

const finalClass = computed(() => {
  return props.notWfull ? props.class : defaultClass + ' ' + props.class
})

const textRef = ref(null)
const isOverflow = ref(false)

const checkOverflow = () => {
  const el = textRef.value
  if (!el) return
  isOverflow.value = el.scrollWidth > el.clientWidth
}

onMounted(() => {
  nextTick(checkOverflow)
})

watch(
  () => props.content,
  async () => {
    await nextTick()
    checkOverflow()
  },
)
</script>
