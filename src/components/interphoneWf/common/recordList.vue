<template>
  <div class="record">
    <el-form
      :model="filterOptions"
      class="record-list-form mb-2"
      :label-width="labelWidth"
    >
      <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
        <div class="record-switch">
          <el-checkbox v-model="recordSwitch">
            <span>{{ $t('writeFreq.recordSwitch') }}</span>
          </el-checkbox>
          <el-form-item
            :label="$t('dialog.programmingPwd')"
            :label-width="modelLabelWidth"
          >
            <el-input
              v-model="ioPassword"
              type="password"
              :disabled="!modelValue"
              autocomplete="new-password"
              :maxlength="8"
            />
          </el-form-item>
        </div>
        <el-col :sm="12" :xs="24" class="form-item">
          <el-checkbox
            v-model="filterOptions.queryById"
            :disabled="!modelValue"
          >
            <span>{{ $t('writeFreq.queryById') }}</span>
          </el-checkbox>
          <el-select
            v-model="filterOptions.id"
            :disabled="!modelValue || !filterOptions.queryById"
          >
            <el-option
              v-for="id in idOptions"
              :key="id"
              :value="id"
              :label="id"
            />
          </el-select>
        </el-col>
        <el-col :sm="12" :xs="24" class="form-item">
          <el-checkbox
            v-model="filterOptions.queryByCallDir"
            :disabled="!modelValue"
          >
            <span>{{ $t('writeFreq.queryByCallDir') }}</span>
          </el-checkbox>
          <el-select
            v-model="filterOptions.callDir"
            :disabled="!modelValue || !filterOptions.queryByCallDir"
          >
            <el-option
              v-for="item in callDirOptions"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            />
          </el-select>
        </el-col>
        <el-col :sm="24" class="form-item">
          <el-checkbox
            v-model="filterOptions.queryByTime"
            :disabled="!modelValue"
          >
            <span>{{ $t('writeFreq.queryByTime') }}</span>
          </el-checkbox>
          <el-date-picker
            v-model="filterOptions.queryTime"
            type="daterange"
            range-separator="-"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 1, 1, 23, 59, 59),
            ]"
            :start-placeholder="$t('dialog.startTime')"
            :end-placeholder="$t('dialog.endTime')"
            :disabled="!modelValue || !filterOptions.queryByTime"
            class="grow"
            @change="chooseTime"
          />
        </el-col>
      </el-row>
      <el-row class="btn-group record-box-btn">
        <el-button :disabled="!modelValue || disReadBtn" @click="getRecordList">
          {{ $t('writeFreq.getRecordList') }}
        </el-button>
        <el-button disabled>
          {{ $t('writeFreq.viewRecordingFiles') }}
        </el-button>
        <el-button
          :disabled="downloadRecordList.length === 0 || isReading || isWriting"
          @click="startDownload"
        >
          {{ $t('writeFreq.batchDownload') }}
        </el-button>
      </el-row>
    </el-form>
    <dataTablesVue3
      ref="dataTable"
      :head="dthead"
      :data="recordData"
      :layout="dtLayout"
      scrollY="47vh"
      name="recordTable"
    />
  </div>
</template>

<script>
import dataTablesVue3 from '@/components/common/dataTablesVue3.vue'
import { getLocalTimeString } from '@/utils/time'
import { SupportedLang } from '@/modules/i18n/index.js'
import { formatTime } from '@/utils/time'

// 模拟、组呼、单呼、全呼、临时组、全呼ID、网关ID(单呼)、调度台ID(单呼)
// public static string[] CallString = new string[8] { "FM", "G", "P", "A", "G", "A", "P", "P" };
// 会话ID解析：
// 高4位：常规模式（sdc） 0：无类型（模拟），1：组呼，2：单呼，3：全呼 //都按常规模式处理
//       集群模式（svt） 0：无类型（模拟），1：组呼，2：单呼，7：调度台
// 低28位：解析为号码 callId & 0x00FFFFFF

const CallIdType = {
  0: 'FM',
  1: 'G',
  2: 'P',
  3: 'A',
  7: 'p',
}

export default {
  name: 'RecordList',
  emits: ['update:modelValue', 'downloadRecord', 'getRecordList'],
  components: {
    dataTablesVue3,
  },
  props: {
    // 录音开关
    modelValue: {
      type: Boolean,
      required: true,
    },
    isReading: {
      type: Boolean,
      required: true,
    },
    isWriting: {
      type: Boolean,
      required: true,
    },
    disReadBtn: {
      type: Boolean,
      required: true,
    },
    recordList: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      filterOptions: {
        queryByTime: false,
        queryTime: [],
        queryById: false,
        id: '',
        queryByCallDir: false,
        callDir: 1,
      },
      downloadRecordList: [],
      ioPassword: '',
    }
  },
  computed: {
    dtLayout() {
      return {
        topStart: null,
        topEnd: null,
        bottomStart: 'info',
        bottomEnd: null,
      }
    },
    recordData() {
      if (this.recordList.length === 0) return []
      return this.recordList.filter(item => {
        const byCallDir = this.filterOptions.queryByCallDir
          ? item.recordParse.direction === this.filterOptions.callDir
          : true
        const byId =
          this.filterOptions.queryById && this.filterOptions.id !== ''
            ? item.callId === this.filterOptions.id
            : true
        const startTime = item.startTime * 1000
        const byTime =
          this.filterOptions.queryByTime &&
          this.filterOptions.queryTime.length === 2
            ? startTime >=
                new Date(this.filterOptions.queryTime[0]).getTime() &&
              startTime <= new Date(this.filterOptions.queryTime[1]).getTime()
            : true

        return byCallDir && byId && byTime
      })
    },
    recordSwitch: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      },
    },
    dthead() {
      return [
        {
          title: '<input type="checkbox" class="dt-checkBox-all"></input>',
          data: '',
          width: '40px',
          render: (data, type, row, meta) => {
            return `<input type="checkbox" class="dt-checkBox" data-record-id="${row.recordId}"></input>`
          },
          orderable: false,
        },
        {
          title: this.$t('writeFreq.recordId'),
          data: 'recordId',
          width: '80px',
        },
        {
          title: this.$t('writeFreq.targetId'),
          data: 'callId',
          width: '120px',
          render: (data, type, row, meta) => {
            // 非集群模式都当作常规模式
            const flagByte = data >> 24
            const id = data & 0x00ffffff
            return (CallIdType[flagByte] ?? flagByte) + (id === 0 ? '' : id)
          },
        },
        {
          title: this.$t('writeFreq.callDir'),
          data: 'recordParse',
          width: '120px',
          render: data => {
            return data.direction === 0
              ? this.$t('writeFreq.inbound')
              : this.$t('writeFreq.Exhale')
          },
        },
        {
          title: this.$t('dialog.startTime'),
          data: 'startTime',
          width: '120px',
          render: data => {
            return getLocalTimeString(new Date(data * 1000)) ?? data
          },
        },
        {
          title: this.$t('writeFreq.recordTime'),
          data: '',
          width: '100px',
          render: (data, type, row, meta) => {
            const s = (row.frameCount * 60) / 1000
            return Math.ceil(s) + ' s'
          },
        },
      ]
    },
    locale() {
      return this.$i18n.locale
    },
    isFR() {
      return this.locale === SupportedLang.fr
    },
    isEN() {
      return this.locale === SupportedLang.enUS
    },
    labelWidth() {
      return this.isFR || this.isEN ? '100px' : '80px'
    },
    modelLabelWidth() {
      return this.isFR || this.isEN ? '165px' : '80px'
    },
    idOptions() {
      const ids = new Set()
      for (let i = 0; i < this.recordList.length; i++) {
        ids.add(this.recordList[i].callId)
      }
      return ids
    },
    callDirOptions() {
      // 0:语音呼入 1:语音呼出
      return [
        {
          label: this.$t('writeFreq.inbound'),
          value: 0,
        },
        {
          label: this.$t('writeFreq.Exhale'),
          value: 1,
        },
      ]
    },
  },
  methods: {
    startDownload() {
      this.$emit('downloadRecord', this.downloadRecordList)
    },
    chooseTime(val) {
      this.filterOptions.queryTime =
        val === null ? [] : val.map(item => formatTime(item))
    },
    getRecordList() {
      this.$emit('getRecordList')
    },
    queryDtCheckBoxAllSelector() {
      return this.$el.querySelector(
        '.dt-container .dt-scroll-head .dt-checkBox-all',
      )
    },
    queryDtScrollBodyProxySelector() {
      return this.$el.querySelector(
        '.dt-container .dt-scroll-body .dataTable > tbody',
      )
    },
    queryDtScrollBodyCheckBoxSelector() {
      return this.$el.querySelectorAll(
        '.dt-container .dt-scroll-body .dataTable > tbody .dt-checkBox',
      )
    },
    chooseRecord(isChoose, recordId) {
      const index = this.downloadRecordList.findIndex(
        item => item.recordId === recordId,
      )
      if (isChoose && index === -1) {
        // 勾选
        const item = this.recordData.find(item => item.recordId === recordId)
        if (item) {
          this.downloadRecordList.push(item)
        }
      } else if (!isChoose && index !== -1) {
        // 取消勾选
        this.downloadRecordList.splice(index, 1)
      }

      const checkBoxAll = this.queryDtCheckBoxAllSelector()
      this.downloadRecordList.length === this.recordData.length
        ? (checkBoxAll.checked = true)
        : (checkBoxAll.checked = false)
    },
    syncAllDtCheckBoxChecked(isChoose, checkBoxList) {
      this.downloadRecordList = []
      if (isChoose) {
        for (let i = 0; i < checkBoxList.length; i++) {
          checkBoxList[i].checked = true
          this.chooseRecord(true, +checkBoxList[i].dataset.recordId)
        }
      } else {
        for (let i = 0; i < checkBoxList.length; i++) {
          checkBoxList[i].checked = false
        }
      }
    },
  },
  watch: {
    recordData(/* dataList */) {
      this.$refs.dataTable?.instance?.clear()
      this.$refs.dataTable?.instance?.draw(true)
      this.$nextTick(() => {
        const chooseAllBtn = this.queryDtCheckBoxAllSelector()
        if (!chooseAllBtn) return
        const checkBoxList = this.queryDtScrollBodyCheckBoxSelector()
        this.syncAllDtCheckBoxChecked(chooseAllBtn.checked, checkBoxList)
      })
    },
  },
  mounted() {
    this.$nextTick(() => {
      const chooseAllBtn = this.queryDtCheckBoxAllSelector()
      chooseAllBtn?.addEventListener('change', e => {
        const checkBoxList = this.queryDtScrollBodyCheckBoxSelector()
        this.syncAllDtCheckBoxChecked(e.target.checked, checkBoxList)
      })

      // 代理选中下载的复选框
      const tableBody = this.queryDtScrollBodyProxySelector()
      tableBody?.addEventListener('change', e => {
        if (
          e.target.type !== 'checkbox' &&
          !e.target.classList.contains('dt-checkBox') &&
          e.target.dataset.recordId === undefined
        ) {
          return
        }
        this.chooseRecord(e.target.checked, +e.target.dataset.recordId)
      })
    })
  },
  activated() {
    this.$refs.dataTable.columnsAdjust()
  },
}
</script>

<style lang="scss">
.record {
  height: 100%;

  .record-list-form {
    height: auto !important;

    .record-switch {
      width: 100%;
      padding: 0 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .form-item {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 10px;
    }

    //.btn-group {
    //  display: flex;
    //  justify-content: center;
    //}
  }

  .dataTables_wrapper {
    height: calc(100% - 168px);

    .dataTables_scroll .dataTables_scrollBody {
      min-height: 0vh;
    }
  }
}
</style>
