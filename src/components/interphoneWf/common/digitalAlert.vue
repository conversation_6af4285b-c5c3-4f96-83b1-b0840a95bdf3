<template>
  <el-form
    ref="digitAlarm"
    class="digit-alarm-config-form"
    :model="digitalAlert"
    :rules="digitalAlertRules"
    label-position="top"
  >
    <!--    <el-row :gutter='20' class='no-margin-x'-->
    <!--            type='flex' align='middle'>-->
    <div class="grid grid-cols-1">
      <el-form-item :label="$t('dialog.name')" prop="name">
        <el-input
          v-model="digitalAlert.name"
          :maxlength="16"
          @change="nameChange"
        />
      </el-form-item>
      <el-form-item :label="$t('dataTable.alarmType')">
        <el-select
          v-model="digitalAlert.type"
          :placeholder="$t('dialog.select')"
          :no-match-text="$t('dialog.noMatchText')"
        >
          <el-option
            v-for="(item, i) in digitalAlertTypeList"
            :key="i"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('dialog.mode')">
        <el-select
          v-model="digitalAlert.mode"
          :disabled="forbidAlarm"
          :placeholder="$t('dialog.select')"
          :no-match-text="$t('dialog.noMatchText')"
        >
          <el-option
            v-for="(item, i) in digitalAlertModeList"
            :key="i"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('writeFreq.replyChannel')">
        <el-select
          v-model="digitalAlert.replyChannel"
          :disabled="forbidReplyChannel"
          :placeholder="$t('dialog.select')"
          :no-match-text="$t('dialog.noMatchText')"
        >
          <el-option
            v-for="(item, i) in replyChannelList"
            :key="i"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('writeFreq.notPoliteRetry')">
        <el-input-number
          v-model="digitalAlert.impoliteRetry"
          :disabled="forbidAlarm"
          step-strictly
          :min="1"
          :max="15"
          :step="1"
        />
      </el-form-item>
      <el-form-item :label="$t('writeFreq.politeRetry')">
        <bf-input-number
          v-model="politeRetry"
          :disabled="politeRetryDisabled"
          :formatter="sendCountFormat"
          step-strictly
          :min="0"
          :max="15"
        />
      </el-form-item>
      <el-form-item :label="$t('writeFreq.micActiveTime')">
        <el-input-number
          v-model="digitalAlert.hotMicDuration"
          :disabled="disMicActiveTime"
          step-strictly
          :min="10"
          :max="120"
          :step="10"
        />
      </el-form-item>
      <el-form-item label="" class="checkbox-form-item">
        <el-checkbox v-model="digitalAlert.config.autoSendGps">
          <span v-text="$t('writeFreq.alarmAutoSendGps')" />
        </el-checkbox>
      </el-form-item>
    </div>
  </el-form>
</template>

<script>
import { cloneDeep } from 'lodash'
import validateRules from '@/utils/validateRules'
import { defineAsyncComponent } from 'vue'

const DigitalAlert = {
  id: 0,
  type: 0,
  mode: 0,
  replyChannel: 0xffff,
  impoliteRetry: 15,
  politeRetry: 5,
  hotMicDuration: 10,
  config: {
    autoSendGps: false,
  },
  name: '',
}

export default {
  name: 'DigitalAlert',
  emits: ['update:modelValue', 'initData', 'update:alertId', 'nameChange'],
  props: {
    modelValue: {
      type: Array,
      required: true,
    },
    channelList: {
      type: Array,
      default() {
        return []
      },
    },
    autoSendGps: {
      type: Boolean,
      default: false,
    },
    limit: {
      type: Number,
      default: 32,
    },
    alertId: {
      type: Number,
      default: 0,
    },
    model: {
      type: String,
    },
  },
  data() {
    return {
      digitalAlert: cloneDeep(DigitalAlert),
      digitalAlertList: [],
    }
  },
  methods: {
    nameChange(val) {
      if (!val) {
        this.digitalAlert.name = `${this.$t('writeFreq.system')} ${this.digitalAlert.id + 1}`
      }
      this.$emit('nameChange', this.digitalAlert)
    },
    sendCountFormat(val) {
      if (val === 15) {
        return this.$t('dialog.keepSending')
      }

      return val
    },
    nextId(usedId, limit = this.limit) {
      let id = 0
      while (id < limit) {
        if (!usedId.includes(id)) {
          return id
        }
        id++
      }
      return id
    },
    createDigitalAlertItem() {
      const data = cloneDeep(DigitalAlert)
      const usedId = (this.digitalAlertList || []).map(item => item.id)
      const id = this.nextId(usedId)
      data.id = id
      data.name = `${this.$t('writeFreq.system')} ${id + 1}`
      return data
    },
    addDigitalAlert() {
      if (this.digitalAlertList.length >= this.limit) {
        return
      }
      const data = this.createDigitalAlertItem()
      this.digitalAlertList.push(data)
      this.digitalAlert = data
      this.$emit('update:alertId', data.id)

      return data
    },
    initDigitalAlert() {
      this.digitalAlertList = []
      this.addDigitalAlert()
      this.$nextTick(() => {
        this.$emit('initData')
      })
    },
  },
  watch: {
    modelValue: {
      deep: true,
      handler(val) {
        this.digitalAlertList = val
      },
    },
    digitalAlertList: {
      deep: true,
      handler(val) {
        this.$emit('update:modelValue', val)
      },
    },
    alertId(val) {
      const digitalAlert = this.digitalAlertList.find(item => item.id === val)
      if (digitalAlert) {
        this.digitalAlert = digitalAlert
      }
    },
    noDefaultAddressChannelList: {
      deep: true,
      handler(val) {
        if (val.length > 0 && this.digitalAlert.replyChannel === 0xffff) {
          this.digitalAlert.replyChannel = 0xfffe
        }
      },
    },
  },
  computed: {
    politeRetry: {
      get() {
        let politeRetry = this.digitalAlert.politeRetry
        if (politeRetry === 0xffff) {
          politeRetry = 15
        }
        return politeRetry
      },
      set(val) {
        let politeRetry = val
        if (politeRetry > 14) {
          politeRetry = 0xffff
        }
        this.digitalAlert.politeRetry = politeRetry
      },
    },
    digitalAlertRules() {
      return {
        name: [validateRules.required(['blur'])],
      }
    },
    digitalAlertTypeList() {
      return [
        {
          label: this.$t('writeFreq.forbid'),
          value: 0,
        },
        {
          label: this.$t('dialog.common'),
          value: 1,
        },
        {
          label: this.$t('dialog.silent'),
          value: 2,
        },
        {
          label: this.$t('writeFreq.silenceCarryVoice'),
          value: 3,
        },
      ]
    },
    digitalAlertModeList() {
      return [
        {
          label: this.$t('dialog.emergency'),
          value: 0,
        },
        {
          label: this.$t('writeFreq.emergencyAlarmAndCall'),
          value: 1,
        },
        {
          label: this.$t('writeFreq.emergencyAlarmAndVoice'),
          value: 2,
        },
      ]
    },
    forbidAlarm() {
      return this.digitalAlert.type === 0
    },
    noDefaultAddressChannelList() {
      return this.channelList.filter(channel => {
        // 过滤没有设置发射组的信道
        const defaultAddress =
          channel.subChannelData?.defaultAddress ?? channel.defaultAddress
        if (
          typeof defaultAddress === 'undefined' ||
          defaultAddress === 0xffff
        ) {
          return false
        }
        return true
      })
    },
    replyChannelList() {
      // 没有信道可选时，不可设置回复信道，值为无(0xFFFF)
      if (this.noDefaultAddressChannelList.length === 0) {
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0xffff,
          },
        ]
      }

      const channelList = this.noDefaultAddressChannelList.map(channel => {
        return {
          label: channel.chName,
          value: channel.chId,
        }
      })
      const def = [
        {
          label: this.$t('writeFreq.theSelected'),
          value: 0xfffe,
        },
      ]

      return def.concat(channelList)
    },
    forbidReplyChannel() {
      const result = this.replyChannelList.filter(item => {
        return item.value === 0xffff
      })
      if (result.length > 0) {
        return true
      }

      return this.forbidAlarm
    },
    disMicActiveTime() {
      return this.digitalAlert.mode !== 2 || this.forbidAlarm
    },
    politeRetryDisabled() {
      return (
        (this.digitalAlert.mode === 2 && this.model === 'TD920') ||
        this.forbidAlarm
      )
    },
  },
  components: {
    bfInputNumber: defineAsyncComponent(
      () => import('@/components/common/bfInputNumber'),
    ),
  },
  beforeMount() {
    this.initDigitalAlert()
  },
}
</script>

<style>
.checkbox-form-item {
  margin-top: 33px;
}
</style>
