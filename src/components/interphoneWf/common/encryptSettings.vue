<template>
  <section class="encrypt-settings-container">
    <div class="encrypt-list-header flex justify-space-evenly align-center">
      <div class="flex-item text-center">
        <el-checkbox :model-value="encryptEnable" @change="encryptEnableChange">
          <span v-text="$t('dialog.enable')" />
        </el-checkbox>
      </div>
      <div class="flex-item text-center">
        <el-button
          type="primary"
          class="w-32"
          :disabled="disableAddEncryptItem"
          @click="addEncryptItem"
          v-text="$t('dialog.add')"
        />
      </div>
    </div>
    <el-tabs ref="tabs" v-model="tabName" class="encrypt-list-tabs">
      <el-tab-pane
        v-for="opt in encryptEditOptions"
        :key="opt.name"
        :label="opt.label"
        :name="opt.name"
        class="h-full encrypt-tab-pane"
        :class="[`encrypt-list-${opt.name}`]"
      >
        <el-table
          :data="opt.dataList"
          :empty-text="$t('msgbox.emptyText')"
          :highlight-current-row="encryptEnable"
          :row-class-name="encryptEnable ? 'encrypt-enable-row' : ''"
          :max-height="tableMaxHeight"
          class="encrypt-list__table"
        >
          <el-table-column label="#" type="index" />
          <el-table-column label="ID" min-width="150">
            <template #default="scope">
              <el-input-number
                v-model="scope.row.id"
                :min="1"
                :max="0xff"
                :disabled="!encryptEnable"
                @change="
                  (_, oldValue) =>
                    encryptKeyIdChanged(scope.row, opt.dataList, oldValue)
                "
              />
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('writeFreq.secretKeyName')"
            min-width="150"
          >
            <template #default="scope">
              <el-input
                v-model="scope.row.name"
                :maxlength="16"
                :disabled="!encryptEnable"
                @change="() => encryptKeyNameChanged(scope.row, opt.name)"
              />
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('writeFreq.secretKeyValue')"
            min-width="150"
          >
            <template #default="scope">
              <el-input
                v-model="scope.row.value"
                type="password"
                :maxlength="opt.valueMaxLength"
                :disabled="!encryptEnable"
                @input="() => opt.valueInputEvent(scope.row)"
                @change="() => opt.valueChangeEvent(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column label="#" :width="encryptListActiveWidth">
            <template #default="scope">
              <el-button
                type="danger"
                :disabled="disableDeleteButton(opt.dataList, scope.row)"
                @click="() => opt.deleteEvent(scope.row, scope.$index)"
                v-text="$t('dialog.delete')"
              />
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </section>
</template>

<script>
import { cloneDeep, throttle } from 'lodash'
import { SupportedLang } from '@/modules/i18n'
import { warningBox } from '@/utils/notify'

// 支持的加密列表类型
const EncryptType = {
  XOR: 'XOR',
  ARC4: 'ARC4',
  AES256: 'AES256',
}

const OneEncryptItem = {
  index: 0,
  id: 0,
  value: '',
  name: '',
}

export default {
  name: 'EncryptSettings',
  emits: [
    'update:encryptEnable',
    'update:encryptXORList',
    'update:encryptARC4List',
    'update:encryptAES256List',
  ],
  props: {
    encryptEnable: {
      type: Boolean,
      default: false,
    },
    // 密钥列表上限
    encryptListLimit: {
      type: Number,
      default: 32,
    },

    // 密钥列表
    encryptXORList: {
      type: Array,
      default: () => [],
    },
    encryptARC4List: {
      type: Array,
      default: () => [],
    },
    encryptAES256List: {
      type: Array,
      default: () => [],
    },

    // 添加密钥的函数
    addEncryptARC4Item: {
      type: Function,
    },
    addEncryptAES256Item: {
      type: Function,
    },
    addEncryptXORItem: {
      type: Function,
    },

    // 允许输入的密钥字符
    allowXOR: {
      type: RegExp,
      default: () => /[^0-9A-Za-z!@#$%^&*()]/g,
    },
    allowARC4: {
      type: RegExp,
      default: () => /[^0-9A-Fa-f]/g,
    },
    allowAES256: {
      type: RegExp,
      default: () => /[^0-9A-Fa-f]/g,
    },
  },
  data() {
    return {
      tabName: EncryptType.XOR,
      tableMaxHeight: undefined,
    }
  },
  methods: {
    calcTableMaxHeight() {
      if (!this.$refs.tabs) {
        this.tableMaxHeight = 486
        return
      }

      const offsetHeight = this.$refs.tabs.$el.offsetHeight
      // 45 为tabs标签高度与边距之和
      this.tableMaxHeight = offsetHeight - 45
    },
    disableDeleteButton(list) {
      return !this.encryptEnable || list.length <= 1
    },
    updateEncryptList(prop, list) {
      this.$emit(`update:${prop}`, list)
    },
    encryptEnableChange(value) {
      this.$emit('update:encryptEnable', value)
    },
    encryptKeyNamePrefix(typeName) {
      switch (typeName) {
        case EncryptType.ARC4:
          return this.$t('writeFreq.arc4List')
        case EncryptType.AES256:
          return this.$t('writeFreq.aes256List')
        case EncryptType.XOR:
        default:
          return this.$t('writeFreq.encryptedList')
      }
    },
    encryptKeyNameChanged(encryptItem, suffix = '') {
      if (!encryptItem) {
        return
      }

      if (!encryptItem.name) {
        encryptItem.name =
          this.encryptKeyNamePrefix(suffix) + ' ' + encryptItem.id
      }
    },
    /**
     * 密钥ID变更事件，检测ID是否重复
     * @param row {OneEncryptItem} 密钥ID
     * @param list {OneEncryptItem[]} 密钥列表
     * @param oldValue {number} 旧的密钥ID
     */
    encryptKeyIdChanged(row, list, oldValue) {
      // 找到其他行数据相同ID的
      const index = list.findIndex(
        item => item.id === row.id && item.index !== row.index,
      )
      if (index === -1) {
        return
      }
      warningBox(this.$t('writeFreq.idAlreadyExits'))
        .then(() => {})
        .catch(() => {})
        .finally(() => {
          this.$nextTick(() => {
            row.id = oldValue
          })
        })
    },
    // 检测加密密钥输入的字符是否符合规则
    replaceInvalidByte(value, reg) {
      // 不是正则或字符串，则返回原数据
      if (reg instanceof RegExp || typeof reg === 'string') {
        return value.replace(reg, '')
      }

      return value
    },
    encryptKeyValueInput(encryptItem) {
      if (!encryptItem) {
        return
      }
      // 替换非法字符
      encryptItem.value = this.replaceInvalidByte(
        encryptItem.value,
        this.allowXOR,
      )
    },
    encryptARC4ValueInput(encryptItem) {
      if (!encryptItem) {
        return
      }
      // 替换非法字符
      encryptItem.value = this.replaceInvalidByte(
        encryptItem.value,
        this.allowARC4,
      )
    },
    encryptARC4ValueChanged(encryptItem) {
      if (!encryptItem) {
        return
      }
      // 密钥不存在，自动生成新密钥
      if (!encryptItem.value) {
        encryptItem.value = this.genSpecifyRandom(10, 16).toUpperCase()
        return
      }

      encryptItem.value = this.autoFillBytes(
        encryptItem.value,
        10,
        'F',
        true,
      ).toUpperCase()
    },
    deleteEncryptARC4Item(row, index) {
      const encryptList = [...this.encryptARC4List]
      if (encryptList.includes(row)) {
        encryptList.splice(index, 1)
        for (let i = index; i < encryptList.length; i++) {
          encryptList[i].index = i
        }
      }
      this.updateEncryptList('encryptARC4List', encryptList)
    },
    encryptAES256ValueInput(encryptItem) {
      if (!encryptItem) {
        return
      }
      // 替换非法字符
      encryptItem.value = this.replaceInvalidByte(
        encryptItem.value,
        this.allowAES256,
      )
    },
    encryptAES256ValueChanged(encryptItem) {
      if (!encryptItem) {
        return
      }
      // 密钥不存在，自动生成新密钥
      if (!encryptItem.value) {
        encryptItem.value = this.genSpecifyRandom(64, 16).toUpperCase()
        return
      }

      encryptItem.value = this.autoFillBytes(
        encryptItem.value,
        64,
        'F',
        true,
      ).toUpperCase()
    },
    deleteEncryptAES256Item(row, index) {
      const encryptList = [...this.encryptAES256List]
      if (encryptList.includes(row)) {
        encryptList.splice(index, 1)
        for (let i = index; i < encryptList.length; i++) {
          encryptList[i].index = i
        }
      }
      this.updateEncryptList('encryptAES256List', encryptList)
    },
    // 生成10位的16进制字符串
    genRandom(radix = 16) {
      return Math.random().toString(radix).slice(2, 12)
    },
    // Generate a string of specified length
    genSpecifyRandom(len = 10, radix = 16) {
      let key = this.genRandom(radix)
      while (key.length < len) {
        key += this.genRandom(radix)
      }
      if (key.length > len) {
        key = key.slice(0, len)
      }

      return key
    },
    // 自动补全指定长度的字符串
    autoFillBytes(data, len, byte = '0', reverse = false) {
      if (data.length < len) {
        return reverse ? data.padEnd(len, byte) : data.padStart(len, byte)
      }
      return data
    },
    encryptKeyValueChanged(encryptItem) {
      if (!encryptItem) {
        return
      }

      // 密钥不存在，自动生成新密钥
      if (!encryptItem.value) {
        encryptItem.value = this.genSpecifyRandom(10, 32).toUpperCase()
        return
      }

      encryptItem.value = this.autoFillBytes(encryptItem.value, 10, 'F', true)
    },
    // 删除密钥
    deleteEncryptItem(row, index) {
      const encryptList = [...this.encryptXORList]
      if (encryptList.includes(row)) {
        encryptList.splice(index, 1)
        for (let i = index; i < encryptList.length; i++) {
          encryptList[i].index = i
        }
      }
      // 向父组件传递更新的密钥列表
      this.updateEncryptList('encryptXORList', encryptList)
    },

    nextId(usedId, limit = 32, start = 0) {
      let id = start
      while (id < limit) {
        if (!usedId.includes(id)) {
          return id
        }
        id++
      }
      return id
    },

    createARC4Key() {
      const oneARC4 = cloneDeep(OneEncryptItem)
      const usedId = (this.encryptARC4List || []).map(item => item.id)
      const index = this.encryptARC4List.length
      const id = this.nextId(usedId, this.encryptListLimit, 1)

      oneARC4.index = index
      oneARC4.id = id
      oneARC4.name = this.encryptKeyNamePrefix(EncryptType.ARC4) + ' ' + id
      oneARC4.value = this.genSpecifyRandom(10, 16).toUpperCase()

      return oneARC4
    },
    localAddEncryptARC4Item() {
      if (this.encryptARC4List.length >= this.encryptListLimit) {
        return
      }
      this.updateEncryptList('encryptARC4List', [
        ...this.encryptARC4List,
        this.createARC4Key(),
      ])
    },
    createAES256Key() {
      const oneAes256 = cloneDeep(OneEncryptItem)
      const usedId = (this.encryptAES256List || []).map(item => item.id)
      const index = this.encryptAES256List.length
      const id = this.nextId(usedId, this.encryptListLimit, 1)

      oneAes256.index = index
      oneAes256.id = id
      oneAes256.name = this.encryptKeyNamePrefix(EncryptType.AES256) + ' ' + id
      oneAes256.value = this.genSpecifyRandom(64, 16).toUpperCase()

      return oneAes256
    },
    localAddEncryptAES256Item() {
      if (this.encryptAES256List.length >= this.encryptListLimit) {
        return
      }
      this.updateEncryptList('encryptAES256List', [
        ...this.encryptAES256List,
        this.createAES256Key(),
      ])
    },
    createEncryptXORItem() {
      const oneEncryptOption = cloneDeep(OneEncryptItem)
      const usedId = (this.encryptXORList || []).map(item => item.id)
      const index = this.encryptXORList.length
      const id = this.nextId(usedId, this.encryptListLimit, 1)

      oneEncryptOption.index = index
      oneEncryptOption.id = id
      oneEncryptOption.name =
        this.encryptKeyNamePrefix(EncryptType.XOR) + ' ' + id
      oneEncryptOption.value = this.genSpecifyRandom(10, 16).toUpperCase()

      return oneEncryptOption
    },
    localAddEncryptXORItem() {
      if (this.encryptXORList.length >= this.encryptListLimit) {
        return
      }
      this.updateEncryptList('encryptXORList', [
        ...this.encryptXORList,
        this.createEncryptXORItem(),
      ])
    },
  },
  computed: {
    locale() {
      return this.$i18n.locale
    },
    isCN() {
      return this.locale === SupportedLang.zhCN
    },
    isEN() {
      return this.locale === SupportedLang.enUS
    },
    isFR() {
      return this.locale === SupportedLang.fr
    },

    encryptListActiveWidth() {
      return this.isFR ? '120px' : '100px'
    },
    tabLabels() {
      return {
        [EncryptType.XOR]: this.$t('writeFreq.encryption'),
        [EncryptType.ARC4]: this.$t('writeFreq.advancedEncryptionARC4'),
        [EncryptType.AES256]: this.$t('writeFreq.advancedEncryptionAES256'),
      }
    },
    encryptDataList() {
      return {
        [EncryptType.XOR]: this.encryptXORList,
        [EncryptType.ARC4]: this.encryptARC4List,
        [EncryptType.AES256]: this.encryptAES256List,
      }
    },
    encryptValueInputEvents() {
      return {
        [EncryptType.XOR]: this.encryptKeyValueInput,
        [EncryptType.ARC4]: this.encryptARC4ValueInput,
        [EncryptType.AES256]: this.encryptAES256ValueInput,
      }
    },
    encryptValueChangeEvents() {
      return {
        [EncryptType.XOR]: this.encryptKeyValueChanged,
        [EncryptType.ARC4]: this.encryptARC4ValueChanged,
        [EncryptType.AES256]: this.encryptAES256ValueChanged,
      }
    },
    deleteEncryptValueEvents() {
      return {
        [EncryptType.XOR]: this.deleteEncryptItem,
        [EncryptType.ARC4]: this.deleteEncryptARC4Item,
        [EncryptType.AES256]: this.deleteEncryptAES256Item,
      }
    },
    encryptValueMaxLength() {
      return {
        [EncryptType.XOR]: 10,
        [EncryptType.ARC4]: 10,
        [EncryptType.AES256]: 64,
      }
    },
    encryptEditOptions() {
      return Array.from(Object.entries(EncryptType).values()).map(
        ([_, value]) => {
          return {
            name: value,
            dataList: this.encryptDataList[value],
            label: this.tabLabels[value],
            valueInputEvent: this.encryptValueInputEvents[value],
            valueChangeEvent: this.encryptValueChangeEvents[value],
            deleteEvent: this.deleteEncryptValueEvents[value],
            valueMaxLength: this.encryptValueMaxLength[value],
          }
        },
      )
    },

    addEncryptItem() {
      switch (this.tabName) {
        case EncryptType.ARC4:
          return this.addEncryptARC4Item ?? this.localAddEncryptARC4Item
        case EncryptType.AES256:
          return this.addEncryptAES256Item ?? this.localAddEncryptAES256Item
        case EncryptType.XOR:
        default:
          return this.addEncryptXORItem ?? this.localAddEncryptXORItem
      }
    },
    disableAddEncryptItem() {
      // 没有开启加密功能，不能添加新的密钥
      if (!this.encryptEnable) {
        return true
      }

      switch (this.tabName) {
        case EncryptType.ARC4:
          return this.encryptARC4List.length >= this.encryptListLimit
        case EncryptType.AES256:
          return this.encryptAES256List.length >= this.encryptListLimit
        case EncryptType.XOR:
        default:
          return this.encryptXORList.length >= this.encryptListLimit
      }
    },
  },
  beforeMount() {
    if (this.encryptXORList.length === 0) {
      ;(this.addEncryptXORItem ?? this.localAddEncryptXORItem)()
    }
    if (this.encryptARC4List.length === 0) {
      ;(this.addEncryptARC4Item ?? this.localAddEncryptARC4Item)()
    }
    if (this.encryptAES256List.length === 0) {
      ;(this.addEncryptAES256Item ?? this.localAddEncryptAES256Item)()
    }
  },
  mounted() {
    this.calcTableMaxHeight = throttle(this.calcTableMaxHeight, 300)
    this.calcTableMaxHeight()
    window.addEventListener('resize', this.calcTableMaxHeight)
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.calcTableMaxHeight)
  },
}
</script>

<style scoped lang="scss">
.encrypt-settings-container {
  width: 100%;
  height: 100%;

  .el-tabs.encrypt-list-tabs {
    height: calc(100% - 40px);
  }
}
</style>
