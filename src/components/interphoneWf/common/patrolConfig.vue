<template>
  <el-form
    ref="patrolSystemConfigure"
    class="system-configure-form"
    :model="patrolConfig"
    label-position="top"
    :rules="patrolConfigRules"
  >
    <el-form-item :label="$t('dialog.responseTimeout1')">
      <el-input-number
        v-model="patrolConfig.replyTimeout"
        step-strictly
        :min="2500"
        :max="10000"
        :step="500"
      />
    </el-form-item>
    <el-form-item
      v-if="timeZone"
      :label="$t('dialog.timeZone')"
      class="timezone-wrapper"
    >
      <el-input
        :value="timeZoneLabel"
        class="input-with-select timezone-label"
        disabled
      />
      <el-select
        v-model="timeZoneOffset"
        class="timezone-value"
        :placeholder="$t('dialog.timeZone')"
        filterable
        :no-match-text="$t('dialog.noMatchText')"
      >
        <el-option
          v-for="(item, i) in timeZoneList"
          :key="i"
          class="timezonedrop"
          :label="isEn ? item.text : item.ch"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
  </el-form>
</template>

<script>
import { cloneDeep } from 'lodash'
import tz from '@/utils/timezones'
import validateRules from '@/utils/validateRules'

const PatrolConfig = {
  replyTimeout: 5000,
  timezoneHour: 0,
  timezoneMinute: 0,
}

export default {
  name: 'PatrolConfig',
  emits: ['update:modelValue'],
  props: {
    modelValue: {
      type: Object,
      required: true,
    },
    timeZone: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      patrolConfig: cloneDeep(PatrolConfig),
      timeZoneOffset: new Date().getTimezoneOffset() / -60,
    }
  },
  methods: {
    getTimeZoneOffset(TimeZoneOffset = new Date().getTimezoneOffset()) {
      return {
        timezoneHour: (TimeZoneOffset / 60) | 0,
        timezoneMinute: Math.abs(TimeZoneOffset) % 60,
      }
    },
    setTimeZoneOffset(hour, minute) {
      const sign = hour < 0 ? -1 : 1
      this.timeZoneOffset = (Math.abs(hour) + minute / 60) * sign
    },
    initData() {
      this.patrolConfig = cloneDeep(PatrolConfig)
    },
  },
  watch: {
    modelValue: {
      deep: true,
      immediate: true,
      handler(val) {
        this.patrolConfig = Object.assign(this.patrolConfig, val)
      },
    },
    patrolConfig: {
      deep: true,
      handler(val) {
        this.$emit('update:modelValue', val)
      },
    },
    timeZoneOffset: {
      immediate: true,
      handler(val) {
        const timeZoneOffset = this.getTimeZoneOffset(val * 60)
        this.patrolConfig = Object.assign(this.patrolConfig, timeZoneOffset)
      },
    },
    'patrolConfig.timezoneHour'(val) {
      this.setTimeZoneOffset(val, this.patrolConfig.timezoneMinute)
    },
    'patrolConfig.timezoneMinute'(val) {
      this.setTimeZoneOffset(this.patrolConfig.timezoneHour, val)
    },
  },
  computed: {
    isEn() {
      return this.$i18n.locale === 'en'
    },
    timeZoneList() {
      return tz
    },
    patrolConfigRules() {
      return {
        timeZone: [
          validateRules.required(),
          validateRules.range('blur', -12, 12, `${-12}~${12}`),
        ],
      }
    },
    timeZoneLabel() {
      const sign = this.patrolConfig.timezoneHour < 0 ? '-' : ''
      const hour = Math.abs(this.patrolConfig.timezoneHour)
        .toString()
        .padStart(2, '0')
      const minute = this.patrolConfig.timezoneMinute
        .toString()
        .padStart(2, '0')
      return `${sign}${hour}:${minute}`
    },
  },
}
</script>

<style lang="scss">
.el-form-item.timezone-wrapper {
  .el-form-item__content {
    display: flex;
    flex-wrap: nowrap;

    .timezone-label {
      flex: auto;
      max-width: 120px;

      .el-input__wrapper {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
    }

    .timezone-value {
      flex: auto;

      .el-select__wrapper {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
    }
  }
}
</style>
