<template>
  <div
    class="mt-3 w-full flex-none flex flex-wrap gap-3 justify-center writer-freq-footer"
    :class="containerClass"
  >
    <el-button
      type="primary"
      class="w-32 new-config"
      :size="btnSize"
      :disabled="isReadingOrWriting"
      @click="newConfig"
      v-text="$t('dialog.new')"
    />
    <el-button
      type="primary"
      class="w-32 read-config"
      :size="btnSize"
      :disabled="disableRead"
      @click="readConfig"
      v-text="$t('dialog.readData')"
    />
    <el-button
      type="primary"
      class="w-32 write-config"
      :size="btnSize"
      :disabled="disableWrite"
      @click="writeConfig"
      v-text="$t('dialog.writeIn')"
    />
    <el-button
      type="primary"
      class="w-32 export-config"
      :size="btnSize"
      :disabled="isReading"
      @click="exportConfig"
      v-text="$t('dialog.exportConfig')"
    />
    <el-upload
      class="import-config-upload"
      action=""
      :show-file-list="false"
      :before-upload="importConfig"
    >
      <el-button
        type="primary"
        class="w-32 import-config"
        :size="btnSize"
        :disabled="isReadingOrWriting"
        v-text="$t('dialog.importConfig')"
      />
    </el-upload>
  </div>
</template>

<script>
import bfNotify, { warningBox } from '@/utils/notify'
import { SupportedLang } from '@/modules/i18n'

export default {
  name: 'WriteFreqFooter',
  emits: [
    'new-config',
    'read-config',
    'write-config',
    'export-config',
    'import-config',
  ],
  props: {
    isReading: {
      type: Boolean,
      default: false,
    },
    isWriting: {
      type: Boolean,
      default: false,
    },
    disableRead: {
      type: Boolean,
      default: false,
    },
    disableWrite: {
      type: Boolean,
      default: true,
    },
    btnSize: {
      type: String,
      default: 'default',
    },
  },
  data() {
    return {}
  },
  methods: {
    newConfig() {
      this.$emit('new-config')
    },
    readConfig() {
      this.$emit('read-config')
    },
    writeConfig() {
      this.$emit('write-config')
    },
    exportConfig() {
      this.$emit('export-config')
    },
    importFile(file) {
      return new Promise((resolve, reject) => {
        // 过滤非json文件
        if (file.type !== 'application/json') {
          warningBox(this.$t('msgbox.importValidJsonFile'), 'error')
          bfglob.console.error(
            'importConfig error: Please import a valid json format file ',
            file.type,
          )
          reject()
          return false
        }

        // 使用 FileReader 读取json内容
        const reader = new FileReader()
        reader.onload = () => {
          // 文件内容可能不是标准json数据，需要使用 try...catch(e)... 捕获异常
          try {
            resolve(JSON.parse(reader.result))
          } catch (e) {
            bfglob.console.error('importConfig error:', e)
            reject()
          }
        }
        // 以文本方法读取文件内容
        reader.readAsText(file)
      })
    },
    // 若返回 false 或者返回 Promise 且被 reject，则停止上传。
    importConfig(file) {
      this.importFile(file)
        .then(data => {
          this.newConfig()
          this.$emit('import-config', data)
        })
        .catch(() => {
          bfNotify.warningBox(this.$t('msgbox.importValidJsonFile'), 'error')
        })
      return false
    },
  },
  computed: {
    isReadingOrWriting() {
      return this.isWriting || this.isReading
    },
    isFR() {
      return this.$i18n.locale === SupportedLang.fr
    },
    containerClass() {
      return {
        ['locale-' + this.$i18n.locale]: !!this.$i18n.locale,
      }
    },
  },
}
</script>

<style scoped lang="scss">
.writer-freq-footer {
  .el-button + .el-button {
    margin-left: 0;
  }

  &.locale-fr {
    .import-config,
    .export-config {
      width: 10rem;
    }
  }
}
</style>
