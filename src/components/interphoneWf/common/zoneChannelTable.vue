<template>
  <el-table
    :data="getViewData()"
    border
    stripe
    highlight-current-row
    height="calc(100% - 1px)"
    style="width: 100%"
    @row-dblclick="rowDblclick"
  >
    <el-table-column type="index" width="60px" />
    <el-table-column :label="$t('dialog.chName')" prop="chName" sortable />
    <el-table-column
      v-if="columnVisible.rxFreq"
      :label="$t('dialog.rxFrequency')"
      sortable
    >
      <template #default="scope">
        <span v-text="frequencyHz2Mhz(scope.row.rxFreq)" />
      </template>
    </el-table-column>
    <el-table-column
      v-if="columnVisible.txFreq"
      :label="$t('dialog.txFrequency')"
      sortable
    >
      <template #default="scope">
        <span v-text="frequencyHz2Mhz(scope.row.txFreq)" />
      </template>
    </el-table-column>
    <el-table-column
      v-if="columnVisible.networking"
      :label="$t('writeFreq.networking')"
    >
      <template #default="scope">
        <el-checkbox v-model="scope.row.baseSettings.networking" disabled />
      </template>
    </el-table-column>
    <el-table-column v-if="columnVisible.chType" :label="$t('dialog.chType')">
      <template #default="scope">
        <span v-text="chTypeEnum[scope.row.chType]" />
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import bfutil from '@/utils/bfutil'

export default {
  name: 'ZoneChannelTable',
  emits: ['rowDblclick'],
  props: {
    channels: {
      type: Array,
      required: true,
    },
    zoneData: {
      type: Object,
      default: () => undefined,
    },
    visColumns: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {}
  },
  methods: {
    getViewData() {
      const list = []
      if (!this.zoneData) {
        return list
      }
      for (let i = 0; i < this.zoneData.list.length; i++) {
        const chId = this.zoneData.list[i]
        const channel = this.channelsIndex[chId]
        if (channel) {
          list.push(channel)
        }
      }
      return list
    },
    rowDblclick(row, column, event) {
      this.$emit('rowDblclick', row, column, event)
    },
    frequencyHz2Mhz: bfutil.frequencyHz2Mhz,
  },
  computed: {
    chTypeEnum() {
      return this.chTypeList
        .map(item => {
          return { [item.value]: item.label }
        })
        .reduce((p, c) => Object.assign(p, c), {})
    },
    chTypeList() {
      return [
        { label: this.$t('writeFreq.digitalChannel'), value: 0 },
        { label: this.$t('writeFreq.analogChannel'), value: 1 },
        { label: this.$t('writeFreq.digitalAnalogChannel'), value: 2 },
      ]
    },
    columnVisible() {
      return {
        rxFreq: true,
        txFreq: true,
        networking: false,
        chType: false,
        ...this.visColumns,
      }
    },
    channelsIndex() {
      return this.channels
        .map(data => {
          return { [data.chId]: data }
        })
        .reduce((p, c) => {
          return Object.assign(p, c)
        }, {})
    },
  },
  watch: {
    dataId(val) {
      console.log('dataId:%d', val)
    },
  },
}
</script>

<style scoped></style>
