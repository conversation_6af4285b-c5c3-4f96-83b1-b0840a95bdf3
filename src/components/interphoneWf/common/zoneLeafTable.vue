<template>
  <el-table
    :data="getViewData()"
    border
    stripe
    highlight-current-row
    style="width: 100%"
    @row-dblclick="rowDblclick"
  >
    <el-table-column type="index" width="60px" />
    <el-table-column :label="$t('dialog.chName')" prop="chName" sortable />
    <el-table-column :label="$t('dialog.rxFrequency')" sortable>
      <template #default="scope">
        <span v-text="frequencyHz2Mhz(scope.row.rxFreq)" />
      </template>
    </el-table-column>
    <el-table-column :label="$t('dialog.txFrequency')" sortable>
      <template #default="scope">
        <span v-text="frequencyHz2Mhz(scope.row.txFreq)" />
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import bfutil from '@/utils/bfutil'

export default {
  name: 'ZoneLeafTable',
  emits: ['update:modelValue', 'rowDblclick'],
  props: {
    modelValue: {
      type: Array,
      required: true,
    },
    limit: {
      type: Number,
      default: 64,
    },
    parentZone: {
      type: Object,
      default() {
        return { origin: {}, usedList: [] }
      },
    },
    getDefaultChannel: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      dataList: [],
    }
  },
  methods: {
    getViewData() {
      const list = []
      for (let i = 0; i < this.parentZone.usedList.length; i++) {
        const chId = this.parentZone.usedList[i]
        const channel = this.channelsIndex[chId]
        if (channel) {
          list.push(channel)
        }
      }
      return list
    },
    rowDblclick(row, column, event) {
      this.$emit('rowDblclick', row, column, event)
    },
    frequencyHz2Mhz: bfutil.frequencyHz2Mhz,

    nextId(limit = this.limit) {
      const usedId = (this.dataList || []).map(item => item.chId)
      let id = 0
      while (id < limit) {
        if (!usedId.includes(id)) {
          return id
        }
        id++
      }
      return id
    },
    newOneChannel(type) {
      const data = this.getDefaultChannel(type)
      const id = this.nextId()
      data.chId = id
      data.chName = `${this.$t('dialog.channel')} ${id + 1}`
      return data
    },
    addOneChannel(type = 0) {
      if (this.dataList.length >= this.limit) {
        return
      }
      const data = this.newOneChannel(type)

      this.dataList.push(data)
      return data
    },
    initDataList() {
      this.dataList = []
    },
  },
  computed: {
    channelsIndex() {
      return this.dataList
        .map(data => {
          return { [data.chId]: data }
        })
        .reduce((p, c) => {
          return Object.assign(p, c)
        }, {})
    },
  },
  watch: {
    modelValue: {
      deep: true,
      handler(val) {
        this.dataList = val
      },
    },
    dataList: {
      deep: true,
      handler(val) {
        this.$emit('update:modelValue', val)
      },
    },
    dataId(val) {
      console.log('dataId:%d', val)
    },
  },
}
</script>

<style scoped></style>
