<template>
  <div class="writer-frequency-wrap">
    <selectDevice
      v-model="selectedDeviceDmrId"
      :disabled="isReading || isWriting"
    />
    <section class="TD910-layout">
      <TableTree
        :ref="menuTreeId"
        class="TD910-menu-tree"
        :treeId="menuTreeId"
        :filter="false"
        :contextmenuOption="menuTreeContextmenuOption"
        :option="menuTreeOpts"
        @loaded="treeLoaded"
      />
      <main class="TD910-content">
        <el-card
          v-show="showDeviceInfo"
          shadow="never"
          class="write-freq-component deviceInfo-container"
        >
          <deviceInfo
            ref="deviceWriteInfo"
            v-model="deviceWriteInfo"
            :model="deviceModel"
            hasUiVersion
          />
        </el-card>
        <el-card
          v-show="showGeneralSettings"
          shadow="never"
          class="write-freq-component general-settings-container"
        >
          <el-form
            ref="generalSettings"
            class="general-settings-form"
            :model="generalSettings"
            label-width="100px"
            label-position="top"
            :rules="generalSettingsRules"
          >
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-col :xs="24" :sm="12">
                <el-form-item
                  :label="$t('dialog.terminalName')"
                  prop="deviceName"
                >
                  <el-input
                    v-model="generalSettings.deviceName"
                    :maxlength="16"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item
                  :label="$t('dialog.powerOnPwd')"
                  prop="powerOnPwd"
                >
                  <el-input
                    v-model="generalSettings.powerOnPwd"
                    type="password"
                    :maxlength="6"
                    @input="fixPowerOnValue"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.passwordErrorNumber')">
                  <bf-input-number
                    v-model="generalSettings.powerOnPwdErrLimit"
                    :disabled="generalSettings.powerOnPwd.length !== 6"
                    step-strictly
                    :min="0"
                    :max="10"
                    :step="1"
                    :formatter="
                      v => {
                        return v === 0 ? $t('writeFreq.unlimited2') : v
                      }
                    "
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.chDisplayMode')">
                  <el-select
                    v-model="generalSettings.baseSettings.chDisplayMode"
                    :placeholder="$t('dialog.select')"
                    filterable
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in chDisplayModeList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.showContactContent')">
                  <el-select
                    v-model="generalSettings.concatSettings.showContactContent"
                    :placeholder="$t('dialog.select')"
                    filterable
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in contactContentList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="generalSettings.concatSettings.showStrangeNumber"
                    :disabled="disableStrangeNumber"
                  >
                    <span v-text="$t('writeFreq.showStrangeNumber')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="generalSettings.baseSettings.allowErasing"
                  >
                    <span v-text="$t('writeFreq.allowErasingDevice')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="generalSettings.baseSettings.menuOff">
                    <span v-text="$t('dialog.closeMenuButton')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="generalSettings.baseSettings.bootInterface"
                  >
                    <span v-text="$t('dialog.bootInterface')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.powerSavingMode')">
                  <el-select
                    v-model="generalSettings.baseSettings.savePowerMode"
                    :placeholder="$t('dialog.select')"
                    filterable
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in savePowerModeList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.saveModeOnTimeDelay')">
                  <el-input-number
                    v-model="generalSettings.powerSavingModeDelayTime"
                    step-strictly
                    :min="5"
                    :max="60"
                    :step="5"
                    :disabled="notInSavePowerMode"
                  />
                </el-form-item>
              </el-col>
              <el-col v-if="!DeviceNoLocale" :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.languageType')">
                  <el-select
                    v-model="generalSettings.locale"
                    :placeholder="$t('dialog.select')"
                    filterable
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in localeList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.voiceLevel')">
                  <bf-input-number
                    v-model="generalSettings.soundCtrlLevel"
                    step-strictly
                    :min="0"
                    :max="8"
                    :step="1"
                    :formatter="
                      v => {
                        return v === 0 ? $t('writeFreq.off') : v
                      }
                    "
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.voiceDelay')">
                  <el-input-number
                    v-model="generalSettings.soundCtrlDelay"
                    step-strictly
                    :min="500"
                    :max="10000"
                    :step="500"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item
                  :label="$t('dialog.receiveLowPowerPromptInterval')"
                  prop="powerInfoAlert"
                >
                  <el-input-number
                    v-model="generalSettings.powerInfoAlert"
                    step-strictly
                    :min="0"
                    :max="635"
                    :step="5"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.presetChannel')" />
              </el-divider>

              <el-col :xs="24">
                <preSetChannel
                  v-model="generalSettings.preSetChannel"
                  :channels="channelDataList"
                  :zones="zoneDataList"
                  :zoneIndex="zoneDataIndex"
                />
              </el-col>
            </el-row>

            <td930-time-zone ref="timezone" v-model="timeZone" />
          </el-form>
        </el-card>
        <el-card
          v-if="showUISettings"
          shadow="never"
          class="write-freq-component menu-settings-container"
        >
          <el-form
            ref="uiSettings"
            class="ui-settings-form"
            :model="uiSettings"
            label-width="120px"
            label-position="top"
          >
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.voiceBroadcastSettings')" />
              </el-divider>

              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.chooseAnnouncer')">
                  <el-select
                    v-model="uiSettings.chooseAnnouncer"
                    :placeholder="$t('dialog.select')"
                    filterable
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in chooseAnnouncerList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.speechRate')">
                  <el-input-number
                    v-model="uiSettings.speechRate"
                    step-strictly
                    :min="1"
                    :max="10"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.ringToneSettings')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox
                    v-model="uiSettings.callingSoundSettings.muteAll"
                  >
                    <span v-text="$t('dialog.muteAll')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox
                    v-model="
                      uiSettings.promptSoundSettings.channelBroadcastTone
                    "
                    :disabled="muteAll"
                  >
                    <span v-text="$t('writeFreq.channelBroadcastSound')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item
                  :label="$t('writeFreq.callOutTone')"
                  class="tone-volume-form-item"
                >
                  <el-select
                    v-model="uiSettings.callingSoundSettings.callingOutTone"
                    :placeholder="$t('dialog.select')"
                    filterable
                    :disabled="muteAll"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in callingOutToneList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                  <bf-input-number
                    v-model="uiSettings.callingOutToneVolume"
                    :formatter="formatUiSettingVolume"
                    step-strictly
                    :min="0"
                    :max="8"
                    :step="1"
                    :disabled="
                      muteAll ||
                      uiSettings.callingSoundSettings.callingOutTone === 0
                    "
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item
                  :label="$t('writeFreq.voiceEndTone')"
                  class="tone-volume-form-item"
                >
                  <el-checkbox
                    v-model="uiSettings.callingSoundSettings.voiceEndTone"
                    :disabled="muteAll"
                  />
                  <bf-input-number
                    v-model="uiSettings.voiceEndToneVolume"
                    :formatter="formatUiSettingVolume"
                    step-strictly
                    :min="0"
                    :max="8"
                    :step="1"
                    :disabled="
                      muteAll || !uiSettings.callingSoundSettings.voiceEndTone
                    "
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item
                  :label="$t('writeFreq.singleCallTone')"
                  class="tone-volume-form-item"
                >
                  <el-checkbox
                    v-model="uiSettings.callingSoundSettings.singleCallTone"
                    :disabled="muteAll"
                  />
                  <bf-input-number
                    v-model="uiSettings.singleCallToneVolume"
                    :formatter="formatUiSettingVolume"
                    step-strictly
                    :min="0"
                    :max="8"
                    :step="1"
                    :disabled="
                      muteAll || !uiSettings.callingSoundSettings.singleCallTone
                    "
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item
                  :label="$t('writeFreq.groupCallTone')"
                  class="tone-volume-form-item"
                >
                  <el-checkbox
                    v-model="uiSettings.callingSoundSettings.groupCallTone"
                    :disabled="muteAll"
                  />
                  <bf-input-number
                    v-model="uiSettings.groupCallToneVolume"
                    :formatter="formatUiSettingVolume"
                    step-strictly
                    :min="0"
                    :max="8"
                    :step="1"
                    :disabled="
                      muteAll || !uiSettings.callingSoundSettings.groupCallTone
                    "
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item
                  :label="$t('writeFreq.callingEndTone')"
                  class="tone-volume-form-item"
                >
                  <el-checkbox
                    v-model="uiSettings.callingSoundSettings.callingEndTone"
                    :disabled="muteAll"
                  />
                  <bf-input-number
                    v-model="uiSettings.callingEndToneVolume"
                    :formatter="formatUiSettingVolume"
                    step-strictly
                    :min="0"
                    :max="8"
                    :step="1"
                    :disabled="
                      muteAll || !uiSettings.callingSoundSettings.callingEndTone
                    "
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item
                  :label="$t('writeFreq.messageTone')"
                  class="tone-volume-form-item"
                >
                  <el-checkbox
                    v-model="uiSettings.callingSoundSettings.messageTone"
                    :disabled="muteAll"
                  />
                  <bf-input-number
                    v-model="uiSettings.messageToneVolume"
                    :formatter="formatUiSettingVolume"
                    step-strictly
                    :min="0"
                    :max="8"
                    :step="1"
                    :disabled="
                      muteAll || !uiSettings.callingSoundSettings.messageTone
                    "
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item
                  :label="$t('writeFreq.callPrompt')"
                  class="tone-volume-form-item"
                >
                  <el-checkbox
                    v-model="uiSettings.promptSoundSettings.callPrompt"
                    :disabled="muteAll"
                  />
                  <bf-input-number
                    v-model="uiSettings.callPromptVolume"
                    :formatter="formatUiSettingVolume"
                    step-strictly
                    :min="0"
                    :max="8"
                    :step="1"
                    :disabled="
                      muteAll || !uiSettings.promptSoundSettings.callPrompt
                    "
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item
                  :label="$t('writeFreq.priorityChannelTone')"
                  class="tone-volume-form-item"
                >
                  <el-checkbox
                    v-model="uiSettings.promptSoundSettings.priorityChannelTone"
                    :disabled="muteAll"
                  />
                  <bf-input-number
                    v-model="uiSettings.priorityChannelToneVolume"
                    :formatter="formatUiSettingVolume"
                    step-strictly
                    :min="0"
                    :max="8"
                    :step="1"
                    :disabled="
                      muteAll ||
                      !uiSettings.promptSoundSettings.priorityChannelTone
                    "
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item
                  :label="$t('writeFreq.buttonTone')"
                  class="tone-volume-form-item"
                >
                  <el-checkbox
                    v-model="uiSettings.promptSoundSettings.buttonTone"
                    :disabled="muteAll"
                  />
                  <bf-input-number
                    v-model="uiSettings.buttonToneVolume"
                    :formatter="formatUiSettingVolume"
                    step-strictly
                    :min="0"
                    :max="8"
                    :step="1"
                    :disabled="
                      muteAll || !uiSettings.promptSoundSettings.buttonTone
                    "
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item
                  :label="$t('writeFreq.lowVoltageAlarmTone')"
                  class="tone-volume-form-item"
                >
                  <el-checkbox
                    v-model="uiSettings.promptSoundSettings.lowVoltageAlarmTone"
                    :disabled="muteAll"
                  />
                  <bf-input-number
                    v-model="uiSettings.lowVoltageAlarmToneVolume"
                    :formatter="formatUiSettingVolume"
                    step-strictly
                    :min="0"
                    :max="8"
                    :step="1"
                    :disabled="
                      muteAll ||
                      !uiSettings.promptSoundSettings.lowVoltageAlarmTone
                    "
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item
                  :label="$t('writeFreq.poweredTone')"
                  class="tone-volume-form-item"
                >
                  <el-checkbox
                    v-model="uiSettings.promptSoundSettings.poweredTone"
                    :disabled="muteAll"
                  />
                  <bf-input-number
                    v-model="uiSettings.poweredToneVolume"
                    :formatter="formatUiSettingVolume"
                    step-strictly
                    :min="0"
                    :max="8"
                    :step="1"
                    :disabled="
                      muteAll || !uiSettings.promptSoundSettings.poweredTone
                    "
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item class="tone-volume-form-item">
                  <el-checkbox
                    v-model="uiSettings.inOutNetworkTone"
                    :disabled="muteAll"
                  >
                    <span v-text="$t('writeFreq.inOutNetworkTone')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item
                  :label="$t('writeFreq.receiverAlarmTone')"
                  class="tone-volume-form-item"
                >
                  <el-checkbox
                    v-model="uiSettings.promptSoundSettings.alarmTone"
                  />
                  <bf-input-number
                    v-model="uiSettings.alarmToneVolume"
                    :formatter="formatUiSettingVolume"
                    step-strictly
                    :min="0"
                    :max="8"
                    :step="1"
                    :disabled="!uiSettings.promptSoundSettings.alarmTone"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item
                  :label="$t('writeFreq.callPromptTimes')"
                  class="tone-volume-form-item"
                >
                  <bf-input-number
                    v-model="uiSettings.callPromptTimes"
                    step-strictly
                    :min="0"
                    :max="20"
                    :step="1"
                    :formatter="
                      v => {
                        return v === 0 ? $t('writeFreq.off') : v + ''
                      }
                    "
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.stealthModeSettings')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox v-model="uiSettings.stealthMode">
                    <span v-text="$t('writeFreq.stealthMode')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox
                    v-model="uiSettings.stealthModeSettings.shieldedLedLight"
                    :disabled="notStealthMode"
                  >
                    <span v-text="$t('writeFreq.shieldedLedLight')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox
                    v-model="
                      uiSettings.stealthModeSettings.shieldedKeyboardLock
                    "
                    :disabled="notStealthMode"
                  >
                    <span v-text="$t('writeFreq.shieldedKeyboardLock')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox
                    v-model="
                      uiSettings.stealthModeSettings.stealthModeBacklight
                    "
                    :disabled="notStealthMode"
                  >
                    <span v-text="$t('writeFreq.stealthModeBacklight')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox
                    v-model="
                      uiSettings.stealthModeSettings.stealthModeVibrationShield
                    "
                    :disabled="notStealthMode"
                  >
                    <span v-text="$t('writeFreq.stealthModeVibrationShield')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox
                    v-model="uiSettings.stealthModeSettings.shieldedHeadphones"
                    :disabled="notStealthMode"
                  >
                    <span v-text="$t('writeFreq.shieldedHeadphones')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox
                    v-model="uiSettings.stealthModeSettings.shieldedMicrophone"
                    :disabled="notStealthMode"
                  >
                    <span v-text="$t('writeFreq.shieldedMicrophone')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.backlightSettings')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.backlight')">
                  <el-select
                    v-model="uiSettings.vibrationSettings.backLightMode"
                    :placeholder="$t('dialog.select')"
                    filterable
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in backlightList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.autoBackLightTime')">
                  <el-input-number
                    v-model="uiSettings.autoBackLightTime"
                    step-strictly
                    :min="5"
                    :max="60"
                    :step="1"
                    :disabled="uiSettings.vibrationSettings.backLightMode !== 2"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.indicatorSettings')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox v-model="uiSettings.ledSettings.disabledAllLED">
                    <span v-text="$t('writeFreq.allIndicators')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox
                    v-model="uiSettings.ledSettings.emittingLed"
                    :disabled="disabledAllLED"
                  >
                    <span v-text="$t('writeFreq.emittingLed')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox
                    v-model="uiSettings.ledSettings.receiveLed"
                    :disabled="disabledAllLED"
                  >
                    <span v-text="$t('writeFreq.receiveLed')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox
                    v-model="uiSettings.ledSettings.scanRoamStatusLed"
                    :disabled="disabledAllLED"
                  >
                    <span v-text="$t('writeFreq.scanRoamStatusLed')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox
                    v-model="uiSettings.ledSettings.lowBatteryLed"
                    :disabled="disabledAllLED"
                  >
                    <span v-text="$t('writeFreq.lowBatteryLed')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox
                    v-model="uiSettings.ledSettings.callHangsLed"
                    :disabled="disabledAllLED"
                  >
                    <span v-text="$t('writeFreq.callHangsLed')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.vibrationSettings')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox v-model="uiSettings.vibrationSettings.vibration">
                    <span v-text="$t('writeFreq.vibration')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox
                    v-model="uiSettings.vibrationSettings.singleCall"
                    :disabled="vibrationEnable"
                  >
                    <span v-text="$t('writeFreq.singleCallVibration')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox
                    v-model="uiSettings.vibrationSettings.message"
                    :disabled="vibrationEnable"
                  >
                    <span v-text="$t('writeFreq.messageVibration')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox
                    v-model="uiSettings.vibrationSettings.callTone"
                    :disabled="vibrationEnable"
                  >
                    <span v-text="$t('writeFreq.callToneVibration')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.keyboardLock')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox v-model="uiSettings.autoKeyboardLock">
                    <span v-text="$t('writeFreq.autoKeyboardLock')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item
                  :label="$t('writeFreq.autoKeyboardLockDelayTime')"
                >
                  <el-input-number
                    v-model="uiSettings.autoKeyboardLockDelayTime"
                    step-strictly
                    :min="5"
                    :max="60"
                    :step="1"
                    :disabled="!autoKeyboardLock"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.optionalLockKey')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox
                    v-model="uiSettings.optionalLockSettings.orangeKey"
                  >
                    <span v-text="$t('writeFreq.lockKeys.orangeKey')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox
                    v-model="uiSettings.optionalLockSettings.func1Key"
                  >
                    <span v-text="$t('writeFreq.lockKeys.func1Key')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox
                    v-model="uiSettings.optionalLockSettings.func2Key"
                  >
                    <span v-text="$t('writeFreq.lockKeys.func2Key')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox
                    v-model="uiSettings.optionalLockSettings.dialKey"
                  >
                    <span v-text="$t('writeFreq.lockKeys.p1Key')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox
                    v-model="uiSettings.optionalLockSettings.onHookKey"
                  >
                    <span v-text="$t('writeFreq.lockKeys.p2Key')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox v-model="uiSettings.optionalLockSettings.pttKey">
                    <span v-text="$t('writeFreq.lockKeys.pttKey')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox
                    v-model="uiSettings.optionalLockSettings.channelKey"
                  >
                    <span v-text="$t('writeFreq.lockKeys.channelKey')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox
                    v-model="uiSettings.optionalLockSettings.volumeKey"
                  >
                    <span v-text="$t('writeFreq.lockKeys.volumeKey')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox v-model="uiSettings.knobKeySettings.knobKey">
                    <span v-text="$t('writeFreq.lockKeys.knobKey')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
        <el-card
          v-if="showProgramPasswordSettings"
          shadow="never"
          class="write-freq-component program-password-container"
        >
          <el-form
            ref="programPassword"
            class="program-password-form"
            label-width="100px"
            label-position="top"
          >
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-col>
                <el-form-item :label="$t('writeFreq.readPassword')">
                  <el-input
                    v-model="programReadPassword.md5Key"
                    type="password"
                    :maxlength="8"
                    @change="readPasswordChanged"
                    @input="readPasswordInput"
                  />
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item :label="$t('writeFreq.writePassword')">
                  <el-input
                    v-model="programWritePassword.md5Key"
                    type="password"
                    :maxlength="8"
                    @change="writePasswordChanged"
                    @input="writePasswordInput"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
        <el-card
          v-show="showButtonSettings"
          shadow="never"
          class="write-freq-component buttonDefine-container"
        >
          <el-form
            ref="buttonDefine"
            class="buttonDefine-form"
            :model="buttonDefined"
            label-position="top"
          >
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-col :xs="24" :sm="12">
                <el-form-item
                  :label="$t('dialog.longPressDuration')"
                  :label-width="buttonDefineLabelWidth"
                >
                  <el-input-number
                    v-model="buttonDefined.longPressDuration"
                    step-strictly
                    :min="500"
                    :max="5000"
                    :step="500"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item
                  :label="$t('writeFreq.emergencyAlarmExitDuration')"
                  :label-width="buttonDefineLabelWidth"
                >
                  <el-input-number
                    v-model="buttonDefined.longPressExitTime"
                    step-strictly
                    :min="500"
                    :max="5000"
                    :step="500"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item
                  :label="$t('writeFreq.defaultKeyboardInput')"
                  :label-width="buttonDefineLabelWidth"
                >
                  <el-select
                    v-model="buttonDefined.defaultKeyboardInput"
                    :placeholder="$t('dialog.select')"
                    filterable
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(shortKey, i) in defaultKeyboardInputList"
                      :key="i"
                      :label="shortKey.label"
                      :value="shortKey.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item
                  :label="$t('writeFreq.defaultKnobFunc')"
                  :label-width="buttonDefineLabelWidth"
                >
                  <el-select
                    v-model="buttonDefined.defaultKnobFunc"
                    :placeholder="$t('dialog.select')"
                    filterable
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(shortKey, i) in defaultKnobFuncList"
                      :key="i"
                      :label="shortKey.label"
                      :value="shortKey.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-col :xs="24">
                <el-table
                  :data="buttonDefined.shortLongKeyDefine"
                  :empty-text="$t('msgbox.emptyText')"
                  class="table-no-bg"
                >
                  <el-table-column label="" min-width="65">
                    <template #default="scope">
                      <span v-text="keyNames[scope.$index]" />
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('dialog.shortPress')"
                    min-width="100"
                  >
                    <template #default="scope">
                      <el-form-item label-width="0">
                        <el-select
                          v-model="scope.row.short"
                          :placeholder="$t('dialog.select')"
                          filterable
                          :no-match-text="$t('dialog.noMatchText')"
                          @change="syncShortPressDefine(scope.row)"
                        >
                          <el-option
                            v-for="(shortKey, i) in getSoftKeyFuncDefine(0)"
                            :key="i"
                            :label="shortKey.label"
                            :value="shortKey.value"
                          />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('dialog.longPress')"
                    min-width="85"
                  >
                    <template #default="scope">
                      <el-form-item label-width="0">
                        <el-select
                          v-model="scope.row.long"
                          :placeholder="$t('dialog.select')"
                          filterable
                          :no-match-text="$t('dialog.noMatchText')"
                          @change="syncLongPressDefine(scope.row)"
                        >
                          <el-option
                            v-for="(shortKey, i) in getSoftKeyFuncDefine(1)"
                            :key="i"
                            :label="shortKey.label"
                            :value="shortKey.value"
                          />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.singleKeyFuncCall')" />
              </el-divider>
              <el-col :xs="24">
                <el-table
                  :data="buttonDefined.oneTouchFuncCall"
                  :empty-text="$t('msgbox.emptyText')"
                  class="table-no-bg"
                >
                  <el-table-column label="">
                    <template #default="scope">
                      <span v-text="oneTouchKeyNames[scope.$index]" />
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('writeFreq.callMode')">
                    <template #default="scope">
                      <el-form-item label-width="0">
                        <el-select
                          v-model="scope.row.callMode"
                          :placeholder="$t('dialog.select')"
                          filterable
                          :no-match-text="$t('dialog.noMatchText')"
                          @change="
                            () => {
                              oneTouchFuncCallModeChanged(scope.row)
                            }
                          "
                        >
                          <el-option
                            v-for="(item, i) in callModeList"
                            :key="i"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('dialog.callTarget')">
                    <template #default="scope">
                      <el-form-item label-width="0">
                        <el-select
                          v-model="scope.row.callId"
                          :placeholder="$t('dialog.select')"
                          filterable
                          :disabled="scope.row.callMode === 0xff"
                          :no-match-text="$t('dialog.noMatchText')"
                          @change="
                            () => {
                              oneTouchFuncCallTargetChanged(scope.row)
                            }
                          "
                        >
                          <el-option
                            v-for="(item, i) in buttonDefineAddressList"
                            :key="i"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('dialog.callType')">
                    <template #default="scope">
                      <el-form-item label-width="0">
                        <el-select
                          v-model="scope.row.callType"
                          :placeholder="$t('dialog.select')"
                          filterable
                          :disabled="scope.row.callId === 0xffff"
                          :no-match-text="$t('dialog.noMatchText')"
                          @change="
                            () => {
                              oneTouchFuncCallTypeChanged(scope.row)
                            }
                          "
                        >
                          <el-option
                            v-for="(callType, i) in getSoftKeyCallTypeList(
                              scope.row,
                            )"
                            :key="i"
                            :label="callType.label"
                            :value="callType.value"
                          />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('writeFreq.sms')">
                    <template #default="scope">
                      <el-form-item label-width="0">
                        <el-select
                          v-model="scope.row.msgId"
                          :placeholder="$t('dialog.select')"
                          filterable
                          :disabled="scope.row.callType !== SoftKeyCallType.MSG"
                          :no-match-text="$t('dialog.noMatchText')"
                          popper-class="sms-selection-container"
                        >
                          <el-option
                            v-for="(sms, smsIndex) in getKeyDefineSmsList(
                              scope.row,
                            )"
                            :key="smsIndex"
                            :label="sms.label"
                            :value="sms.value"
                          />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
        <el-card
          v-if="showMenuSettings"
          shadow="never"
          class="write-freq-component menu-settings-container"
        >
          <el-form
            ref="menuSettings"
            class="menu-settings-form"
            :model="menuSettings"
            label-position="top"
          >
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-col :xs="24" :sm="12">
                <el-form-item
                  :label="$t('dialog.menuHangTime')"
                  :label-width="menuHangTimeLabelWidth"
                >
                  <el-input-number
                    v-model="menuSettings.menuHangTime"
                    step-strictly
                    :min="0"
                    :max="30"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.menuEnable">
                    <span v-text="$t('header.setting')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.device')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.interfaceSettings.enable"
                    :disabled="disabledMenuAll"
                  >
                    <span v-text="$t('dialog.deviceSettings')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.interfaceSettings.locale"
                    :disabled="disabledMenuDevice"
                  >
                    <span v-text="$t('writeFreq.langEnv')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.interface2Settings.voiceBroadcast"
                    :disabled="disabledMenuDevice"
                  >
                    <span v-text="$t('writeFreq.voiceBroadcast')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.interfaceSettings.ledIndicator"
                    :disabled="disabledMenuDevice"
                  >
                    <span v-text="$t('dialog.ledIndicator')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.interfaceSettings.backLight"
                    :disabled="disabledMenuDevice"
                  >
                    <span v-text="$t('writeFreq.backlight')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.interfaceSettings.tone"
                    :disabled="disabledMenuDevice"
                  >
                    <span v-text="$t('writeFreq.indicationTones')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.interfaceSettings.keyboardLock"
                    :disabled="disabledMenuDevice"
                  >
                    <span v-text="$t('dialog.keyboardLock')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.interfaceSettings.soundCtrl"
                    :disabled="disabledMenuDevice"
                  >
                    <span v-text="$t('dialog.voiceControl')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.interfaceSettings.vibration"
                    :disabled="disabledMenuDevice"
                  >
                    <span v-text="$t('writeFreq.vibration')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>

              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.interface2Settings.upsideDown"
                    :disabled="disabledMenuDevice"
                  >
                    <span v-text="$t('writeFreq.runBackward')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.interface2Settings.stealthMode"
                    :disabled="disabledMenuDevice"
                  >
                    <span v-text="$t('writeFreq.stealthMode')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.interface2Settings.aloneWork"
                    :disabled="disabledMenuDevice"
                  >
                    <span v-text="$t('writeFreq.workAlone')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.interface2Settings.powerOnPwd"
                    :disabled="disabledMenuPowerOnPwd"
                  >
                    <span v-text="$t('dialog.powerOnPwd')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.interface2Settings.bootInterface"
                    :disabled="disabledMenuDevice"
                  >
                    <span v-text="$t('dialog.bootInterface')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.channelSettings.offline"
                    :disabled="disabledMenuDevice"
                  >
                    <span v-text="$t('dialog.offNetwork')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.channelSettings.squelchLevel"
                    :disabled="disabledMenuDevice"
                  >
                    <span v-text="$t('dialog.squelchLevel')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.interface2Settings.chLockEnable"
                    :disabled="disabledMenuDevice"
                  >
                    <span
                      v-text="$t('writeFreq.softKeyFuncDefine.CH_LOCK_SW')"
                    />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('dialog.channel')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.channelSettings.enable"
                    :disabled="disabledMenuAll"
                  >
                    <span v-text="$t('dialog.channelSetting')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.channelSettings.channelName"
                    :disabled="disabledMenuChannel"
                  >
                    <span v-text="$t('dialog.chName')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.channelSettings.powerLevel"
                    :disabled="disabledMenuDevice"
                  >
                    <span v-text="$t('writeFreq.powerLevel')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.channelSettings.emissionLimit"
                    :disabled="disabledMenuChannel"
                  >
                    <span v-text="$t('dialog.transTimeLimit')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.channelSettings.txFrequency"
                    :disabled="disabledMenuChannel"
                  >
                    <span v-text="$t('dialog.transFrequency')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.channelSettings.rxFrequency"
                    :disabled="disabledMenuChannel"
                  >
                    <span v-text="$t('dialog.receiveFrequency')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>

              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.channel2Settings.subAudio"
                    :disabled="disabledMenuChannel"
                  >
                    <span v-text="$t('dialog.subAudio')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.channel2Settings.colorCode"
                    :disabled="disabledMenuChannel"
                  >
                    <span v-text="$t('dialog.colorCodes')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.channel2Settings.repeaterTimeSlot"
                    :disabled="disabledMenuChannel"
                  >
                    <span v-text="$t('writeFreq.timeSlotSelection')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.channel2Settings.launchContact"
                    :disabled="disabledMenuChannel"
                  >
                    <span v-text="$t('dialog.launchContact')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.channel2Settings.rxGroupList"
                    :disabled="disabledMenuChannel"
                  >
                    <span v-text="$t('dialog.receiveGroupList')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.encryption')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.encrypt"
                    :disabled="disabledMenuChannel"
                  >
                    <span v-text="$t('writeFreq.encryption')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.encryptSettings.keyEnable"
                    :disabled="disabledMenuEncrypt"
                  >
                    <span v-text="$t('writeFreq.secretKeySwitch')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.encryptSettings.newKey"
                    :disabled="disabledMenuEncrypt"
                  >
                    <span v-text="$t('writeFreq.newSecretKey')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.encryptSettings.keyList"
                    :disabled="disabledMenuEncrypt"
                  >
                    <span v-text="$t('writeFreq.keyList')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('dialog.deviceInfo')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.deviceInfoSettings.enable"
                    :disabled="disabledMenuAll"
                  >
                    <span v-text="$t('dialog.deviceInfo')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.deviceInfoSettings.batteryCharge"
                    :disabled="disabledMenuDeviceInfo"
                  >
                    <span v-text="$t('writeFreq.batteryCharge')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.deviceInfoSettings.localNumber"
                    :disabled="disabledMenuDeviceInfo"
                  >
                    <span v-text="$t('dialog.localNumber')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.deviceInfoSettings.localName"
                    :disabled="disabledMenuDeviceInfo"
                  >
                    <span v-text="$t('dialog.localName')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.deviceInfoSettings.firmwareVersion"
                    :disabled="disabledMenuDeviceInfo"
                  >
                    <span v-text="$t('dialog.firmwareVersion')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.deviceInfoSettings.cpVersion"
                    :disabled="disabledMenuDeviceInfo"
                  >
                    <span v-text="$t('dialog.cpVersion')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.deviceInfoSettings.uiVersion"
                    :disabled="disabledMenuDeviceInfo"
                  >
                    <span v-text="$t('writeFreq.uiVersion')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.zone')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.zone">
                    <span v-text="$t('writeFreq.zone')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.scan')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.scanSettings.enable">
                    <span v-text="$t('writeFreq.scan')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.scanSettings.scanSwitch"
                    :disabled="disabledMenuScan"
                  >
                    <span v-text="$t('dialog.scanEnable')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.scanSettings.scanEdit"
                    :disabled="disabledMenuScan"
                  >
                    <span v-text="$t('writeFreq.scanEdit')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.roaming')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.roamSettings.enable">
                    <span v-text="$t('writeFreq.roaming')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.roamSettings.siteLockUnlock"
                    :disabled="disabledMenuRoam"
                  >
                    <span v-text="$t('writeFreq.siteLock')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.roamSettings.manualSiteRoam"
                    :disabled="disabledMenuRoam"
                  >
                    <span v-text="$t('writeFreq.manualSiteRoam')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('dialog.callRecord')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.callRecordSettings.enable">
                    <span v-text="$t('dialog.callRecord')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.callRecordSettings.dialedCall"
                    :disabled="disabledMenuCallRecord"
                  >
                    <span v-text="$t('writeFreq.callOut')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.callRecordSettings.receivedCall"
                    :disabled="disabledMenuCallRecord"
                  >
                    <span v-text="$t('writeFreq.incomingCall')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.callRecordSettings.missedCall"
                    :disabled="disabledMenuCallRecord"
                  >
                    <span v-text="$t('writeFreq.missedCall')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('dialog.annex')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.annex">
                    <span v-text="$t('dialog.annex')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('dialog.satellitePosition')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.gpsSettings.enable"
                    :disabled="disabledMenuAnnex"
                  >
                    <span v-text="$t('dialog.satellitePosition')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.gpsSettings.gpsSwitch"
                    :disabled="disabledMenuGps"
                  >
                    <span v-text="$t('writeFreq.satellitePositionSwitch')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.gpsSettings.gpsLocationInfo"
                    :disabled="disabledMenuGps"
                  >
                    <span v-text="$t('writeFreq.satellitePositionInfo')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.patrol')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.patrolSettings.enable"
                    :disabled="disabledMenuAnnex"
                  >
                    <span v-text="$t('writeFreq.patrol')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.patrolSettings.patrolSweepCard"
                    :disabled="disabledMenuPatrol"
                  >
                    <span v-text="$t('writeFreq.patrolClockIn')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.patrolSettings.patrolRecord"
                    :disabled="disabledMenuPatrol"
                  >
                    <span v-text="$t('writeFreq.patrolRecord')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row
              v-if="false"
              :gutter="20"
              class="no-margin-x"
              type="flex"
              align="middle"
            >
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('dialog.recording')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.recordSettings.enable"
                    :disabled="disabledMenuAnnex"
                  >
                    <span v-text="$t('dialog.recording')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.recordSettings.recordSwitch"
                    :disabled="disabledMenuRecord"
                  >
                    <span v-text="$t('writeFreq.recordSwitch')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.recordSettings.recordPlayback"
                    :disabled="disabledMenuRecord"
                  >
                    <span v-text="$t('writeFreq.recordPlayback')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('dialog.addressBook')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.contacts">
                    <span v-text="$t('dialog.addressBook')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.newContact"
                    :disabled="disabledMenuContacts"
                  >
                    <span v-text="$t('dialog.newContact')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('dialog.contactList')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.contactList"
                    :disabled="disabledMenuContacts"
                  >
                    <span v-text="$t('dialog.contactList')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.contactSettings.singleCall"
                    :disabled="disabledMenuContactList"
                  >
                    <span v-text="$t('dialog.singleCall')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.contactSettings.groupCall"
                    :disabled="disabledMenuContactList"
                  >
                    <span v-text="$t('dialog.groupCall')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.contactSettings.fullCall"
                    :disabled="disabledMenuContactList"
                  >
                    <span v-text="$t('dialog.fullCall')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.contactSettings.lookOver"
                    :disabled="disabledMenuContactList"
                  >
                    <span v-text="$t('writeFreq.lookOver')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.contactSettings.edit"
                    :disabled="disabledMenuContactList"
                  >
                    <span v-text="$t('writeFreq.edit')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.contactSettings.delete"
                    :disabled="disabledMenuContactList"
                  >
                    <span v-text="$t('dialog.delete')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('dialog.manualDialing')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.dialSettings.manualDial"
                    :disabled="disabledMenuContacts"
                  >
                    <span v-text="$t('dialog.manualDialing')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.dialSettings.singleCallDial"
                    :disabled="disabledMenuManualDial"
                  >
                    <span v-text="$t('dialog.singleCallDialing')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.dialSettings.groupCallDial"
                    :disabled="disabledMenuManualDial"
                  >
                    <span v-text="$t('dialog.groupCallDialing')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.controlBusiness')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.controlSettings.enable"
                    :disabled="disabledMenuContacts"
                  >
                    <span v-text="$t('writeFreq.controlBusiness')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.controlSettings.deviceDetect"
                    :disabled="disabledMenuControl"
                  >
                    <span v-text="$t('writeFreq.interphoneDetection')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.controlSettings.callPrompt"
                    :disabled="disabledMenuControl"
                  >
                    <span v-text="$t('dialog.callReminder')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.controlSettings.remoteMonitor"
                    :disabled="disabledMenuControl"
                  >
                    <span v-text="$t('dialog.remoteMonitor')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.controlSettings.remoteKill"
                    :disabled="disabledMenuControl"
                  >
                    <span v-text="$t('writeFreq.interphoneRemoteKill')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.controlSettings.remoteLive"
                    :disabled="disabledMenuControl"
                  >
                    <span v-text="$t('writeFreq.interphoneActivation')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.controlSettings.back2BackMode"
                    :disabled="disabledMenuControl"
                  >
                    <span v-text="$t('writeFreq.back2BackMode')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.sms')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.smsSettings.enable">
                    <span v-text="$t('writeFreq.sms')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.smsSettings.newSms"
                    :disabled="disabledMenuSms"
                  >
                    <span v-text="$t('dialog.new')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.smsSettings.preMadeSms"
                    :disabled="disabledMenuSms"
                  >
                    <span v-text="$t('dialog.preMadeSms')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.smsSettings.inbox"
                    :disabled="disabledMenuSms"
                  >
                    <span v-text="$t('dialog.inbox')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.smsSettings.outbox"
                    :disabled="disabledMenuSms"
                  >
                    <span v-text="$t('dialog.outbox')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.smsSettings.draftBox"
                    :disabled="disabledMenuSms"
                  >
                    <span v-text="$t('dialog.draftBox')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.smsSettings.systemInfo"
                    :disabled="disabledMenuSms"
                  >
                    <span v-text="$t('writeFreq.systemInfo')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.phoneBook')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.phoneBookSettings.enable">
                    <span v-text="$t('writeFreq.phoneBook')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.phoneBookSettings.phoneDialer"
                    :disabled="disabledMenuPhoneBook"
                  >
                    <span v-text="$t('writeFreq.phoneDialer')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.phoneBookSettings.newContact"
                    :disabled="disabledMenuPhoneBook"
                  >
                    <span v-text="$t('dialog.newContact')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.phoneBookSettings.allContacts"
                    :disabled="disabledMenuPhoneBook"
                  >
                    <span v-text="$t('dialog.allContacts')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.phoneBookSettings.lookOverContact"
                    :disabled="disabledMenuPhoneBook"
                  >
                    <span v-text="$t('dialog.viewContacts')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.phoneBookSettings.editContact"
                    :disabled="disabledMenuPhoneBook"
                  >
                    <span v-text="$t('dialog.editContacts2')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="menuSettings.phoneBookSettings.deleteContact"
                    :disabled="disabledMenuPhoneBook"
                  >
                    <span v-text="$t('dialog.deleteContact')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
        <el-card
          v-if="showGpsSettings"
          shadow="never"
          class="write-freq-component gps-settings-container"
        >
          <el-form
            ref="gpsData"
            class="gpsSettings-form"
            :model="gpsSettings"
            label-position="top"
          >
            <el-row :gutter="20" class="no-margin-x">
              <el-col>
                <el-form-item :label="$t('writeFreq.gpsMode')">
                  <el-select
                    v-model="gpsSettings.mode"
                    :placeholder="$t('dialog.select')"
                    filterable
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in gpsModeList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item :label="$t('writeFreq.navType')">
                  <el-select
                    v-model="gpsSettings.navType"
                    :placeholder="$t('dialog.select')"
                    filterable
                    :disabled="!gpsSettingsEnable"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in navTypeList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.triggerControl')" />
              </el-divider>
              <el-col>
                <el-form-item :label="$t('writeFreq.pttTimes')">
                  <bf-input-number
                    v-model="gpsSettings.pttTimes"
                    :disabled="!gpsSettingsEnable"
                    :formatter="v => (v === 0 ? $t('writeFreq.off') : v)"
                    step-strictly
                    :min="0"
                    :max="255"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item :label="$t('writeFreq.connectionTimes')">
                  <bf-input-number
                    v-model="gpsSettings.connectionTimes"
                    :disabled="!gpsSettingsEnable"
                    :formatter="v => (v === 0 ? $t('writeFreq.off') : v)"
                    step-strictly
                    :min="0"
                    :max="255"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.controlCenter')" />
              </el-divider>
              <el-col>
                <el-form-item :label="$t('writeFreq.controlCenter')">
                  <el-input-number
                    v-model="gpsSettings.centerId"
                    :disabled="!gpsSettingsEnable"
                    step-strictly
                    :min="1"
                    :max="16776415"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item :label="$t('writeFreq.queryCommand')">
                  <el-input
                    v-model="gpsSettings.queryCmd"
                    :disabled="!gpsSettingsEnable"
                    @input="
                      val =>
                        (gpsSettings.queryCmd =
                          gpsSettingsQueryCmdInputEvent(val))
                    "
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
        <el-card
          v-if="showUpsideDown"
          shadow="never"
          class="write-freq-component upside-down-container"
        >
          <el-form
            ref="upsideDown"
            class="upside-down-form"
            :model="upsideDown"
            label-position="top"
          >
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-col>
                <el-form-item>
                  <el-checkbox v-model="upsideDown.enable">
                    <span v-text="$t('writeFreq.upsideDownSwitch')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item :label="$t('writeFreq.triggerMode')">
                  <el-select
                    v-model="upsideDown.triggerMode"
                    :placeholder="$t('dialog.select')"
                    :disabled="upsideDownUnEnable"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="item in triggerModeList"
                      :key="item.label"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item :label="$t('writeFreq.triggerInclination')">
                  <el-select
                    v-model="upsideDown.triggerTilt"
                    :placeholder="$t('dialog.select')"
                    :disabled="disableTriggerTilt"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="item in triggerTiltList"
                      :key="item.label"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item :label="$t('writeFreq.entryDelay')">
                  <el-input-number
                    v-model="upsideDown.entryDelay"
                    :disabled="upsideDownUnEnable"
                    step-strictly
                    :min="0"
                    :max="255"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item :label="$t('writeFreq.exitDelay')">
                  <bf-input-number
                    v-model="upsideDown.quitDelay"
                    :disabled="upsideDownUnEnable"
                    :formatter="
                      val => (val === 255 ? $t('writeFreq.unlimited') : val)
                    "
                    step-strictly
                    :min="0"
                    :max="255"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item :label="$t('writeFreq.promptTimeBackwards')">
                  <el-input-number
                    v-model="upsideDown.preHintTime"
                    :disabled="upsideDownUnEnable"
                    step-strictly
                    :min="0"
                    :max="maxPreHintTime"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
        <el-card
          v-if="showWorkAlone"
          shadow="never"
          class="write-freq-component alone-work-container"
        >
          <el-form
            ref="aloneWork"
            class="alone-work-form"
            :model="aloneWork"
            label-position="top"
          >
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-col>
                <el-form-item>
                  <el-checkbox v-model="aloneWork.enable">
                    <span v-text="$t('writeFreq.workAlone')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item :label="$t('writeFreq.workResTimeAlone')">
                  <el-input-number
                    v-model="aloneWork.responseTime"
                    :disabled="workAloneUnEnable"
                    step-strictly
                    :min="1"
                    :max="255"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item :label="$t('writeFreq.workAloneReminderTime')">
                  <el-input-number
                    v-model="aloneWork.remindTime"
                    :disabled="workAloneUnEnable"
                    step-strictly
                    :min="0"
                    :max="remindTimeMax"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item :label="$t('writeFreq.workResOptAlone')">
                  <el-select
                    v-model="aloneWork.responseAction"
                    :placeholder="$t('dialog.select')"
                    :disabled="workAloneUnEnable"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in aloneWorkOptList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
        <el-card
          v-if="showShortMessage"
          shadow="never"
          class="write-freq-component short-message-container"
        >
          <shortMessage
            :ref="refSms"
            v-model="smsContent"
            :maxSize="smsMaxSize"
            :maxLen="140"
            :model="Model"
          />
        </el-card>
        <el-card
          v-if="showEncryptSettings"
          shadow="never"
          class="write-freq-component encrypt-settings-container"
        >
          <div class="encrypt-settings__header">
            <div class="encrypt-control encrypt-control-enable">
              <el-checkbox v-model="encryptConfig.enable">
                <span v-text="$t('dialog.enable')" />
              </el-checkbox>
            </div>
            <div class="encrypt-control encrypt-control-btn">
              <el-button
                v-if="encryptListName === 'xor'"
                type="primary"
                :disabled="
                  !encryptEnable || encryptList.length >= encryptListLimit
                "
                @click="addEncryptItem"
                v-text="$t('dialog.add')"
              />
              <el-button
                v-if="encryptListName === 'arc4'"
                type="primary"
                :disabled="
                  !encryptEnable || encryptARC4List.length >= encryptListLimit
                "
                @click="addEncryptARC4Item"
                v-text="$t('dialog.add')"
              />
              <el-button
                v-if="encryptListName === 'ars'"
                type="primary"
                :disabled="
                  !encryptEnable || encryptARSList.length >= encryptListLimit
                "
                @click="addEncryptARSItem"
                v-text="$t('dialog.add')"
              />
            </div>
          </div>
          <el-tabs v-model="encryptListName" class="encrypt-list-tabs">
            <el-tab-pane
              :label="$t('writeFreq.encryption')"
              name="xor"
              class="encrypt-list-xor"
            >
              <el-table
                :data="encryptList"
                :empty-text="$t('msgbox.emptyText')"
                :highlight-current-row="encryptEnable"
                :row-class-name="encryptEnable ? 'encrypt-enable-row' : ' '"
                height="100%"
                class="encrypt-list__table"
              >
                <el-table-column label="#" type="index" />
                <el-table-column
                  :label="$t('writeFreq.secretKeyName')"
                  min-width="100"
                >
                  <template #default="scope">
                    <el-input
                      v-model="scope.row.keyName"
                      :maxlength="16"
                      :disabled="!encryptEnable"
                      @change="
                        () => {
                          encryptKeyNameChanged(scope.row)
                        }
                      "
                    />
                  </template>
                </el-table-column>
                <el-table-column
                  :label="$t('writeFreq.secretKeyValue')"
                  min-width="100"
                >
                  <template #default="scope">
                    <el-input
                      v-model="scope.row.keyValue"
                      type="password"
                      :maxlength="10"
                      :disabled="!encryptEnable"
                      @input="
                        () => {
                          encryptKeyValueInput(scope.row)
                        }
                      "
                      @change="
                        () => {
                          encryptKeyValueChanged(scope.row)
                        }
                      "
                    />
                  </template>
                </el-table-column>
                <el-table-column label="#" :width="encryptListActiveWidth">
                  <template #default="scope">
                    <el-button
                      type="danger"
                      :disabled="!encryptEnable || encryptList.length <= 1"
                      @click="deleteEncryptItem(scope.row, scope.$index)"
                      v-text="$t('dialog.delete')"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane
              :label="$t('writeFreq.advancedEncryptionARC4')"
              name="arc4"
              class="encrypt-list-arc4"
            >
              <el-table
                :data="encryptARC4List"
                :empty-text="$t('msgbox.emptyText')"
                :highlight-current-row="encryptEnable"
                :row-class-name="encryptEnable ? 'encrypt-enable-row' : ' '"
                height="100%"
                class="encrypt-list__table"
              >
                <el-table-column label="#" type="index" />
                <el-table-column
                  :label="$t('writeFreq.secretKeyName')"
                  min-width="100"
                >
                  <template #default="scope">
                    <el-input
                      v-model="scope.row.keyName"
                      :maxlength="16"
                      :disabled="!encryptEnable"
                      @change="
                        () => {
                          encryptKeyNameChanged(scope.row, 'ARC4')
                        }
                      "
                    />
                  </template>
                </el-table-column>
                <el-table-column
                  :label="$t('writeFreq.secretKeyValue')"
                  min-width="100"
                >
                  <template #default="scope">
                    <el-input
                      v-model="scope.row.keyValue"
                      type="password"
                      :maxlength="10"
                      :disabled="!encryptEnable"
                      @input="
                        () => {
                          encryptARC4ValueInput(scope.row)
                        }
                      "
                      @change="
                        () => {
                          encryptARC4ValueChanged(scope.row)
                        }
                      "
                    />
                  </template>
                </el-table-column>
                <el-table-column label="#" :width="encryptListActiveWidth">
                  <template #default="scope">
                    <el-button
                      type="danger"
                      :disabled="!encryptEnable || encryptARC4List.length <= 1"
                      @click="deleteEncryptARC4Item(scope.row, scope.$index)"
                      v-text="$t('dialog.delete')"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane
              :label="$t('writeFreq.advancedEncryptionARS')"
              name="ars"
              class="encrypt-list-ars"
            >
              <el-table
                :data="encryptARSList"
                :empty-text="$t('msgbox.emptyText')"
                :highlight-current-row="encryptEnable"
                :row-class-name="encryptEnable ? 'encrypt-enable-row' : ' '"
                height="100%"
                class="encrypt-list__table"
              >
                <el-table-column label="#" type="index" />
                <el-table-column
                  :label="$t('writeFreq.secretKeyName')"
                  min-width="100"
                >
                  <template #default="scope">
                    <el-input
                      v-model="scope.row.keyName"
                      :maxlength="16"
                      :disabled="!encryptEnable"
                      @change="
                        () => {
                          encryptKeyNameChanged(scope.row, 'ARS')
                        }
                      "
                    />
                  </template>
                </el-table-column>
                <el-table-column
                  :label="$t('writeFreq.secretKeyValue')"
                  min-width="100"
                >
                  <template #default="scope">
                    <el-input
                      v-model="scope.row.keyValue"
                      type="password"
                      :maxlength="64"
                      :disabled="!encryptEnable"
                      @input="
                        () => {
                          encryptARSValueInput(scope.row)
                        }
                      "
                      @change="
                        () => {
                          encryptARSValueChanged(scope.row)
                        }
                      "
                    />
                  </template>
                </el-table-column>
                <el-table-column label="#" :width="encryptListActiveWidth">
                  <template #default="scope">
                    <el-button
                      type="danger"
                      :disabled="!encryptEnable || encryptARSList.length <= 1"
                      @click="deleteEncryptARSItem(scope.row, scope.$index)"
                      v-text="$t('dialog.delete')"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </el-card>
        <el-card
          v-show="showPhoneBook"
          shadow="never"
          class="write-freq-component phone-book-container"
        >
          <phoneBook
            :ref="phoneBookTreeId"
            class="phone-book"
            :treeId="phoneBookTreeId"
            :redrawTree="showPhoneBook"
            @select="selectPhoneBooks"
          />
        </el-card>
        <el-card
          v-show="showDigitalAddress"
          shadow="never"
          class="write-freq-component address-book-container"
        >
          <addressBook
            :ref="addrBookTreeId"
            class="address-book"
            :treeId="addrBookTreeId"
            :maxSize="1000"
            :redrawTree="showDigitalAddress"
            :callTypes="addressBookCallTypes"
            :encode="encodeDmrId"
            :decode="decodeDmrId"
            noSysSelect
            @select="selectAddressBooks"
          />
        </el-card>
        <el-card
          v-if="showScanConfig"
          shadow="never"
          class="write-freq-component scan-config-container"
        >
          <el-form
            ref="scanConfig"
            class="scan-config-form"
            :model="scanConfig"
            label-position="top"
          >
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-col :xs="24">
                <el-form-item :label="$t('writeFreq.scanSamplingTime')">
                  <el-input-number
                    v-model="scanConfig.scanSamplingTime"
                    :min="100"
                    :max="1000"
                    :step="100"
                    step-strictly
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
        <el-card
          v-if="showScanList"
          shadow="never"
          class="write-freq-component scan-container"
        >
          <scanGroupList
            ref="scanGroup"
            v-model="scanGroup[currScanGroupIndex]"
            :channels="channelDataList"
            :model="Model"
            :is-fullscreen="isFullscreen"
            @name-change="updateScanGroupNode"
          />
        </el-card>
        <el-card
          v-if="showRoamConfig"
          shadow="never"
          class="write-freq-component roam-config-container"
        >
          <el-form
            ref="roamConfig"
            class="roam-config-form"
            :model="roamConfig"
            label-position="top"
          >
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="roamConfig.activeSiteSearch">
                    <span v-text="$t('writeFreq.activeSiteSearch')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24">
                <el-form-item :label="$t('writeFreq.activeSiteSearchTiming')">
                  <el-input-number
                    v-model="roamConfig.activeSiteSearchTiming"
                    step-strictly
                    :min="0"
                    :max="255"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24">
                <el-form-item :label="$t('writeFreq.rssiDetectCycle')">
                  <el-input-number
                    v-model="roamConfig.rssiDetectCycle"
                    :min="30"
                    :max="120"
                    :step="5"
                    step-strictly
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24">
                <el-form-item
                  :label="$t('writeFreq.autoRoamingSearchInterval')"
                >
                  <el-input-number
                    v-model="roamConfig.autoRoamingSearchInterval"
                    step-strictly
                    :min="0"
                    :max="255"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
        <el-card
          v-if="showRoamList"
          shadow="never"
          class="write-freq-component roam-container"
        >
          <roamGroupList
            ref="roamGroup"
            v-model="roamGroup[currRoamGroupIndex]"
            :channels="channelDataList"
            :model="Model"
            :directMode="directMode"
            :currChannel="oneChannel"
            @name-change="updateRoamGroupNode"
          />
        </el-card>
        <el-card
          v-if="showPatrolSystem"
          shadow="never"
          class="write-freq-component patrol-system-container"
        >
          <el-form
            ref="patrolSystem"
            class="patrol-system-form"
            :model="patrolConfig"
            label-position="top"
          >
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('dialog.basicConfig')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.responseTimeout1')">
                  <el-input-number
                    v-model="patrolConfig.responseTimeout"
                    step-strictly
                    :min="2500"
                    :max="10000"
                    :step="500"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('dialog.emergency')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="patrolConfig.emergencyAlarm">
                    <span v-text="$t('dialog.emergency')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dataTable.alarmType')">
                  <el-select
                    v-model="patrolConfig.alarmType"
                    :placeholder="$t('dialog.select')"
                    filterable
                    :disabled="disableEmergencyAlarm"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in alarmTypeList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.callingContact')">
                  <el-select
                    v-model="patrolConfig.callContact"
                    :placeholder="$t('dialog.select')"
                    filterable
                    :disabled="disableEmergencyAlarm"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in callingContactList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.sendCount')">
                  <bf-input-number
                    v-model="sendCount"
                    :disabled="disableEmergencyAlarm"
                    :formatter="sendCountFormat"
                    step-strictly
                    :min="0"
                    :max="14"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.autoListeningTime')">
                  <el-input-number
                    v-model="patrolConfig.autoOpenMonitor"
                    :disabled="disableEmergencyAlarm"
                    step-strictly
                    :min="0"
                    :max="99"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.autoPositioningTime')">
                  <el-input-number
                    v-model="patrolConfig.autoOpenLocateTrack"
                    :disabled="disableEmergencyAlarm"
                    step-strictly
                    :min="0"
                    :max="99"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('dialog.trailCtrl')" />
              </el-divider>
              <el-col :xs="24">
                <el-form-item>
                  <el-checkbox v-model="patrolConfig.enableMonitorTrack">
                    <span v-text="$t('dialog.trailCtrl')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.trailSpacing')">
                  <el-input-number
                    v-model="patrolConfig.trackInterval"
                    :disabled="disableMonitorTrack"
                    :min="0"
                    :max="995"
                    :step="5"
                    step-strictly
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.shortestDistance')">
                  <el-input-number
                    v-model="patrolConfig.minDistance"
                    :disabled="disableMonitorTrack"
                    :min="0"
                    :max="495"
                    :step="5"
                    step-strictly
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('dialog.activeRFID')" />
              </el-divider>
              <el-col :xs="24">
                <el-form-item>
                  <el-checkbox v-model="patrolConfig.rfidSettings.enable">
                    <span v-text="$t('dialog.activeRFID')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.mode')">
                  <el-select
                    v-model="patrolConfig.rfidSettings.mode"
                    :placeholder="$t('dialog.select')"
                    filterable
                    :disabled="disableRfid"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in rfidModeList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.power')">
                  <el-select
                    v-model="patrolConfig.rfidSettings.power"
                    :placeholder="$t('dialog.select')"
                    filterable
                    :disabled="disableRfid"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in rfidPowerList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.answer')">
                  <el-select
                    v-model="patrolConfig.rfidSettings.reply"
                    :placeholder="$t('dialog.select')"
                    filterable
                    :disabled="disableRfid"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in rfidReplyList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.channel')">
                  <el-input-number
                    v-model="patrolConfig.channel"
                    :disabled="disableRfid"
                    step-strictly
                    :min="2"
                    :max="125"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
              <el-col
                v-for="(dataChannel, index) in patrolConfig.channelAddress"
                :key="index"
                :xs="24"
                :sm="12"
              >
                <el-form-item :label="$t('dialog.dataChannel', { num: index })">
                  <bf-input-number
                    v-model="patrolConfig.channelAddress[index]"
                    :disabled="disableRfid"
                    step-strictly
                    :min="0"
                    :max="0xffffffffff"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
        <el-card
          v-show="showRxGroup"
          shadow="never"
          class="write-freq-component receive-group-container"
        >
          <receiveGroup
            :ref="refReceiveGroup"
            v-model="rxGroupList"
            :channels="selectedChannels"
            :addressTreeId="addrBookTreeId"
            :getDefaultAddress="getDefaultAddress"
            :getAddressName="getAddressNameByDmrId"
            :getOriginAddress="getOriginAddressBook"
          />
        </el-card>
        <el-card
          v-show="showDmrConfigSettings"
          shadow="never"
          class="write-freq-component dmr-config-container"
        >
          <el-form
            ref="dmrConfig"
            class="dmr-config-form"
            :model="dmrConfig"
            label-position="top"
          >
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-col :xs="24" :sm="12">
                <el-form-item label="DMRID">
                  <el-input :value="dmrIdLabel" disabled />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.repeaterId')">
                  <el-input-number
                    v-model="dmrConfig.repeaterId"
                    step-strictly
                    :min="1"
                    :max="16777215"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.sendPreambleDuration')">
                  <el-input-number
                    v-model="dmrConfig.sendLeadCodeTime"
                    :min="0"
                    :max="8640"
                    :step="60"
                    step-strictly
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.offNetworkGroupCallHangTime')">
                  <el-input-number
                    v-model="dmrConfig.offlineGroupCallHungTime"
                    :min="0"
                    :max="7000"
                    :step="500"
                    step-strictly
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item
                  :label="$t('dialog.offNetworkSingleCallHangTime')"
                >
                  <el-input-number
                    v-model="dmrConfig.offlineSingleCallHungTime"
                    :min="0"
                    :max="7000"
                    :step="500"
                    step-strictly
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.remoteMonitorDuration')">
                  <el-input-number
                    v-model="dmrConfig.channelCount"
                    :min="10"
                    :max="120"
                    :step="10"
                    step-strictly
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="dmrConfig.baseSettings.rejectStrangerCall"
                  >
                    <span v-text="$t('writeFreq.rejectStrangerCall')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="dmrConfig.baseSettings.directMode">
                    <span v-text="$t('dialog.passThroughMode')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.decoding')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="dmrConfig.decodeSettings.remoteDeathDecode"
                  >
                    <span v-text="$t('writeFreq.remoteKillActivateDecode')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="dmrConfig.decodeSettings.remoteMonitorDecode"
                    @change="
                      v => {
                        v &&
                          (dmrConfig.decodeSettings.underAlarmRemoteMonitorDecode =
                            v)
                      }
                    "
                  >
                    <span v-text="$t('writeFreq.remoteMonitorDecode')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="
                      dmrConfig.decodeSettings.underAlarmRemoteMonitorDecode
                    "
                    :disabled="dmrConfig.decodeSettings.remoteMonitorDecode"
                  >
                    <span
                      v-text="$t('writeFreq.underAlarmRemoteMonitorDecode')"
                    />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="dmrConfig.decodeSettings.callToneDecode"
                  >
                    <span v-text="$t('writeFreq.callToneDecode')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="dmrConfig.decodeSettings.deviceDetectDecode"
                  >
                    <span v-text="$t('writeFreq.deviceDetectDecode')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.authentication')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="dmrConfig.decodeSettings.remoteDeathActiveAuth"
                  >
                    <span
                      v-text="$t('writeFreq.remoteKillActivateDecodeAuth')"
                    />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="dmrConfig.decodeSettings.remoteMonitorAuth"
                  >
                    <span v-text="$t('writeFreq.remoteMonitorAuth')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24">
                <el-form-item :label="$t('writeFreq.airAuthKey')">
                  <el-input v-model="dmrConfig.airAuthKey" :maxlength="16" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
        <el-card
          v-if="showDigitalAlert"
          shadow="never"
          class="write-freq-component digit-alarm-container"
        >
          <el-form
            ref="digitAlarm"
            class="digit-alarm-config-form"
            :model="digitalAlert"
            :rules="digitalAlertRules"
            label-position="top"
          >
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.name')" prop="name">
                  <el-input
                    v-model="digitalAlert.name"
                    :maxlength="16"
                    @change="updateDigitalAlertNode"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dataTable.alarmType')">
                  <el-select
                    v-model="digitalAlert.type"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in digitalAlertTypeList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.mode')">
                  <el-select
                    v-model="digitalAlert.mode"
                    :disabled="disableDigitalAlertMode"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in digitalAlertModeList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.replyChannel')">
                  <el-select
                    v-model="digitalAlert.replyChannel"
                    :disabled="disableReplyChannel"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in replyChannelList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="digitalAlert.autoEmergencyCall"
                    :disabled="disabledAutoEmergencyCall"
                  >
                    <span v-text="$t('writeFreq.autoEmergencyCall')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.emergencyCallTimes')">
                  <el-input-number
                    v-model="digitalAlert.emergencyCallTimes"
                    :disabled="!digitalAlert.autoEmergencyCall"
                    step-strictly
                    :min="1"
                    :max="255"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.receiveDuration')">
                  <el-input-number
                    v-model="digitalAlert.receiveDuration"
                    :disabled="!digitalAlert.autoEmergencyCall"
                    :min="10"
                    :max="120"
                    :step="10"
                    step-strictly
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.micActiveTime')">
                  <el-input-number
                    v-model="digitalAlert.micActiveDuration"
                    :disabled="!digitalAlert.autoEmergencyCall"
                    step-strictly
                    :min="10"
                    :max="120"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.notPoliteRetry')">
                  <el-input-number
                    v-model="digitalAlert.impoliteRetry"
                    :disabled="disablePoliteRetry"
                    step-strictly
                    :min="1"
                    :max="15"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.politeRetry')">
                  <bf-input-number
                    v-model="politeRetry"
                    :disabled="disablePoliteRetry"
                    :formatter="politeRetryFormat"
                    step-strictly
                    :min="-1"
                    :max="14"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
        <el-card
          v-if="showAnalogAlert"
          shadow="never"
          class="write-freq-component analog-alarm-container"
        >
          <el-form
            ref="analogAlarm"
            class="analog-alarm-config-form"
            :model="analogAlert"
            :rules="digitalAlertRules"
            label-position="top"
          >
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.name')" prop="name">
                  <el-input
                    v-model="analogAlert.name"
                    :maxlength="16"
                    @change="updateAnalogAlertNode"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dataTable.alarmType')">
                  <el-select
                    v-model="analogAlert.type"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in analogAlertTypeList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.mode')">
                  <el-select
                    v-model="analogAlert.mode"
                    :disabled="disableAnalogReplyChannel"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in digitalAlertModeList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.replyChannel')">
                  <el-select
                    v-model="analogAlert.replyChannel"
                    :disabled="disableAnalogReplyChannel"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in replyChannelList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="analogAlert.autoEmergencyCall"
                    :disabled="disabledAnalogAutoEmergencyCall"
                  >
                    <span v-text="$t('writeFreq.autoEmergencyCall')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.emergencyCallTimes')">
                  <el-input-number
                    v-model="analogAlert.emergencyCallTimes"
                    :disabled="!analogAlert.autoEmergencyCall"
                    step-strictly
                    :min="1"
                    :max="255"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.receiveDuration')">
                  <el-input-number
                    v-model="analogAlert.receiveDuration"
                    :disabled="!analogAlert.autoEmergencyCall"
                    step-strictly
                    :min="10"
                    :max="120"
                    :step="10"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.micActiveTime')">
                  <el-input-number
                    v-model="analogAlert.micActiveDuration"
                    :disabled="!analogAlert.autoEmergencyCall"
                    step-strictly
                    :min="10"
                    :max="120"
                    :step="10"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.notPoliteRetry')">
                  <el-input-number
                    v-model="analogAlert.impoliteRetry"
                    :disabled="disableAnalogPoliteRetry"
                    step-strictly
                    :min="1"
                    :max="15"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.politeRetry')">
                  <bf-input-number
                    v-model="analogPoliteRetry"
                    :disabled="disableAnalogPoliteRetry"
                    :formatter="politeRetryFormat"
                    step-strictly
                    :min="-1"
                    :max="14"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="analogAlert.localEmergencyAlert"
                    disabled
                  >
                    <span v-text="$t('writeFreq.localEmergencyAlert')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="analogAlert.backgroundPromptTone"
                    disabled
                  >
                    <span v-text="$t('writeFreq.backgroundPromptTone')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item
                  :label="$t('writeFreq.transmitAlertAudioDuration')"
                >
                  <el-input-number
                    v-model="analogAlert.emergencyAlertDuration"
                    :disabled="disableAnalogPoliteRetry"
                    step-strictly
                    :min="1"
                    :max="255"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.transmitAlertAudioTimes')">
                  <el-input-number
                    v-model="analogAlert.emergencyAlertTimes"
                    :disabled="disableAnalogPoliteRetry"
                    step-strictly
                    :min="1"
                    :max="255"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.alarmSquelchMode')">
                  <el-select
                    v-model="analogAlert.alarmSquelchMode"
                    disabled
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in alarmSquelchModeList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
        <el-card
          v-show="showZoneRootData"
          shadow="never"
          class="write-freq-component zone-info-container"
        >
          <multistageZone
            ref="rootZone"
            v-model="zoneDataList"
            v-model:dataId="currZoneId"
            :limit="zoneLimit"
            :level="1"
            zoneName="name"
            @row-dblclick="zoneRootDataDblclick"
          />
        </el-card>
        <el-card
          v-show="showZoneSettings"
          shadow="never"
          class="write-freq-component zone-channel-container"
        >
          <zoneChannelTable
            ref="zoneChannelTable"
            :channels="channelDataList"
            :zoneData="zoneDataIndex[currZoneId]"
            @row-dblclick="zoneDataRowDblclick"
          />
        </el-card>
        <el-card
          v-show="showChannelItem"
          shadow="never"
          class="write-freq-component channel-container"
        >
          <el-form
            ref="channelSetting"
            class="channel-setting-form"
            :model="oneChannel"
            :rules="channelRules"
            label-position="top"
          >
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.chName')" prop="chName">
                  <el-input
                    v-model="oneChannel.chName"
                    :maxlength="16"
                    @change="chNameChanged"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.chType')">
                  <el-select
                    v-model="oneChannel.chType"
                    @change="chTypeChanged"
                  >
                    <el-option
                      v-for="(item, i) in chTypeList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isDChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.channelBandwidth')">
                  <el-select v-model="oneChannel.channelBandwidth">
                    <el-option
                      v-for="(item, i) in channelBandwidthList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.scanAndRoamList')">
                  <el-select
                    v-model="oneChannel.channelScanList"
                    :disabled="isVirtualCluster"
                  >
                    <el-option
                      v-for="(item, i) in chScanRoamGroupList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="oneChannel.baseSettings.autoScan"
                    :disabled="disAutoScan"
                  >
                    <span v-text="$t('writeFreq.autoScanning')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="isDChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="oneChannel.callingSettings.autoRoam"
                    :disabled="disAutoRoam"
                  >
                    <span v-text="$t('writeFreq.autoRoaming')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.colorCodes')">
                  <el-input-number
                    v-model="oneChannel.colorCode"
                    step-strictly
                    :min="0"
                    :max="maxColorCode"
                    :step="1"
                    :disabled="isVirtualCluster"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="oneChannel.baseSettings.allowOffline"
                    :disabled="isVirtualCluster"
                  >
                    <span v-text="$t('dialog.allowOffNetwork')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="oneChannel.baseSettings.onlyReceive">
                    <span v-text="$t('dialog.receiveOnly')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="isDChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="oneChannel.callingSettings.ipSiteConnect"
                    :disabled="isVirtualCluster || sameFreq || onlyReceive"
                  >
                    <span v-text="$t('writeFreq.ipSiteConnection')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="oneChannel.callingSettings.priorityInterrupt"
                  >
                    <span v-text="$t('writeFreq.priorityInterrupt')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <!--              "联网"参数项,不再使用,但保留此参数 -->
              <!--              <el-col-->
              <!--                :xs="24"-->
              <!--                :sm="12"-->
              <!--                v-if='isDChannel'-->
              <!--              >-->
              <!--                <el-form-item>-->
              <!--                  <el-checkbox v-model="oneChannel.baseSettings.networking">-->
              <!--                    <span v-text="$t('writeFreq.networking')"></span>-->
              <!--                  </el-checkbox>-->
              <!--                </el-form-item>-->
              <!--              </el-col>-->
              <el-col v-if="isDChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="oneChannel.baseSettings.localCall"
                    :disabled="!isVirtualCluster"
                  >
                    <span v-text="$t('writeFreq.localCall')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="oneChannel.callingSettings.throughMode"
                    :disabled="!sameFreq"
                    @change="throughModeChange"
                  >
                    <span v-text="$t('writeFreq.TDMAThroughMode')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <!-- 虚拟集群 -->
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="oneChannel.encryptionSettings.virtualCluster"
                    :disabled="sameFreq"
                    @change="virtualClusterChange"
                  >
                    <span v-text="$t('writeFreq.virtualCluster')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="isDChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.SVTSiteInfo')">
                  <el-select
                    v-model="oneChannel.SVTSiteInfo"
                    :disabled="disableSVTSiteInfo"
                  >
                    <el-option
                      v-for="item in SVTSiteInfoList"
                      :key="item.label"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.chTimeSlotCalibrator')">
                  <el-select
                    v-model="oneChannel.callingSettings.channelSlotCalibrator"
                    :disabled="!oneChannel.callingSettings.throughMode"
                  >
                    <el-option
                      v-for="(item, i) in chTimeSlotCalList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.slotMode')">
                  <el-select
                    v-model="oneChannel.timeSlot"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                    :disabled="isVirtualCluster"
                    @change="timeSlotChanged"
                  >
                    <el-option
                      v-for="(item, i) in slotModeList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.specifyTxTimeSlot')">
                  <el-select
                    v-model="oneChannel.specifyTxTimeSlot"
                    :placeholder="$t('dialog.select')"
                    :disabled="isVirtualCluster || oneChannel.timeSlot !== 2"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in specifyTxTimeSlotList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.duplexMode')">
                  <el-select
                    v-model="oneChannel.encryptionSettings.duplexModel"
                    :placeholder="$t('dialog.select')"
                    :disabled="oneChannel.timeSlot !== 2"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="item in duplexModelList"
                      :key="item.label"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.voicePriority')">
                  <el-select v-model="oneChannel.voicePriority">
                    <el-option
                      v-for="item in voicePriorityList"
                      :key="item.label"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isDChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="oneChannel.analogSettings.weightingMark"
                  >
                    <span v-text="$t('writeFreq.weightingMark')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="!isDChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="oneChannel.analogSettings.compandEnable"
                  >
                    <span v-text="$t('writeFreq.compandingEnable')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row
              v-if="!isAChannel"
              :gutter="20"
              class="no-margin-x"
              type="flex"
              align="middle"
            >
              <el-divider>
                <el-icon class="divider-icon">
                  <CaretBottom />
                </el-icon>
                <span
                  class="divider-label"
                  v-text="$t('writeFreq.encryption')"
                />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="oneChannel.encryptionSettings.enable"
                    :disabled="disEncryptConfigEnable"
                  >
                    <span v-text="$t('dialog.enable')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.encryptionAlgorithm')">
                  <el-select
                    v-model="oneChannel.encryptionSettings.type"
                    :placeholder="$t('dialog.select')"
                    :disabled="disChannelEncrypt"
                    :no-match-text="$t('dialog.noMatchText')"
                    @change="encryptTypeChange"
                  >
                    <el-option
                      v-for="item in algorithmList"
                      :key="item.label"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.keyList')">
                  <el-select
                    v-model="oneChannel.encryptKey"
                    :placeholder="$t('dialog.select')"
                    :disabled="disChannelEncrypt"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="item in encryptKeyList"
                      :key="item.label"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="oneChannel.encryptionSettings.multiKeyDecryption"
                    :disabled="disAdvancedEncryption"
                  >
                    <span v-text="$t('writeFreq.multiKeyDecryption')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="oneChannel.encryptionSettings.randomKeyEncryption"
                    :disabled="disAdvancedEncryption"
                  >
                    <span v-text="$t('writeFreq.randomKeyEncryption')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon class="divider-icon">
                  <CaretBottom />
                </el-icon>
                <span class="divider-label" v-text="$t('dialog.receive')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.rxFrequency')" prop="rxFreq">
                  <frequencyMhz
                    v-model="oneChannel.rxFreq"
                    :maxlength="9"
                    @input="resetSVTSiteInfo"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <freqMapOffset
                  v-model="freqOffset"
                  v-model:dstFreq="oneChannel.txFreq"
                  :srcFreq="oneChannel.rxFreq"
                  :freqRange="[
                    {
                      min: deviceWriteInfo.minFrequency,
                      max: deviceWriteInfo.maxFrequency,
                    },
                  ]"
                  :disabled="onlyReceive"
                  @mapping="resetSVTSiteInfo"
                />
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="oneChannel.baseSettings.emergencyAlarmIndication"
                    :disabled="isVirtualCluster"
                  >
                    <span v-text="$t('writeFreq.emergencyAlarmIndication')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="oneChannel.baseSettings.emergencyAlarmConfirm"
                    :disabled="
                      isVirtualCluster ||
                      !oneChannel.baseSettings.emergencyAlarmIndication
                    "
                  >
                    <span v-text="$t('writeFreq.emergencyAlarmConfirm')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="oneChannel.baseSettings.emergencyCallTip"
                    :disabled="isVirtualCluster"
                  >
                    <span v-text="$t('writeFreq.emergencyCallAlert')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.receiveGroupList')">
                  <el-select
                    v-model="oneChannel.rxGroupId"
                    disabled
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in receiveGroupList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col v-if="!isDChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.rxCdcssType')">
                  <el-select
                    v-model="oneChannel.receiveToneType"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in receiveToneTypeList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isDChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.rxAnalogCdcss')">
                  <el-select
                    v-model="oneChannel.rxAnalogToneCode"
                    filterable
                    :disabled="disRxAnalogToneCode"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in analogToneCode"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isDChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.rxDigitalCdcss')">
                  <el-select
                    v-model="oneChannel.rxDigitalToneCode"
                    filterable
                    :disabled="disRxDigitalToneCode"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in digitalToneCode"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col v-if="isAChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.carrierSquelchLevel')">
                  <el-select
                    v-model="oneChannel.squelchLevel"
                    filterable
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="item in squelchLevelList"
                      :key="item.label"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isDChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.receiveSquelchMode')">
                  <el-select
                    v-model="oneChannel.rxSquelchMode"
                    filterable
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="item in rxSquelchModeList"
                      :key="item.label"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isDChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.monitorSquelchMode')">
                  <el-select
                    v-model="oneChannel.monitorSquelchMode"
                    filterable
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in rxSquelchModeList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isDChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.switchChannelSquelchMode')">
                  <el-select
                    v-model="oneChannel.switchChannelSquelchMode"
                    filterable
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in squelchModeList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="isDAChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.analogCallHangTime')">
                  <el-input-number
                    v-model="oneChannel.analogCallHangTime"
                    step-strictly
                    :min="0"
                    :max="255"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
              <el-col v-if="!isDChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.tailSelection')">
                  <el-select
                    v-model="oneChannel.endingSelection"
                    :disabled="disRxAnalogToneCode"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in tailToneList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon class="divider-icon">
                  <CaretBottom />
                </el-icon>
                <span class="divider-label" v-text="$t('dialog.emission')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.txFrequency')" prop="txFreq">
                  <frequencyMhz
                    v-model="oneChannel.txFreq"
                    :maxlength="9"
                    :disabled="onlyReceive"
                    @input="resetSVTSiteInfo"
                  />
                </el-form-item>
              </el-col>
              <el-col v-show="isDAChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.defaultTxChNetwork')">
                  <el-select
                    v-model="oneChannel.defaultTxChannelNetwork"
                    :disabled="onlyReceive"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in defaultTxChannelNetworkList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.sendGroup')">
                  <el-select
                    v-model="oneChannel.defaultTarget"
                    disabled
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in defaultAddressList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="emergencySysLabel">
                  <el-select
                    v-model="oneChannel.emergencySysId"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                    :disabled="isVirtualCluster"
                  >
                    <el-option
                      v-for="(item, i) in emergencySysIdList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.permissionConditions')">
                  <el-select
                    v-model="oneChannel.permitConditions"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                    :disabled="onlyReceive"
                  >
                    <el-option
                      v-for="(item, i) in permissionConditionsList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                      :disabled="item.disabled"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item
                  :label="$t('writeFreq.callEmissionPermitConditions')"
                >
                  <el-select
                    v-model="oneChannel.callingTxPermitCondition"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                    :disabled="onlyReceive"
                  >
                    <el-option
                      v-for="(item, i) in callingTxPermitConditionList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                      :disabled="item.disabled"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="oneChannel.callingSettings.dataCallConfirm"
                    :disabled="onlyReceive"
                  >
                    <span v-text="$t('writeFreq.confirmedDataSingleCall')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="oneChannel.callingSettings.singleCallConfirm"
                    :disabled="onlyReceive || allowPermitConditions"
                  >
                    <span v-text="$t('writeFreq.singleCallVoiceConfirm')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>

              <el-col v-if="!isDChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.txCdcssType')">
                  <el-select
                    v-model="oneChannel.transmitToneType"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in transmitToneTypeList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isDChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.txAnalogCdcss')">
                  <el-select
                    v-model="oneChannel.txAnalogToneCode"
                    filterable
                    :disabled="disTxAnalogToneCode"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in analogToneCode"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isDChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.txDigitalCdcss')">
                  <el-select
                    v-model="oneChannel.txDigitalToneCode"
                    filterable
                    :disabled="disTxDigitalToneCode"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in digitalToneCode"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isDChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.busyChannelLock')">
                  <el-select
                    v-model="oneChannel.busyChannelLock"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                    :disabled="onlyReceive"
                  >
                    <el-option
                      v-for="(item, i) in busyChannelLockList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isDChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox
                    v-model="oneChannel.analogSettings.tailCancellation"
                    :disabled="onlyReceive"
                  >
                    <span v-text="$t('dialog.tailCancellation')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-show="!isDChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.plosive')">
                  <el-select
                    v-model="oneChannel.plosive"
                    :disabled="disTxAnalogToneCode"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in plosiveList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.txPower')">
                  <el-select
                    v-model="oneChannel.txPower"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                    :disabled="onlyReceive"
                  >
                    <el-option
                      v-for="(item, i) in txPowerTypes"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.sendTimeLimiter')">
                  <bf-input-number
                    v-model="oneChannel.sendTimeLimiter"
                    :min="0"
                    :max="495"
                    :step="15"
                    step-strictly
                    :formatter="val => (val === 0 ? $t('dialog.off') : val)"
                    :disabled="onlyReceive"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.totPwdUpdateDelay')">
                  <el-input-number
                    v-model="oneChannel.totKeyUpdateDelay"
                    step-strictly
                    :min="0"
                    :max="255"
                    :step="1"
                    :disabled="onlyReceive || oneChannel.sendTimeLimiter === 0"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
        <!--虚拟集群-->
        <el-card
          v-show="showVirtualCluster"
          shadow="never"
          class="write-freq-component virtual-cluster-container"
        >
          <virtualCluster930svtV0
            ref="virtualCluster"
            v-model="virtualCluster"
            labelPosition="top"
            :selectDeviceData="selectDeviceData"
            :selectedAddressBook="selectedAddressBook"
            :channelDataList="channelDataList"
          />
        </el-card>
        <!-- 站点信息 -->
        <el-card
          v-show="showSiteInfo"
          shadow="never"
          class="write-freq-component virtual-cluster-site-container"
        >
          <siteInfo
            ref="SiteInfo"
            v-model="siteInfoList[siteInfoIndex]"
            @name-change="siteInfoNameChange"
            @update:model-value="siteInfoListChange"
          />
        </el-card>
      </main>
    </section>
    <write-freq-footer
      :is-reading="isReading"
      :is-writing="isWriting"
      :disable-read="disReadBtn"
      :disable-write="disWriteBtn"
      @new-config="newConfig"
      @read-config="readDataConfig"
      @write-config="writeInFrequency"
      @export-config="exportConfig"
      @import-config="importConfig"
    />
  </div>
</template>

<script>
import TableTree from '@/components/common/tableTree'
import { cloneDeep, merge } from 'lodash'
import bftree from '@/utils/bftree'
import bfutil, { DeviceTypes } from '@/utils/bfutil'
import bfNotify from '@/utils/notify'
import validateRules from '@/utils/validateRules'
import {
  AnalogCode,
  ButtonKeys,
  CallType,
  createDefaultArgs,
  DigitalCode,
  getClassInstance,
  InsertDataAfterClear,
  Model,
  OperationOrderTable,
  SoftKeyCallType,
  TableIndex,
} from '@/writingFrequency/interphone/R930SVT0'
import {
  filterChannelIdWhenDeviceChanged,
  fixPowerOnPassword,
  fixProgramPassword,
  generateEnumObject,
  resetDigitalAlertReplyChannel,
  gpsSettingsQueryCmdInputEvent,
} from '@/writingFrequency/interphone/common'
import toolMixin from '@/writingFrequency/interphone/TD811ToolMixin'
import commonToolMixin from '@/writingFrequency/interphone/commonToolMixin'
import deviceInfo from '@/components/interphoneWf/common/deviceInfo'
import selectDevice from '@/components/interphoneWf/common/selectDevice'
import WriteFreqFooter from '@/components/interphoneWf/common/writeFreqFooter.vue'
import { defineAsyncComponent } from 'vue'

const Password = {
  md5Key: '',
}
const MenuTreeContextmenu = generateEnumObject({
  ADD: 1,
  DELETE: 2,
  ORDER: 3,
})
const GeneralSettings = createDefaultArgs({ type: TableIndex.GeneralSettings })
const UISettings = createDefaultArgs({ type: TableIndex.UISettings })
const ButtonDefined = Object.assign(
  createDefaultArgs({ type: TableIndex.KeysSettings }),
  {
    // 短-长按功能, [橙色键,功能1键,功能2键 ,方向上键,方向下键 ,确认键,返回键,拨号键,挂机键]
    shortLongKeyDefine: [
      {
        short: 1,
        long: 2,
      },
      {
        short: 0,
        long: 0,
      },
      {
        short: 0,
        long: 0,
      },
      {
        short: 8,
        long: 22,
      },
      {
        short: 9,
        long: 23,
      },
      {
        short: 14,
        long: 0,
      },
      {
        short: 18,
        long: 0,
      },
      {
        short: 17,
        long: 0,
      },
      {
        short: 13,
        long: 0,
      },
    ],
  },
)
const MenuSettings = createDefaultArgs({ type: TableIndex.MenuSettings })
const GpsSettings = createDefaultArgs({ type: TableIndex.GpsSettings })
const AloneWork = createDefaultArgs({ type: TableIndex.AloneWorkSettings })
const UpsideDown = createDefaultArgs({ type: TableIndex.UpsideDownSettings })
const DMRConfig = createDefaultArgs({ type: TableIndex.DmrConfigSettings })
const EncryptConfig = createDefaultArgs({
  type: TableIndex.EncryptionConfigSettings,
})
const OneEncryptOption = createDefaultArgs({
  type: TableIndex.EncryptionListSettings,
})
const EncryptARC4List = createDefaultArgs({
  type: TableIndex.EncryptionARC4List,
})
const EncryptARSList = createDefaultArgs({ type: TableIndex.EncryptionARSList })
const ScanConfig = createDefaultArgs({ type: TableIndex.ScanConfigSettings })
const ScanListItem = createDefaultArgs({ type: TableIndex.ScanListSettings })
const RoamConfig = createDefaultArgs({ type: TableIndex.RoamConfigSettings })
const RoamListItem = createDefaultArgs({ type: TableIndex.RoamListSettings })
const PatrolConfig = createDefaultArgs({
  type: TableIndex.PatrolSystemSettings,
})
const DigitalAlert = createDefaultArgs({
  type: TableIndex.DigitalAlarmSettings,
})
const AnalogAlert = createDefaultArgs({ type: TableIndex.AnalogAlarmSettings })
const ZoneData = createDefaultArgs({ type: TableIndex.ZoneSettings })
const Channel = Object.assign(
  createDefaultArgs({ type: TableIndex.ChannelSettings }),
  { channelScanList: 0xff },
)
const VirtualCluster = createDefaultArgs({ type: TableIndex.VirtualCluster })
let lastClickNodeKey = null
const DefaultSiteInfo = createDefaultArgs({ type: TableIndex.SiteInfo })
// 400MHz
const DefaultFrequency = 400

export default {
  name: 'TD930SVTR7F',
  mixins: [commonToolMixin, toolMixin],
  data() {
    return {
      selectedDeviceDmrId: '',
      isReading: false,
      isWriting: false,
      menuTreeId: 'TD811MenuTree',
      selectMenu: this.string(TableIndex.DeviceInfo),

      // 设备信息
      deviceModel: Model,
      deviceWriteInfo: {},
      // 身份信息
      identityInfo: {},
      // 编程密码
      programReadPassword: cloneDeep(Password),
      programWritePassword: cloneDeep(Password),
      isModifyReadPwassword: false,
      isModifyWritePwassword: false,
      // 常规设置
      generalSettings: cloneDeep(GeneralSettings),
      // UI设置
      uiSettings: cloneDeep(UISettings),
      // 按键设置
      buttonDefined: cloneDeep(ButtonDefined),
      // 短信
      refSms: 'shortMessage',
      smsMaxSize: 10,
      smsContent: [],
      // DMR基础配置
      dmrConfig: cloneDeep(DMRConfig),
      // 加密配置
      encryptListName: 'xor', // xor, arc4, ars
      encryptConfig: cloneDeep(EncryptConfig),
      // 加密列表 最大数量: 32
      encryptListLimit: 32,
      encryptList: [this.createEncryptItem()],
      encryptARC4List: [this.createARC4Key()],
      encryptARSList: [this.createARSKey()],
      // 菜单设置
      menuSettings: cloneDeep(MenuSettings),
      // 卫星定位
      gpsSettings: cloneDeep(GpsSettings),
      // 倒放
      upsideDown: cloneDeep(UpsideDown),
      // 单独工作
      aloneWork: cloneDeep(AloneWork),
      // 数字报警
      digitalAlert: cloneDeep(DigitalAlert),
      digitalAlertList: [],
      currDigitalAlertId: -1,
      digitalAlertListLimit: 4,
      // 模拟警报
      analogAlert: cloneDeep(AnalogAlert),
      analogAlertList: [],
      currAnalogAlertId: -1,
      analogAlertListLimit: 4,
      // 数字通讯录
      selectedAddressBook: [],
      addressBookCache: [],
      originAddressBook: [],
      addrBookTreeId: 'TD811AddressBookTree',
      // 电话本
      phoneBookTreeId: 'TD811PhoneBook',
      phoneBook: [],
      // 接收组
      rxGroupList: [],
      refReceiveGroup: 'receiveGroup',
      // 信道
      channelDataList: [],
      channelDataListCache: [],
      channelLimit: 1024,
      currChannelId: -1,
      oneChannel: cloneDeep(Channel),
      // 频率偏移值
      freqOffset: 10,
      // 区域
      zoneDataList: [],
      zoneLimit: 128,
      currZoneId: -1,
      zoneListLimit: 64,
      // 扫描组
      scanConfig: cloneDeep(ScanConfig),
      scanGroup: [],
      currScanGroupId: 0,
      scanGroupLimit: 32,
      oneScanGroupLimit: 16,
      // 漫游
      roamConfig: cloneDeep(RoamConfig),
      roamGroup: [],
      currRoamGroupId: 0,
      roamGroupLimit: 32,
      oneRoamGroupLimit: 16,
      // 系统功能巡查配置
      sendCount: 3,
      patrolConfig: cloneDeep(PatrolConfig),
      wfCountLimit: 3,
      // 虚拟集群
      virtualCluster: cloneDeep(VirtualCluster),
      // 站点信息,最多16个
      siteInfoList: [],
      currSiteId: 0,
      siteInfoLimit: 16,
    }
  },
  methods: {
    resetSVTSiteInfo() {
      this.oneChannel.SVTSiteInfo = 0xff
    },
    gpsSettingsQueryCmdInputEvent,
    fixPowerOnValue(value) {
      this.generalSettings.powerOnPwd = fixPowerOnPassword(value)
    },
    // 信道加密配置，加密类型变更，重置加密列表值
    encryptTypeChange() {
      this.oneChannel.encryptKey = 0
    },
    // 自动补全指定长度的字符串
    autoFillBytes(data, len, byte = '0', reverse = false) {
      if (data.length < len) {
        return reverse ? data.padEnd(len, byte) : data.padStart(len, byte)
      }
      return data
    },
    replaceInvalidByte(value, reg) {
      // 不是正则或字符串，则返回原数据
      if (reg instanceof RegExp || typeof reg === 'string') {
        return value.replace(reg, '')
      }

      return value
    },
    // 生成10位的16进制字符串
    genRandom(radix = 16) {
      return Math.random().toString(radix).slice(2, 12)
    },
    // Generate a string of specified length
    genSpecifyRandom(len = 10, radix = 16) {
      let key = this.genRandom(radix)
      while (key.length < len) {
        key += this.genRandom(radix)
      }
      if (key.length > len) {
        key = key.slice(0, len)
      }

      return key
    },
    createARC4Key() {
      const oneARC4 = cloneDeep(EncryptARC4List)
      const usedId = (this.encryptARC4List || []).map(item => item.keyId)
      const keyId = this.nextId(usedId)
      oneARC4.keyId = keyId
      oneARC4.keyName =
        this.$t('writeFreq.encryptionKey') + '-ARC4 ' + (keyId + 1)
      // 密钥值 5B+1B 设置具体的密钥值，长度受密钥长度参数控制。采用BCD码显示
      oneARC4.keyValue = this.genSpecifyRandom(10, 16).toUpperCase()

      return oneARC4
    },
    deleteEncryptARC4Item(row, index) {
      if (this.encryptARC4List.includes(row)) {
        this.encryptARC4List.splice(index, 1)
      }
    },
    addEncryptARC4Item() {
      if (this.encryptARC4List.length >= this.encryptListLimit) {
        return
      }
      this.encryptARC4List.push(this.createARC4Key())
    },
    encryptARC4ValueChanged(encryptItem) {
      if (!encryptItem) {
        return
      }
      // 密钥不存在，自动生成新密钥
      if (!encryptItem.keyValue) {
        encryptItem.keyValue = this.genSpecifyRandom(10, 16).toUpperCase()
        return
      }

      encryptItem.keyValue = this.autoFillBytes(
        encryptItem.keyValue,
        10,
        'F',
        true,
      ).toUpperCase()
    },
    encryptARC4ValueInput(encryptItem) {
      if (!encryptItem) {
        return
      }
      // 替换非法字符
      encryptItem.keyValue = this.replaceInvalidByte(
        encryptItem.keyValue,
        /[^0-9A-Fa-f]/g,
      )
    },
    createARSKey() {
      const oneARS = cloneDeep(EncryptARSList)
      const usedId = (this.encryptARSList || []).map(item => item.keyId)
      const keyId = this.nextId(usedId)
      oneARS.keyId = keyId
      oneARS.keyName =
        this.$t('writeFreq.encryptionKey') + '-AES ' + (keyId + 1)
      // 密钥值 32B+1B 设置具体的密钥值，长度受密钥长度参数控制。采用BCD码显示
      oneARS.keyValue = this.genSpecifyRandom(64, 16).toUpperCase()

      return oneARS
    },
    deleteEncryptARSItem(row, index) {
      if (this.encryptARSList.includes(row)) {
        this.encryptARSList.splice(index, 1)
      }
    },
    addEncryptARSItem() {
      if (this.encryptARSList.length >= this.encryptListLimit) {
        return
      }
      this.encryptARSList.push(this.createARSKey())
    },
    encryptARSValueChanged(encryptItem) {
      if (!encryptItem) {
        return
      }
      // 密钥不存在，自动生成新密钥
      if (!encryptItem.keyValue) {
        encryptItem.keyValue = this.genSpecifyRandom(64, 16).toUpperCase()
        return
      }

      encryptItem.keyValue = this.autoFillBytes(
        encryptItem.keyValue,
        64,
        'F',
        true,
      ).toUpperCase()
    },
    encryptARSValueInput(encryptItem) {
      if (!encryptItem) {
        return
      }
      // 替换非法字符
      encryptItem.keyValue = this.replaceInvalidByte(
        encryptItem.keyValue,
        /[^0-9A-Fa-f]/g,
      )
    },

    // IP站点连接取消时，需要重置漫游组已选信道列表，没有开启IP站点连接的信道，不能被漫游组选中
    resetRoamGroupSelectedChannel() {
      for (let i = 0; i < this.roamGroup.length; i++) {
        const data = this.roamGroup[i]
        data.roamChList = data.roamChList.filter(chId => {
          return this.oneChannel.chId !== chId
        })
        data.roamChCount = data.roamChList.length
      }
    },
    throughModeChange(v) {
      // 选中直通模式时，彩色码最大为14
      if (v) {
        this.oneChannel.colorCode = Math.min(
          this.oneChannel.colorCode,
          this.maxColorCode,
        )
      }
    },
    virtualClusterChange(v) {
      if (v) {
        // 当勾选虚拟集群后，不允许配置扫描/漫游列表、IP站点互联
        this.oneChannel.scanRoamListType = 0xff
        this.oneChannel.scanList = 0xff
        this.oneChannel.channelScanList = 0xff
        this.oneChannel.callingSettings.ipSiteConnect = false

        // 当勾选虚拟集群后，时隙选择默认设置为虚拟集群，指定发射时隙默认为无，且均不可配置
        this.oneChannel.timeSlot = 2
        this.oneChannel.specifyTxTimeSlot = 0

        // 不允许脱网
        this.oneChannel.baseSettings.allowOffline = false
        // “发送准许条件”和“呼叫中发射准许条件”不允许配置“可用彩色码”选项
        this.oneChannel.permitConditions = 0
        this.oneChannel.callingTxPermitCondition = 0
        // 不允许配置“数字紧急警报提示”、“数字紧急警报确认”、“数字紧急呼叫提示”和“数字紧急警报系统”
        this.oneChannel.baseSettings.emergencyAlarmIndication = false
        this.oneChannel.baseSettings.emergencyAlarmConfirm = false
        this.oneChannel.baseSettings.emergencyCallTip = false
        this.oneChannel.emergencySysId = 0xff
      } else {
        // 取消选择时，需要将虚拟集群中包含该信道的数据删除
        const index = this.virtualCluster.channelList.findIndex(
          v => v === this.oneChannel.chId,
        )
        if (index !== -1) this.virtualCluster.channelList.splice(index, 1)

        // 取消本地呼叫
        this.oneChannel.baseSettings.localCall = false
      }
    },
    formatUiSettingVolume(v) {
      return v === 0 ? this.$t('writeFreq.currentVolume') : v
    },
    frequencyHz2Mhz: bfutil.frequencyHz2Mhz,
    frequencyMhz2Hz: bfutil.frequencyMhz2Hz,
    readPasswordInput(pwd) {
      if (this.isModifyReadPwassword) {
        return
      }
      const md5Key = (this.programReadPassword.md5Key = '')
      this.readPasswordChanged(md5Key)
    },
    readPasswordChanged(pwd) {
      this.isModifyReadPwassword = true
    },
    writePasswordInput(pwd) {
      this.programWritePassword.md5Key = fixProgramPassword(pwd)
      if (this.isModifyWritePwassword) {
        return
      }
      const md5Key = (this.programWritePassword.md5Key = '')
      this.writePasswordChanged(md5Key)
    },
    writePasswordChanged(pwd) {
      this.isModifyWritePwassword = true
    },
    // 切换选中的设备时，同步界面中的配置数据
    mergeGeneralSettings(device) {
      this.generalSettings = Object.assign(this.generalSettings, {
        deviceName: device.selfId,
      })
    },
    mergeDmrConfigSettings(device) {
      this.dmrConfig = Object.assign(this.dmrConfig, {
        dmrId: parseInt(device.dmrId, 16),
      })
    },
    getOldChannelFromCache(chId) {
      for (let i = 0; i < this.channelDataListCache.length; i++) {
        const data = this.channelDataListCache[i]
        if (data.chId === chId) {
          return cloneDeep(data)
        }
      }
      return {}
    },
    createChannelName(chType = 0, chId = 0) {
      return `${this.$t('dialog.channel')} ${chId + 1}`
      // const keys = {
      //   0: this.$t('writeFreq.digitalChannel'),
      //   1: this.$t('writeFreq.analogChannel'),
      //   2: this.$t('writeFreq.digitalAnalogChannel'),
      // }
      // return `${keys[chType]} ${chId + 1}`
    },
    createOneChannel(usedIdList = []) {
      const data = cloneDeep(Channel)
      const id = this.nextId(usedIdList, this.channelLimit)
      data.chId = id
      data.chName = this.createChannelName(data.chType, id)
      // 重置信道默认频率
      const minFreq = this.frequencyMhz2Hz(this.deviceWriteInfo.minFrequency)
      data.rxFreq = minFreq || data.rxFreq
      data.txFreq = minFreq || data.txFreq

      return data
    },
    initChannels() {
      // 如果没有合法有效的信道，则向用户提示
      if (!this.selectedChannels.length) {
        this.$nextTick(() => {
          bfNotify.messageBox(this.$t('writeFreq.notHaveValidChannel'), 'error')
        })
        return
      }

      // 区域数据
      const zoneDataMap = {}
      const zoneNodes = {}
      const zoneUsedIdList = new Set()
      const zoneDataList = []
      const zoneConfigCache = {}

      // 信道数据
      const usedIdList = new Set()
      const channelDataList = []

      for (let i = 0; i < this.selectedChannels.length; i++) {
        const channel = this.selectedChannels[i]
        // 缓存信道区域配置，同一个区域配置只查找一次
        const zoneConfig =
          zoneConfigCache[channel.zoneRid] ||
          bfglob.gchannelZone.get(channel.zoneRid)
        if (!zoneConfig) {
          continue
        }
        zoneConfigCache[channel.zoneRid] = zoneConfig

        // 只处理当前机型下的区域和信道
        // 生成区域数据
        let zoneData = zoneDataMap[channel.zoneRid]
        if (!zoneData) {
          zoneData = this.createZoneData([...zoneUsedIdList])
          zoneData.name = zoneConfig.zoneTitle
          zoneDataList.push(zoneData)
          zoneUsedIdList.add(zoneData.zoneId)
          zoneDataMap[channel.zoneRid] = zoneData
        }
        // 每个区域最多有16个信道
        if (zoneData.list.length >= this.zoneListLimit) {
          continue
        }

        // 生成信道的节点数据，初始化时，默认为数字信道
        let channelData = this.createOneChannel([...usedIdList])
        const chId = channel.no - 1
        // 判断信道ID是否超出机型的信道范围
        if (chId >= this.channelLimit) {
          continue
        }
        // 合并缓存参数
        channelData = merge(
          channelData,
          this.getOldChannelFromCache(chId) || cloneDeep(this.oneChannel),
        )
        // 重置信道ID和名称
        channelData.chId = chId
        channelData.chName = this.createChannelName(channelData.chType, chId)
        // 设置接收组和发射组ID
        channelData.rxGroupId = this.getRxGroupId(channelData.chId)
        channelData.defaultTarget = this.getDefaultAddress(channel.sendGroup)
        // 缓存区域的ID到信道中
        channelData.zoneId = zoneData.zoneId
        channelData.zoneRid = channel.zoneRid
        usedIdList.add(chId)
        channelDataList.push(channelData)

        // 设置区域管辖信道数据参数
        this.setZoneDataList(zoneData, channelData.chId)

        // 生成fancytree节点
        // 区域节点
        if (!zoneNodes[channel.zoneRid]) {
          zoneNodes[channel.zoneRid] = this.createZoneLeafNodeData(zoneData)
        }
        // 直接将信道节点数据放到区域节点数据下，以便快速生成菜单节点
        if (!zoneNodes[channel.zoneRid].children) {
          zoneNodes[channel.zoneRid].children = []
        }
        zoneNodes[channel.zoneRid].children.push(
          this.createChannelNodeData(channelData),
        )
      }

      this.zoneDataList = zoneDataList
      this.channelDataList = channelDataList
      const showData = channelDataList.find(
        data => data.chId === this.currChannelId,
      )
      if (showData) {
        this.oneChannel = showData
      }

      // 挂载区域节点，信道节点数据已经添加到区域节点的children
      const zoneParentNode = this.getNodeByKey(
        TableIndex.ZoneSettings + '-base',
      )
      if (zoneParentNode) {
        this.addNodeChildren(
          zoneParentNode,
          Object.keys(zoneNodes).map(key => zoneNodes[key]),
        )
        zoneParentNode.setExpanded(true)
      }
    },
    filterScanListChannelId() {
      const channelIdList = this.currentChannelIdList
      this.scanGroup = this.scanGroup.map(item => {
        item.membersList = filterChannelIdWhenDeviceChanged(
          channelIdList,
          item.membersList,
        )
        item.memberCount = item.membersList.length
        return item
      })
    },
    filterRoamListChannelId() {
      const channelIdList = this.currentChannelIdList
      this.roamGroup = this.roamGroup.map(item => {
        item.roamChList = filterChannelIdWhenDeviceChanged(
          channelIdList,
          item.roamChList,
        )
        item.roamChCount = item.roamChList.length
        return item
      })
    },
    resetDigitalAlarmReplyChannel() {
      const channelIdList = this.currentChannelIdList
      this.digitalAlertList = this.digitalAlertList.map(data => {
        data.replyChannel = resetDigitalAlertReplyChannel(
          channelIdList,
          data.replyChannel,
        )
        return data
      })
      this.analogAlertList = this.analogAlertList.map(data => {
        data.replyChannel = resetDigitalAlertReplyChannel(
          channelIdList,
          data.replyChannel,
        )
        return data
      })
    },
    async syncDeviceDataIntoConfig(device) {
      this.mergeGeneralSettings(device)
      this.mergeDmrConfigSettings(device)
      // 接收组
      await this.initRxGroupList()
      // 区域/信道
      this.initChannels()
      // 扫描组过滤不存在的信道ID
      this.filterScanListChannelId()
      // 漫游组过滤不存在的信道ID
      this.filterRoamListChannelId()
      // 数字报警重置回复信道参数
      this.resetDigitalAlarmReplyChannel()

      this.$nextTick(() => {
        if (!lastClickNodeKey) {
          return
        }
        this.manuallyTriggerNodeClick(this.getNodeByKey(lastClickNodeKey))
      })

      // 处理虚拟群集的归属组
      //所属归属组，从组呼联系人选择, 所以联系人中必须得选中归属组
      if (device.deviceType === DeviceTypes.VirtualClusterDevice) {
        const devGroup = device.devGroup
        // 归属组在通讯录上设置选中且禁用状态
        bfglob.emit(
          `${this.addrBookTreeId}:unselectableNode`,
          [devGroup],
          () => {
            // 等待通讯录渲染完成
            setTimeout(() => {
              this.$nextTick(() => {
                const book = this.selectedAddressBook.find(
                  item => item.dmrId === devGroup,
                )
                // 设置归属组在通讯录上的ID
                if (book) {
                  this.virtualCluster.vcGroupId = book.id
                }
              })
            }, 0)
          },
        )
      }
    },
    // 清除通讯录被禁用的状态
    clearTreeNodeUnselectable() {
      // 清除通讯录被禁用和非系统通讯数据的节点
      this.addressBookComp && this.addressBookComp.removeNotInSystemNodes()
      this.selectedAddressBook.forEach(item => {
        bftree.nodeUnselectable(this.addrBookTreeId, item.nodeKey)
      })
    },
    clearPrivateConfig() {
      // 重置密码是否有修改标记
      this.isModifyReadPwassword = false
      this.isModifyWritePwassword = false
      // 清除常规设置的设备名称
      this.generalSettings.deviceName = ''
      this.generalSettings.preSetChannel = cloneDeep(
        GeneralSettings.preSetChannel,
      )
      // DMR配置
      this.dmrConfig.dmrId = 0
      // 接收组
      this.receiveGroupComp && this.receiveGroupComp.resetRxGroupList()
      // 区域
      this.resetZoneDataList()
      // 信道
      this.resetChannelDataList()
      this.clearTreeNodeUnselectable()
      if (this.phoneBookComp) {
        this.phoneBookComp.removeNotInSystemNodes()
      }
    },
    // cleanAll 标记是否清除全部配置
    async clearDeviceDataConfig(cleanAll = false) {
      // 必须清除的数据，接收组、区域、信道等私有数据，包括一些标记参数
      this.clearPrivateConfig()

      this.initEncryptConfig()
      this.initScanGroupList()
      this.initRoamGroupList()
      this.initDigitAlertList()
      this.initAnalogAlertList()
      this.initSiteInfoList()

      // 可选的清除数据，常规设置、菜单、按键定义、警报、通讯录、电话本、短信等通用数据
      if (!cleanAll) {
        return
      }
      this.originAddressBook = []
      this.selectedAddressBook = []
      this.addressBookCache = []
      this.addressBookComp && this.addressBookComp.treeReload(true)
      this.phoneBook = []
      this.phoneBookComp && this.phoneBookComp.treeReload(true)
    },
    initSmsData() {
      this.smsContent = []
    },
    initEncryptConfig() {
      this.encryptConfig = cloneDeep(EncryptConfig)
      this.encryptList = [this.createEncryptItem()]
      this.encryptARC4List = [this.createARC4Key()]
      this.encryptARSList = [this.createARSKey()]
    },
    initRxGroupList() {
      if (!this.receiveGroupComp) {
        return []
      }
      return this.receiveGroupComp.initRxGroupList(this.selectedChannels)
    },
    newOneScanGroup() {
      const data = cloneDeep(ScanListItem)
      data.scanId = this.nextId(this.scanGroupUsedId, this.scanGroupLimit)
      data.name = `${this.$t('writeFreq.scanningGroup')} ${data.scanId + 1}`

      return data
    },
    initScanGroupList() {
      // 删除节点，通过父节点，直接删除所有子节点数据
      this.removeMenuTreeNodeChildren(this.string(TableIndex.ScanListSettings))

      this.scanGroup = []
      const data = this.newOneScanGroup()
      this.scanGroup.push(data)
      this.currScanGroupId = data.scanId
      this.addOneScanGroupNode(data)
    },
    newOneRoamGroup() {
      const data = cloneDeep(RoamListItem)
      data.roamId = this.nextId(this.roamGroupUsedId, this.roamGroupLimit)
      data.name = `${this.$t('writeFreq.roamingGroup')} ${data.roamId + 1}`

      return data
    },
    initRoamGroupList() {
      // 删除节点，通过父节点，直接删除所有子节点数据
      this.removeMenuTreeNodeChildren(this.string(TableIndex.RoamListSettings))

      this.roamGroup = []
      const data = this.newOneRoamGroup()
      this.roamGroup.push(data)
      this.currRoamGroupId = data.roamId
      this.addOneRoamGroupNode(data)
    },

    /**
     * 当站点信息列表删除其中一个站点配置，需要同步检查所有信道使用该配置的参数
     * @param siteInfo 被删除的站点信息
     */
    syncChannelSvtSiteInfoWhenDeleteSiteInfo(siteInfo) {
      for (let i = 0; i < this.channelDataList.length; i++) {
        const channel = this.channelDataList[i]
        if (siteInfo.id !== channel.SVTSiteInfo) {
          continue
        }
        channel.SVTSiteInfo = 0xff
      }
    },
    /**
     * 站点信息列表参数变更事件，需要检查所有信道的虚拟集群站点配置
     * @param siteInfo 当前编辑的站点信息
     */
    siteInfoListChange(siteInfo) {
      for (let i = 0; i < this.channelDataList.length; i++) {
        const channel = this.channelDataList[i]
        if (siteInfo.id !== channel.SVTSiteInfo) {
          continue
        }

        // 当前信道的发射与接收频率
        const txFreq = channel.txFreq
        const rxFreq = channel.rxFreq
        // 判断站点信息中对应的频率是否与当前信道的频率相同，如果不同，重置信道的站点信息配置参数
        if (
          !siteInfo.frequency.some(
            freq => freq.rxFreq === rxFreq && freq.txFreq === txFreq,
          )
        ) {
          channel.SVTSiteInfo = 0xff
        }
      }
    },

    initSiteInfoList() {
      // 删除节点，通过父节点，直接删除所有子节点数据
      this.removeMenuTreeNodeChildren(this.string(TableIndex.SiteInfo))

      this.siteInfoList = []
      const data = this.newOneSiteInfo()
      this.siteInfoList.push(data)
      this.currSiteId = data.id
      this.addOneSiteInfoNode(data)
    },
    initDigitAlertList() {
      // 删除旧节点
      const parentNodeKey = this.string(TableIndex.DigitalAlarmSettings)
      this.removeMenuTreeNodeChildren(parentNodeKey)

      // 重新初始化数据
      this.digitalAlertList = []
      this.digitalAlertList.push(this.addDigitalAlertData())
      this.loadDigitalAlertNode()
    },
    initAnalogAlertList() {
      // 删除旧节点
      const parentNodeKey = this.string(TableIndex.AnalogAlarmSettings)
      this.removeMenuTreeNodeChildren(parentNodeKey)

      // 重新初始化数据
      this.analogAlertList = []
      this.analogAlertList.push(this.addAnalogAlertData())
      this.loadAnalogAlertNode()
    },
    setZoneDataList(data, chId) {
      const list = new Set(data.list)
      list.add(chId)
      data.list = [...new Set(list)]
      data.count = data.list.length
    },
    createZoneData(usedIdList = []) {
      const data = cloneDeep(ZoneData)
      const id = this.nextId(usedIdList, this.zoneLimit)
      data.zoneId = id
      data.name = `${this.$t('writeFreq.zone')} ${id + 1}`
      return data
    },
    clearZoneNodes() {
      this.removeMenuTreeNodeChildren(TableIndex.ZoneSettings + '-base')
    },
    resetZoneDataList() {
      this.clearZoneNodes()
      this.zoneDataList = []
    },
    resetChannelDataList() {
      // 删除信道菜单节点下所有子节点
      this.clearZoneNodes()
      if (this.channelDataList.length) {
        this.channelDataListCache = cloneDeep(this.channelDataList)
      }
      this.channelDataList = []
    },
    removeMenuTreeNodeChildren(key) {
      const parentNode = this.getNodeByKey(key)
      if (parentNode) {
        parentNode.expanded = true
        parentNode.removeChildren()
        this.updateViewport()
      }
    },
    loadZoneNodes({ dataList = [], parentKey = '', createNode, isChannel }) {
      if (typeof createNode !== 'function' || !dataList.length) {
        return
      }

      const zonesMap = {}
      for (let i = 0; i < dataList.length; i++) {
        const data = dataList[i]
        const key = isChannel
          ? `${parentKey}:${this.channelInZoneIndex[data.chId]}`
          : parentKey
        if (!zonesMap[key]) {
          zonesMap[key] = []
        }
        zonesMap[key].push(createNode(data))
      }

      const parentNodeCache = {}
      const zonesMapKeys = Object.keys(zonesMap)
      for (let i = 0; i < zonesMapKeys.length; i++) {
        const key = zonesMapKeys[i]
        const parentNode =
          parentNodeCache[key] ||
          (parentNodeCache[key] = this.getNodeByKey(key))
        if (!parentNode) {
          continue
        }
        this.addNodeChildren(parentNode, zonesMap[key])
        parentNode.setExpanded(true)
      }
    },

    // 区域数据
    jumpToChannelSettings(key) {
      bftree.gotoNodeLocation(this.menuTreeId, key, { isActive: true })
    },
    zoneDataRowDblclick(row) {
      const showChannel = () => {
        this.setChannelData(row)
        this.jumpToChannelSettings(`${TableIndex.ChannelSettings}:${row.chId}`)
      }
      if (row.chId === this.oneChannel.chId) {
        showChannel()
      }
      // 如果信道设置的数据在信道列表中，则需要验证表单通过后再切换信道
      this.validateOneChannel()
        .then(() => {
          showChannel()
        })
        .catch(() => {
          // 提示对应的三级区域有信道未命名
          const zoneLeafData = this.zoneDataIndex[this.oneChannel.zoneId]
          if (zoneLeafData) {
            bfNotify.messageBox(
              this.$t('writeFreq.correctChDataTip', {
                name: zoneLeafData.zoneName,
              }),
              'warning',
            )
          }
        })
    },
    createZoneLeafNodeData(data) {
      const parentNodeKey = this.string(TableIndex.ZoneSettings)
      const key = `${parentNodeKey}:${data.zoneId}`
      return {
        title: data.name,
        folder: true,
        expanded: true,
        key: key,
        icon: false,
        selected: false,
        origin: data,
        children: [],
      }
    },
    loadZoneLeafNodes(dataList = this.zoneDataList) {
      this.loadZoneNodes({
        dataList,
        parentKey: this.string(TableIndex.ZoneSettings) + '-base',
        createNode: this.createZoneLeafNodeData,
      })
    },
    zoneRootDataDblclick(row) {
      const key = `${TableIndex.ZoneSettings}:${row.zoneId}`
      bftree.gotoNodeLocation(this.menuTreeId, key, { isActive: true })
    },

    // 信道设置
    createChannelNodeData(data) {
      const parentNodeKey = this.string(TableIndex.ChannelSettings)
      const key = `${parentNodeKey}:${data.chId}`
      return {
        title: data.chName,
        folder: false,
        expanded: false,
        key: key,
        icon: false,
        selected: false,
        origin: data,
      }
    },
    loadChannelNodes(dataList = this.channelDataList) {
      this.loadZoneNodes({
        dataList,
        parentKey: this.string(TableIndex.ZoneSettings),
        createNode: this.createChannelNodeData,
        isChannel: true,
      })
    },
    updateOneChannelNode(data) {
      this.updateNodeTitle({
        key: `${TableIndex.ChannelSettings}:${data.chId}`,
        title: data.chName,
        data,
      })
    },
    chNameChanged(val) {
      if (!val) {
        this.oneChannel.chName = this.createChannelName(
          this.oneChannel.chType,
          this.oneChannel.chId,
        )
      }
      this.updateOneChannelNode(this.oneChannel)
    },
    chTypeChanged(val) {
      this.oneChannel.baseSettings.networking = false
      this.oneChannel.callingSettings.ipSiteConnect = false

      // 信道类型变更,需要重置站点参数选择
      this.oneChannel.SVTSiteInfo = 0xff

      // 数模信道，需要重置部分参数
      if (this.isDAChannel) {
        this.oneChannel.rxSquelchMode = 1
      }
    },
    timeSlotChanged(val) {
      this.oneChannel.specifyTxTimeSlot = val === 2 ? 0 : val + 1
      if (val !== 2) {
        this.oneChannel.encryptionSettings.duplexModel = 0
      }
    },
    getFreqRangeMaxValue(freq, unsigned = true) {
      for (let i = 0; i < this.deviceWriteInfo.frequencyRange.length; i++) {
        const range = this.deviceWriteInfo.frequencyRange[i]
        const min = bfutil.frequencyMhz2Hz(range.min)
        const max = bfutil.frequencyMhz2Hz(range.max)
        if (freq >= min && freq <= max) {
          return unsigned ? max : min
        }
      }
      return freq
    },
    setChannelData(data) {
      this.oneChannel = data
    },
    validateOneChannel() {
      if (!this.channelSettingComp) {
        return Promise.reject()
      }
      return this.channelSettingComp.validate()
    },

    // 加密配置
    encryptKeyNameChanged(encryptItem, suffix = '') {
      if (!encryptItem) {
        return
      }
      if (!encryptItem.keyName) {
        const _suffix = suffix ? '-' + suffix : ''
        encryptItem.keyName =
          this.$t('writeFreq.encryptedList') +
          _suffix +
          ' ' +
          (encryptItem.keyId + 1)
      }
    },
    // 检测加密密钥输入的字符是否符合规则
    encryptKeyValueInput(encryptItem) {
      if (!encryptItem) {
        return
      }
      // 替换非法字符
      encryptItem.keyValue = this.replaceInvalidByte(
        encryptItem.keyValue,
        /[^0-9A-Za-z]/g,
      )
    },
    encryptKeyValueChanged(encryptItem) {
      if (!encryptItem) {
        return
      }
      // 密钥不存在，自动生成新密钥
      if (!encryptItem.keyValue) {
        encryptItem.keyValue = this.genSpecifyRandom(10, 32).toUpperCase()
        return
      }

      encryptItem.keyValue = this.autoFillBytes(
        encryptItem.keyValue,
        10,
        'F',
        true,
      )
    },
    nextId(usedId, limit = 32) {
      let id = 0
      while (id < limit) {
        if (!usedId.includes(id)) {
          return id
        }
        id++
      }
      return id
    },
    deleteEncryptItem(row, index) {
      if (this.encryptList.includes(row)) {
        this.encryptList.splice(index, 1)
      }
    },
    addEncryptItem() {
      if (this.encryptList.length >= this.encryptListLimit) {
        return
      }
      const oneEncryptOption = this.createEncryptItem()
      this.encryptList.push(oneEncryptOption)
    },
    createEncryptItem() {
      const oneEncryptOption = cloneDeep(OneEncryptOption)
      const usedId = (this.encryptList || []).map(item => item.keyId)
      const keyId = this.nextId(usedId)
      oneEncryptOption.keyId = keyId
      oneEncryptOption.keyName =
        this.$t('writeFreq.encryptionKey') + ' ' + (keyId + 1)
      oneEncryptOption.keyValue = this.genSpecifyRandom(10, 32).toUpperCase()
      return oneEncryptOption
    },

    // 按键设置
    syncShortPressDefine(row) {
      // 如果短按是紧急模式开/关,则长按自动设置为紧急模式关/开
      if (row.short === ButtonKeys.WARNING_ON) {
        row.long = ButtonKeys.WARNING_OFF
      } else if (row.short === ButtonKeys.WARNING_OFF) {
        row.long = ButtonKeys.WARNING_ON
      } else if (
        row.long === ButtonKeys.WARNING_ON ||
        row.long === ButtonKeys.WARNING_OFF
      ) {
        row.long = ButtonKeys.NONE
      } else if (row.short === ButtonKeys.TRANSIENT_MONITOR) {
        // 功能键设置暂态监听和暂态静噪打开 应该是短按长按都是同一个
        row.long = ButtonKeys.TRANSIENT_MONITOR
      } else if (row.short === ButtonKeys.TRANSIENT_SQUELCH_OPEN) {
        row.long = ButtonKeys.TRANSIENT_SQUELCH_OPEN
      }
    },
    syncLongPressDefine(row) {
      // 如果长按是紧急模式开/关,则短按自动设置为紧急模式关/开
      if (row.long === ButtonKeys.WARNING_OFF) {
        row.short = ButtonKeys.WARNING_ON
      } else if (row.long === ButtonKeys.WARNING_ON) {
        row.short = ButtonKeys.WARNING_OFF
      } else if (
        row.short === ButtonKeys.WARNING_ON ||
        row.short === ButtonKeys.WARNING_OFF
      ) {
        row.short = ButtonKeys.NONE
      } else if (row.long === ButtonKeys.TRANSIENT_MONITOR) {
        // 功能键设置暂态监听和暂态静噪打开 应该是短按长按都是同一个
        row.short = ButtonKeys.TRANSIENT_MONITOR
      } else if (row.long === ButtonKeys.TRANSIENT_SQUELCH_OPEN) {
        row.short = ButtonKeys.TRANSIENT_SQUELCH_OPEN
      }
    },
    getSoftKeyFuncDefine(type = 0) {
      let unusedList = []
      const commonUnusedList = [
        ButtonKeys.DTMF_MENU,
        ButtonKeys.COMMON_CONTACT_LIST,
        ButtonKeys.TRANSIENT_2_TONE,
        ButtonKeys.TELEMETRY_BUTTON1,
        ButtonKeys.TELEMETRY_BUTTON2,
        ButtonKeys.TELEMETRY_BUTTON3,
      ]
      switch (type) {
        case 0:
          unusedList = []
          break
        case 1:
          unusedList = []
          break
      }

      unusedList = commonUnusedList.concat(unusedList)
      return this.softKeyFuncDefine.filter(item => {
        return !unusedList.includes(item.value)
      })
    },

    // 通讯录
    getSelectedAddress(id) {
      for (let i = 0; i < this.selectedAddressBook.length; i++) {
        const item = this.selectedAddressBook[i]
        if (item.id === id) {
          return item
        }
      }

      return undefined
    },
    oneTouchFuncCallTypeChanged(row) {
      if (row.callType !== SoftKeyCallType.MSG) {
        row.msgId = 0xff
      } else if (row.msgId === 0xff) {
        // 默认选中第一条短信
        row.msgId = this.smsContent[0].msgId
      }
    },
    oneTouchFuncCallTargetChanged(row) {
      if (row.callId === 0xffff) {
        row.callType = 0xff
        row.msgId = 0xff
        return
      }
      // 没有找到对应的通讯录联系人
      const address = this.getSelectedAddress(row.callId)
      if (!address) {
        return
      }

      // 重置呼叫类型参数
      row.callType =
        address.callType === CallType.SINGLE
          ? SoftKeyCallType.SINGLE
          : SoftKeyCallType.GROUP
    },
    oneTouchFuncCallModeChanged(row) {
      // { callId: 0xFFFF, callMode: 0xFF, callType: 0xFF, msgId: 0xFF },
      if (row.callMode === 0xff) {
        row.callId = 0xffff
        row.callType = 0xff
        row.msgId = 0xff
      }
    },
    getSoftKeyCallTypeList(data) {
      const list = []
      const address = this.getAddressFromAllBooks(data.callId)
      if (!address) {
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0xff,
          },
        ]
      }
      const hasSms = this.smsContent.length > 0
      if (hasSms) {
        list.push(SoftKeyCallType[SoftKeyCallType.MSG])
      }

      // 当预制短信有配置时，可选择短信选项
      // 当呼叫对象为组呼时候，显示组呼、短信
      // 当呼叫对象为单呼时候，显示单呼、短信、呼叫提示
      if (address.callType === CallType.SINGLE) {
        list.push(SoftKeyCallType[SoftKeyCallType.SINGLE])
        list.push(SoftKeyCallType[SoftKeyCallType.TIP])
        if (data.callType === SoftKeyCallType.GROUP) {
          data.callType = SoftKeyCallType.SINGLE
        }
      } else {
        list.push(SoftKeyCallType[SoftKeyCallType.GROUP])
      }

      return list.map(key => {
        return {
          label: this.$t(`writeFreq.softKeyCallType.${key}`),
          value: SoftKeyCallType[key],
        }
      })
    },
    getKeyDefineSmsList(row) {
      if (row.callType === SoftKeyCallType.MSG && this.smsContent.length) {
        return this.smsContent.map(sms => {
          return {
            label: sms.msgContent,
            value: sms.msgId,
          }
        })
      }
      return [
        {
          label: this.$t('dialog.nothing'),
          value: 0xff,
        },
      ]
    },
    detectButtonDefined() {
      for (let i = 0; i < this.buttonDefined.oneTouchFuncCall.length; i++) {
        const item = this.buttonDefined.oneTouchFuncCall[i]
        // 如果对应的通讯录对象不存在，则将按键功能重置为默认值
        const contact = this.getSelectedAddress(item.callId)
        if (!contact) {
          this.resetButtonDefined(item)
        }
      }
    },
    encodeDmrId(config) {
      config.number += config.callType << 24
      config.sortId = config.sortId || config.id

      return config
    },
    decodeDmrId(config) {
      // 去掉高位类型标记
      const flagByte = (config.callType = config.number >> 24)
      // 全呼
      if (flagByte === CallType.BROADCAST) {
        config.dmrId = bfglob.fullCallDmrId
        return config
      }
      // 生成dmrId，过滤非本系统数据
      config.dmrId = bfutil.toHexDmrId(
        config.number & 0x00ffffff,
        flagByte === CallType.GROUP,
      )

      return config
    },
    selectAddressBooks(books) {
      this.addressBookCache = cloneDeep(this.selectedAddressBook)
      this.selectedAddressBook = books
      // 通讯录变化时，检测按键定义中单键呼叫功能的设置
      this.detectButtonDefined()
    },

    // 电话簿
    selectPhoneBooks(books) {
      this.phoneBook = books
    },
    getOriginAddressBook(id) {
      for (let i = 0; i < this.originAddressBook.length; i++) {
        const item = this.originAddressBook[i]
        if (item.id === id) {
          return item
        }
      }
      return undefined
    },
    getAddressNameByDmrId(dmrId) {
      // 从读取回来的通讯录中查找对应的dmrId的通讯录名称
      for (let i = 0; i < this.originAddressBook.length; i++) {
        const item = this.originAddressBook[i]
        if (item.dmrId === dmrId) {
          return item.name
        }
      }

      // 在通讯录中无法找到数据，则从本地的数据中查找
      const org = bfglob.gorgData.getDataByIndex(dmrId)
      return org ? org.orgShortName : ''
    },
    getSelectedAddressByDmrId(dmrId) {
      for (let i = 0; i < this.selectedAddressBook.length; i++) {
        const item = this.selectedAddressBook[i]
        if (item.dmrId === dmrId) {
          return item
        }
      }

      return undefined
    },
    getDefaultAddress(dmrId) {
      const address = this.getSelectedAddressByDmrId(dmrId)
      return address ? address.id : 0xffff
    },
    resetButtonDefined(target) {
      target.callMode = 0xff
      target.callId = 0xffff
      target.callType = 0xff
      target.msgId = 0xff

      return target
    },
    getAddressBookFromCache(addrId) {
      for (let i = 0; i < this.addressBookCache.length; i++) {
        const book = this.addressBookCache[i]
        if (book.id === addrId) {
          return book
        }
      }
      return undefined
    },
    getAddressByDmrId(dmrId) {
      for (let i = 0; i < this.selectedAddressBook.length; i++) {
        const item = this.selectedAddressBook[i]
        if (item.dmrId === dmrId) {
          return item
        }
      }
      return undefined
    },
    getSmsById(msgId) {
      for (let i = 0; i < this.smsContent.length; i++) {
        const sms = this.smsContent[i]
        if (sms.msgId === msgId) {
          return sms
        }
      }
      return undefined
    },
    getAddressFromAllBooks(id) {
      let contact = this.getSelectedAddress(id)
      if (!contact) {
        contact = this.getOriginAddressBook(id)
      }

      return contact
    },
    detectButtonDefinedFromSmsChange() {
      for (let i = 0; i < this.buttonDefined.oneTouchFuncCall.length; i++) {
        const item = this.buttonDefined.oneTouchFuncCall[i]
        if (item.callType !== SoftKeyCallType.MSG) {
          continue
        }
        // 如果按键定义中的短信ID对应的短信内容已经不存在，则按键功能短信ID重置为默认值
        const sms = this.getSmsById(item.msgId)
        if (!sms) {
          // 如果对应的通讯录对象不存在，则将按键功能重置为默认值
          const contact = this.getAddressFromAllBooks(item.callId)
          if (!contact) {
            this.resetButtonDefined(item)
            continue
          }
          item.msgId = 0xff

          // 重置按键功能类型参数
          if (contact.callType === CallType.SINGLE) {
            item.callType = SoftKeyCallType.SINGLE
          } else {
            item.callType = SoftKeyCallType.GROUP
          }
        }
      }
    },

    /* 菜单树节点操作功能 */
    updateNodeTitleFromLocaleChange() {
      const updateNodeTitle = nodeData => {
        const node = this.menuTreeRef.getNodeByKey(nodeData.key)
        if (!node) {
          return
        }
        node.setTitle(nodeData.title)
        if (nodeData.children) {
          for (let i = 0; i < nodeData.children.length; i++) {
            updateNodeTitle(nodeData.children[i])
          }
        }
      }
      for (let i = 0; i < this.menuTreeSource.length; i++) {
        updateNodeTitle(this.menuTreeSource[i])
      }
    },
    treeLoaded() {
      bftree.gotoNodeLocation(
        this.menuTreeId,
        this.string(TableIndex.DeviceInfo),
        { isActive: true },
      )
    },
    menuTreeNodeClick(event, data) {
      const node = data.node
      if (!node) {
        return
      }

      lastClickNodeKey = node.key
      let [parentNodeKey, dataId] = node.key.split(':')
      this.selectMenu = parentNodeKey
      // 没有数据的ID，为基础的菜单节点，只有一个配置项，非列表类型父节点
      if (typeof dataId === 'undefined') {
        // 判断是否为动态数据节点的父节点，如果是则该节点不显示组件
        if (this.contextmenuNodeKeys.includes(parentNodeKey)) {
          this.$nextTick(() => {
            this.selectMenu = ''
          })
        }
        return
      }

      // 处理动态生成的节点
      dataId = parseInt(dataId)
      switch (parentNodeKey) {
        // 数字紧急报警
        case this.string(TableIndex.DigitalAlarmSettings):
          this.currDigitalAlertId = dataId
          break
        // 模拟紧急报警
        case this.string(TableIndex.AnalogAlarmSettings):
          this.currAnalogAlertId = dataId
          break
        // 区域数据
        case this.string(TableIndex.ZoneSettings):
          this.currZoneId = dataId
          break
        // 信道
        case this.string(TableIndex.ChannelSettings):
          this.currChannelId = dataId
          break
        // 扫描
        case this.string(TableIndex.ScanListSettings):
          this.currScanGroupId = dataId
          break
        // 漫游
        case this.string(TableIndex.RoamListSettings):
          this.currRoamGroupId = dataId
          break
        // 站点信息
        case this.string(TableIndex.SiteInfo):
          this.currSiteId = dataId
          break
        default:
      }
    },
    contextmenuBeforeOpen(event, ui) {
      const menuTreeRef = this.menuTreeRef
      const node = $.ui.fancytree.getNode(ui.target)
      if (!node) {
        return false
      }
      // 判断哪个节点有右键菜单功能，没有的返回false以阻止打开右键菜单
      const [nodeKey, dataId] = node.key.split(':')
      if (!this.contextmenuNodeKeys.includes(nodeKey)) {
        return false
      }

      node.setActive()
      const dataNotExist = typeof dataId === 'undefined'
      let contextmenu = this.menuTreeContextmenu
      const status = {
        add: true,
        delete: true,
      }

      switch (nodeKey) {
        // 数字紧急报警
        case this.string(TableIndex.DigitalAlarmSettings):
          if (dataNotExist) {
            status.add =
              this.digitalAlertList.length < this.digitalAlertListLimit
            contextmenu = status.add
              ? contextmenu.filter(item => item.cmd === MenuTreeContextmenu.ADD)
              : []
          } else {
            status.delete = this.digitalAlertList.length > 1
            contextmenu = status.delete
              ? contextmenu.filter(
                  item => item.cmd === MenuTreeContextmenu.DELETE,
                )
              : []
          }
          break
        // 模拟紧急报警
        case this.string(TableIndex.AnalogAlarmSettings):
          if (dataNotExist) {
            status.add = this.analogAlertList.length < this.analogAlertListLimit
            contextmenu = status.add
              ? contextmenu.filter(item => item.cmd === MenuTreeContextmenu.ADD)
              : []
          } else {
            status.delete = this.analogAlertList.length > 1
            contextmenu = status.delete
              ? contextmenu.filter(
                  item => item.cmd === MenuTreeContextmenu.DELETE,
                )
              : []
          }
          break
        // 扫描列表
        case this.string(TableIndex.ScanListSettings):
          if (dataNotExist) {
            status.add = this.scanGroup.length < this.scanGroupLimit
            contextmenu = status.add
              ? contextmenu.filter(item => item.cmd === MenuTreeContextmenu.ADD)
              : []
          } else {
            status.delete = this.scanGroup.length > 1
            contextmenu = status.delete
              ? contextmenu.filter(
                  item => item.cmd === MenuTreeContextmenu.DELETE,
                )
              : []
          }
          break
        // 漫游列表
        case this.string(TableIndex.RoamListSettings):
          if (dataNotExist) {
            status.add = this.roamGroup.length < this.roamGroupLimit
            contextmenu = status.add
              ? contextmenu.filter(item => item.cmd === MenuTreeContextmenu.ADD)
              : []
          } else {
            status.delete = this.roamGroup.length > 1
            contextmenu = status.delete
              ? contextmenu.filter(
                  item => item.cmd === MenuTreeContextmenu.DELETE,
                )
              : []
          }
          break
        // 站点信息
        case this.string(TableIndex.SiteInfo):
          if (dataNotExist) {
            status.add = this.siteInfoList.length < this.siteInfoLimit
            contextmenu = status.add
              ? contextmenu.filter(item => item.cmd === MenuTreeContextmenu.ADD)
              : []
          } else {
            status.delete = this.siteInfoList.length > 1
            contextmenu = status.delete
              ? contextmenu.filter(
                  item => item.cmd === MenuTreeContextmenu.DELETE,
                )
              : []
          }
          break
        // 信道和区域等数据以系统内数据为准，不支持右键菜单
        default:
          return false
      }
      menuTreeRef.replaceMenu(contextmenu)
    },
    contextmenuSelect(event, ui) {
      const node = $.ui.fancytree.getNode(ui.target)
      if (!node) {
        return
      }

      const cmd = parseInt(ui.cmd)
      const [nodeKey, dataId] = node.key.split(':')
      switch (nodeKey) {
        // 数字紧急报警
        case this.string(TableIndex.DigitalAlarmSettings):
          this.digitalAlertContextmenuEvent(cmd, nodeKey, dataId)
          break
        // 模拟报警
        case this.string(TableIndex.AnalogAlarmSettings):
          this.analogAlertContextmenuEvent(cmd, nodeKey, dataId)
          break
        // 扫描
        case this.string(TableIndex.ScanListSettings):
          this.scanGroupContextmenuEvent(cmd, nodeKey, dataId)
          break
        // 漫游
        case this.string(TableIndex.RoamListSettings):
          this.roamGroupContextmenuEvent(cmd, nodeKey, dataId)
          break
        // 站点信息
        case this.string(TableIndex.SiteInfo):
          this.siteInfoContextmenuEvent(cmd, nodeKey, dataId)
          break
        default:
      }
    },
    manuallyTriggerNodeClick(node) {
      if (!node || !node.tr) {
        return
      }

      node.tr.click()
    },
    updateViewport() {
      this.menuTreeRef && this.menuTreeRef.updateViewport()
    },
    getNodeByKey(key) {
      return this.menuTreeRef?.getNodeByKey(key)
    },
    removeNode(key) {
      return this.menuTreeRef?.removeNode(key)
    },
    updateNodeTitle({ key, title, data }) {
      const node = this.getNodeByKey(key)
      if (!node) {
        return
      }

      node.title = title
      node.data.origin = data
      node.renderTitle()
    },
    addNodeChildren(parentNode, children) {
      return this.menuTreeRef?.addNodeChildren(parentNode, children)
    },

    // 数字紧急报警节点操作
    deleteDigitalAlertNode(data) {
      const key = `${TableIndex.DigitalAlarmSettings}:${data.id}`
      this.removeNode(key)
    },
    addDigitalAlertNode(data) {
      const parentNodeKey = this.string(TableIndex.DigitalAlarmSettings)
      const parentNode = this.getNodeByKey(parentNodeKey)
      const node = this.addNodeChildren(
        parentNode,
        this.createDigitalAlertNode(data),
      )
      parentNode.setExpanded(true)
      this.manuallyTriggerNodeClick(node)
    },
    createDigitalAlertNode(data) {
      const parentNodeKey = TableIndex.DigitalAlarmSettings
      const key = `${parentNodeKey}:${data.id}`

      return {
        title: data.name,
        folder: false,
        expanded: false,
        key,
        icon: false,
        selected: false,
        origin: data,
      }
    },
    updateDigitalAlertNode(name) {
      if (!name) {
        this.digitalAlert.name = `${this.$t('writeFreq.digitalAlert')} ${
          this.digitalAlert.id + 1
        }`
      }

      const data = this.digitalAlert
      this.updateNodeTitle({
        key: `${TableIndex.DigitalAlarmSettings}:${data.id}`,
        title: data.name,
        data,
      })
    },
    loadDigitalAlertNode() {
      const parentNodeKey = this.string(TableIndex.DigitalAlarmSettings)
      const parentNode = this.getNodeByKey(parentNodeKey)
      if (!parentNode) {
        return
      }
      const children = []
      for (let i = 0; i < this.digitalAlertList.length; i++) {
        const data = this.digitalAlertList[i]
        const node = this.getNodeByKey(`${parentNodeKey}:${data.id}`)
        if (node) {
          node.title = data.name
          node.data.origin = data
          node.renderTitle()
          continue
        }
        children.push(this.createDigitalAlertNode(data))
      }
      this.addNodeChildren(parentNode, children)
      parentNode.setExpanded(true)
    },
    createDigitalAlertItem() {
      const data = cloneDeep(DigitalAlert)
      const usedId = (this.digitalAlertList || []).map(item => item.id)
      const id = this.nextId(usedId)
      data.id = id
      data.name = `${this.$t('writeFreq.digitalAlert')} ${id + 1}`
      return data
    },
    addDigitalAlertData() {
      if (this.digitalAlertList.length >= this.digitalAlertListLimit) {
        return
      }

      return this.createDigitalAlertItem()
    },
    addDigitalAlert() {
      const data = this.addDigitalAlertData()
      this.digitalAlertList.push(data)
      this.digitalAlert = data
      this.currDigitalAlertId = data.id

      this.addDigitalAlertNode(data)
    },
    deleteDigitalAlertData(nodeKey, dataId) {
      const dataLen = this.digitalAlertList.length
      for (let i = 0; i < dataLen; i++) {
        const data = this.digitalAlertList[i]
        if (data.id !== dataId) {
          continue
        }

        // 删除菜单树节点，删除数据源数据
        this.deleteDigitalAlertNode(data)
        this.digitalAlertList.splice(i, 1)
        // 将下一个数据显示在表单中
        const nextDataIndex = i === dataLen - 1 ? 0 : i
        let nextData = this.digitalAlertList[nextDataIndex]
        if (!nextData) {
          nextData = this.digitalAlertList[0]
        }
        this.currDigitalAlertId = nextData.id
        const nextNodeKey = `${TableIndex.DigitalAlarmSettings}:${nextData.id}`
        bftree.gotoNodeLocation(this.menuTreeId, nextNodeKey, {
          isActive: true,
        })

        break
      }
    },
    digitalAlertContextmenuEvent(cmd, nodeKey, dataId) {
      const dataNotExist = typeof dataId === 'undefined'
      if (!dataNotExist) {
        dataId = parseInt(dataId)
      }

      switch (cmd) {
        case MenuTreeContextmenu.ADD:
          this.addDigitalAlert()
          break
        case MenuTreeContextmenu.DELETE:
          this.deleteDigitalAlertData(nodeKey, dataId)
          break
        default:
      }
    },

    // 模拟警报节点操作
    deleteAnalogAlertNode(data) {
      const key = `${TableIndex.AnalogAlarmSettings}:${data.id}`
      this.removeNode(key)
    },
    addAnalogAlertNode(data) {
      const parentNodeKey = this.string(TableIndex.AnalogAlarmSettings)
      const parentNode = this.getNodeByKey(parentNodeKey)
      const node = this.addNodeChildren(
        parentNode,
        this.createAnalogAlertNode(data),
      )
      parentNode.setExpanded(true)
      this.manuallyTriggerNodeClick(node)
    },
    createAnalogAlertNode(data) {
      const parentNodeKey = TableIndex.AnalogAlarmSettings
      const key = `${parentNodeKey}:${data.id}`

      return {
        title: data.name,
        folder: false,
        expanded: false,
        key,
        icon: false,
        selected: false,
        origin: data,
      }
    },
    updateAnalogAlertNode(name) {
      if (!name) {
        this.analogAlert.name = `${this.$t('writeFreq.analogAlert')} ${
          this.analogAlert.id + 1
        }`
      }

      const data = this.analogAlert
      this.updateNodeTitle({
        key: `${TableIndex.AnalogAlarmSettings}:${data.id}`,
        title: data.name,
        data,
      })
    },
    loadAnalogAlertNode() {
      const parentNodeKey = this.string(TableIndex.AnalogAlarmSettings)
      const parentNode = this.getNodeByKey(parentNodeKey)
      if (!parentNode) {
        return
      }
      const children = []
      for (let i = 0; i < this.analogAlertList.length; i++) {
        const data = this.analogAlertList[i]
        const node = this.getNodeByKey(`${parentNodeKey}:${data.id}`)
        if (node) {
          node.title = data.name
          node.data.origin = data
          node.renderTitle()
          continue
        }
        children.push(this.createAnalogAlertNode(data))
      }
      this.addNodeChildren(parentNode, children)
      parentNode.setExpanded(true)
    },
    createAnalogAlertItem() {
      const data = cloneDeep(AnalogAlert)
      const usedId = (this.analogAlertList || []).map(item => item.id)
      const id = this.nextId(usedId)
      data.id = id
      data.name = `${this.$t('writeFreq.analogAlert')} ${id + 1}`
      return data
    },
    addAnalogAlertData() {
      if (this.analogAlertList.length >= this.analogAlertListLimit) {
        return
      }
      return this.createAnalogAlertItem()
    },
    addAnalogAlert() {
      const data = this.addAnalogAlertData()
      this.analogAlertList.push(data)
      this.analogAlert = data
      this.currAnalogAlertId = data.id

      this.addAnalogAlertNode(data)
    },
    deleteAnalogAlertData(nodeKey, dataId) {
      const dataLen = this.analogAlertList.length
      for (let i = 0; i < dataLen; i++) {
        const data = this.analogAlertList[i]
        if (data.id !== dataId) {
          continue
        }

        // 删除菜单树节点，删除数据源数据
        this.deleteAnalogAlertNode(data)
        this.analogAlertList.splice(i, 1)
        // 将下一个数据显示在表单中
        const nextDataIndex = i === dataLen - 1 ? 0 : i
        let nextData = this.analogAlertList[nextDataIndex]
        if (!nextData) {
          nextData = this.analogAlertList[0]
        }
        this.currAnalogAlertId = nextData.id
        const nextNodeKey = `${TableIndex.AnalogAlarmSettings}:${nextData.id}`
        bftree.gotoNodeLocation(this.menuTreeId, nextNodeKey, {
          isActive: true,
        })

        break
      }
    },
    analogAlertContextmenuEvent(cmd, nodeKey, dataId) {
      const dataNotExist = typeof dataId === 'undefined'
      if (!dataNotExist) {
        dataId = parseInt(dataId)
      }

      switch (cmd) {
        case MenuTreeContextmenu.ADD:
          this.addAnalogAlert()
          break
        case MenuTreeContextmenu.DELETE:
          this.deleteAnalogAlertData(nodeKey, dataId)
          break
        default:
      }
    },

    // 扫描组节点操作
    getScanGroupKey(scanId) {
      return `${TableIndex.ScanListSettings}:${scanId}`
    },
    deleteScanGroupNode(data) {
      this.removeNode(this.getScanGroupKey(data.scanId))
    },
    updateScanGroupNode(data) {
      this.updateNodeTitle({
        key: this.getScanGroupKey(data.scanId),
        title: data.name,
        data,
      })
    },
    createScanGroupNode(data) {
      return {
        title: data.name,
        folder: false,
        expanded: false,
        key: this.getScanGroupKey(data.scanId),
        icon: false,
        selected: false,
        origin: data,
      }
    },
    loadScanGroupNode() {
      const parentNodeKey = this.string(TableIndex.ScanListSettings)
      const parentNode = this.getNodeByKey(parentNodeKey)
      if (!parentNode) {
        bfglob.console.warn('扫描组节点不存在')
        return
      }
      const children = []
      for (let i = 0; i < this.scanGroup.length; i++) {
        const data = this.scanGroup[i]
        const node = this.getNodeByKey(this.getScanGroupKey(data.scanId))
        if (node) {
          this.updateScanGroupNode(data)
          continue
        }
        children.push(this.createScanGroupNode(data))
      }
      this.addNodeChildren(parentNode, children)
      parentNode.setExpanded(true)
    },
    addOneScanGroupNode(data) {
      const parentNodeKey = this.string(TableIndex.ScanListSettings)
      const parentNode = this.getNodeByKey(parentNodeKey)
      if (!parentNode) {
        return
      }

      const node = this.createScanGroupNode(data)
      this.addNodeChildren(parentNode, node)
      parentNode.setExpanded(true)
    },
    addScanGroupData() {
      if (this.scanGroup.length >= this.scanGroupLimit) {
        return
      }

      const data = this.newOneScanGroup()
      this.scanGroup.push(data)
      this.currScanGroupId = data.scanId
      this.addOneScanGroupNode(data)
      // 跳转到新添加的节点上
      bftree.gotoNodeLocation(
        this.menuTreeId,
        this.getScanGroupKey(data.scanId),
        { isActive: true },
      )
    },
    deleteScanGroupData(nodeKey, dataId) {
      for (let i = 0; i < this.scanGroup.length; i++) {
        const data = this.scanGroup[i]
        if (data.scanId !== dataId) {
          continue
        }

        // 删除菜单树节点，删除数据源数据
        this.deleteScanGroupNode(data)
        this.scanGroup.splice(i, 1)
        // 将下一个数据显示在表单中
        const nextDataIndex = i === this.scanGroup.length - 1 ? 0 : i
        let nextData = this.scanGroup[nextDataIndex]
        if (!nextData) {
          nextData = this.scanGroup[0]
        }
        this.currScanGroupId = nextData.scanId
        bftree.gotoNodeLocation(
          this.menuTreeId,
          this.getScanGroupKey(nextData.scanId),
          { isActive: true },
        )

        break
      }
    },
    scanGroupContextmenuEvent(cmd, nodeKey, dataId) {
      const dataNotExist = typeof dataId === 'undefined'
      if (!dataNotExist) {
        dataId = parseInt(dataId)
      }

      switch (cmd) {
        case MenuTreeContextmenu.ADD:
          this.addScanGroupData()
          break
        case MenuTreeContextmenu.DELETE:
          this.deleteScanGroupData(nodeKey, dataId)
          break
        default:
      }
    },

    // 漫游组节点操作
    getRoamGroupKey(roamId) {
      return `${TableIndex.RoamListSettings}:${roamId}`
    },
    deleteRoamGroupNode(data) {
      this.removeNode(this.getRoamGroupKey(data.roamId))
    },
    updateRoamGroupNode(data) {
      this.updateNodeTitle({
        key: this.getRoamGroupKey(data.roamId),
        title: data.name,
        data,
      })
    },
    createRoamGroupNode(data) {
      return {
        title: data.name,
        folder: false,
        expanded: false,
        key: this.getRoamGroupKey(data.roamId),
        icon: false,
        selected: false,
        origin: data,
      }
    },
    loadRoamGroupNode() {
      const parentNodeKey = this.string(TableIndex.RoamListSettings)
      const parentNode = this.getNodeByKey(parentNodeKey)
      if (!parentNode) {
        bfglob.console.warn('漫游组节点不存在')
        return
      }
      const children = []
      for (let i = 0; i < this.roamGroup.length; i++) {
        const data = this.roamGroup[i]
        const node = this.getNodeByKey(this.getRoamGroupKey(data.roamId))
        if (node) {
          this.updateRoamGroupNode(data)
          continue
        }
        children.push(this.createRoamGroupNode(data))
      }
      this.addNodeChildren(parentNode, children)
      parentNode.setExpanded(true)
    },
    addOneRoamGroupNode(data) {
      const parentNodeKey = this.string(TableIndex.RoamListSettings)
      const parentNode = this.getNodeByKey(parentNodeKey)
      if (!parentNode) {
        return
      }

      const node = this.createRoamGroupNode(data)
      this.addNodeChildren(parentNode, node)
      parentNode.setExpanded(true)
    },
    addRoamGroupData() {
      if (this.roamGroup.length >= this.roamGroupLimit) {
        return
      }

      const data = this.newOneRoamGroup()
      this.roamGroup.push(data)
      this.currRoamGroupId = data.roamId
      this.addOneRoamGroupNode(data)
      // 跳转到新添加的节点上
      bftree.gotoNodeLocation(
        this.menuTreeId,
        this.getRoamGroupKey(data.roamId),
        { isActive: true },
      )
    },
    deleteRoamGroupData(nodeKey, dataId) {
      for (let i = 0; i < this.roamGroup.length; i++) {
        const data = this.roamGroup[i]
        if (data.roamId !== dataId) {
          continue
        }

        // 删除菜单树节点，删除数据源数据
        this.deleteRoamGroupNode(data)
        this.roamGroup.splice(i, 1)
        // 将下一个数据显示在表单中
        const nextDataIndex = i === this.roamGroup.length - 1 ? 0 : i
        let nextData = this.roamGroup[nextDataIndex]
        if (!nextData) {
          nextData = this.roamGroup[0]
        }
        this.currRoamGroupId = nextData.roamId
        bftree.gotoNodeLocation(
          this.menuTreeId,
          this.getRoamGroupKey(nextData.roamId),
          { isActive: true },
        )

        break
      }
    },
    roamGroupContextmenuEvent(cmd, nodeKey, dataId) {
      const dataNotExist = typeof dataId === 'undefined'
      if (!dataNotExist) {
        dataId = parseInt(dataId)
      }

      switch (cmd) {
        case MenuTreeContextmenu.ADD:
          this.addRoamGroupData()
          break
        case MenuTreeContextmenu.DELETE:
          this.deleteRoamGroupData(nodeKey, dataId)
          break
        default:
      }
    },
    siteInfoNameChange(id, name) {
      this.updateNodeTitle({
        key: this.getSiteInfoKey(id),
        title: name,
      })
    },
    newOneSiteInfo() {
      const data = cloneDeep(DefaultSiteInfo)
      const rxFreq = this.frequencyMhz2Hz(
        this.deviceWriteInfo.minFrequency || 400,
      )
      data.frequency = [
        {
          rxFreq,
          txFreq: rxFreq + this.frequencyMhz2Hz(1),
        },
      ]
      data.id = this.nextId(this.siteInfoUsedId, this.siteInfoLimit)
      data.name = `${this.$t('writeFreq.siteInfo')} ${data.id + 1}`

      return data
    },
    // 站点信息的树节点key
    getSiteInfoKey(id) {
      return `${TableIndex.SiteInfo}:${id}`
    },
    createSiteInfoNode(data) {
      return {
        title: data.name,
        folder: false,
        expanded: false,
        key: this.getSiteInfoKey(data.id),
        icon: false,
        selected: false,
        origin: data,
      }
    },
    addOneSiteInfoNode(data) {
      const parentNodeKey = this.string(TableIndex.SiteInfo)
      const parentNode = this.getNodeByKey(parentNodeKey)
      if (!parentNode) {
        return
      }

      const node = this.createSiteInfoNode(data)
      this.addNodeChildren(parentNode, node)
      parentNode.setExpanded(true)
    },
    addSiteInfoData() {
      if (this.siteInfoList.length >= this.siteInfoLimit) {
        return
      }

      const data = this.newOneSiteInfo()
      this.siteInfoList.push(data)
      this.currSiteId = data.id
      this.addOneSiteInfoNode(data)
      // 跳转到新添加的节点上
      bftree.gotoNodeLocation(this.menuTreeId, this.getSiteInfoKey(data.id), {
        isActive: true,
      })

      this.siteInfoListChange(data)
    },
    deleteSiteInfoNode(data) {
      this.removeNode(this.getSiteInfoKey(data.id))
    },
    deleteSiteInfoData(nodeKey, dataId) {
      for (let i = 0; i < this.siteInfoList.length; i++) {
        const data = this.siteInfoList[i]
        if (data.id !== dataId) {
          continue
        }

        // 删除菜单树节点，删除数据源数据
        this.deleteSiteInfoNode(data)
        this.siteInfoList.splice(i, 1)
        // 将下一个数据显示在表单中
        const nextDataIndex = i === this.siteInfoList.length - 1 ? 0 : i
        let nextData = this.siteInfoList[nextDataIndex]
        if (!nextData) {
          nextData = this.siteInfoList[0]
        }
        this.currSiteId = nextData.id

        bftree.gotoNodeLocation(
          this.menuTreeId,
          this.getSiteInfoKey(nextData.id),
          { isActive: true },
        )

        this.syncChannelSvtSiteInfoWhenDeleteSiteInfo(data)

        break
      }
    },
    siteInfoContextmenuEvent(cmd, nodeKey, dataId) {
      const dataNotExist = typeof dataId === 'undefined'
      if (!dataNotExist) {
        dataId = parseInt(dataId)
      }

      switch (cmd) {
        case MenuTreeContextmenu.ADD:
          this.addSiteInfoData()
          break
        case MenuTreeContextmenu.DELETE:
          this.deleteSiteInfoData(nodeKey, dataId)
          break
        default:
      }
    },

    /* 同步界面表单数据 */
    asyncDataList(originList, dataList, indexList, key = 'id', mapCallBack) {
      const needDelList = []
      const list = originList.map(item => {
        if (typeof mapCallBack === 'function') {
          item = mapCallBack(item)
        }
        const cache = indexList[item[key]]
        if (cache) {
          needDelList.push(cache)
        }
        return merge(cache, item)
      })
      while (needDelList.length) {
        dataList.splice(dataList.indexOf(needDelList.pop()), 1)
      }
      return dataList.concat(list)
    },
    asyncDeviceWriteInfo(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }
      this.deviceWriteInfo = Object.assign(this.deviceWriteInfo, res.subData[0])
    },
    asyncIdentityInfo(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }

      const settings = res.subData[0]
      this.identityInfo = Object.assign(this.identityInfo, settings)

      // 显示序列号
      this.deviceWriteInfo['serizeNumber'] = this.identityInfo.serizeNumber
    },
    // asyncReadPassword(res) {
    //   if (!res || !Array.isArray(res.subData)) {
    //     return
    //   }
    //
    //   // this.programReadPassword = merge(this.programReadPassword, res.subData[0])
    // },
    // asyncWritePassword(res) {
    //   if (!res || !Array.isArray(res.subData)) {
    //     return
    //   }
    //
    //   // this.programWritePassword = merge(this.programWritePassword, res.subData[0])
    // },
    asyncGeneralSettings(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }

      this.generalSettings = merge(this.generalSettings, res.subData[0])
    },
    asyncUISettings(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }

      this.uiSettings = merge(this.uiSettings, res.subData[0])
    },
    asyncButtonsDefine(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }

      this.buttonDefined = merge(this.buttonDefined, res.subData[0])
    },
    asyncShortMessage(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }

      for (let i = 0; i < res.subData.length; i++) {
        const sms = res.subData[i]
        const index = this.smsContent.findIndex(
          item => item.msgId === sms.msgId,
        )
        if (index === -1) {
          this.smsContent.push(sms)
        } else {
          this.smsContent[index] = sms
        }
      }
    },
    asyncEncryptConfig(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }

      this.encryptConfig = merge(this.encryptConfig, res.subData[0])
    },
    asyncEncryptList(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }
      this.encryptList = this.asyncDataList(
        res.subData,
        this.encryptList,
        this.encryptListIndex,
        'keyId',
      )
    },
    asyncEncryptARC4List(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }
      this.encryptARC4List = this.asyncDataList(
        res.subData,
        this.encryptARC4List,
        this.encryptARC4ListIndex,
        'keyId',
      )
    },
    asyncEncryptARSList(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }
      this.encryptARSList = this.asyncDataList(
        res.subData,
        this.encryptARSList,
        this.encryptARSListIndex,
        'keyId',
      )
    },
    asyncMenuSettings(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }

      this.menuSettings = merge(this.menuSettings, res.subData[0])
    },
    asyncGpsSettings(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }

      this.gpsSettings = merge(this.gpsSettings, res.subData[0])
    },
    asyncDigitalAlertList(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }

      this.digitalAlertList = this.asyncDataList(
        res.subData,
        this.digitalAlertList,
        this.digitalAlertListIndex,
      )
      if (this.digitalAlertList.length && this.currDigitalAlertId <= 0) {
        this.digitalAlert = merge(this.digitalAlert, this.digitalAlertList[0])
        this.currDigitalAlertId = this.digitalAlert.id
      }
      this.loadDigitalAlertNode()
    },
    asyncAnalogAlertList(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }

      this.analogAlertList = this.asyncDataList(
        res.subData,
        this.analogAlertList,
        this.analogAlertListIndex,
      )
      if (this.analogAlertList.length && this.currAnalogAlertId <= 0) {
        this.analogAlert = merge(this.analogAlert, this.analogAlertList[0])
        this.currAnalogAlertId = this.analogAlertList[0].id
      }
      this.loadAnalogAlertNode()
    },
    asyncAddressBook(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }

      // 同步本地通讯录树
      if (this.addressBookComp) {
        this.addressBookComp.asyncNodeSelectStatus(res.subData)
      }
      this.originAddressBook = this.originAddressBook.concat(res.subData)
    },
    asyncPhoneBook(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }

      // 保存本地数据
      for (let i = 0; i < res.subData.length; i++) {
        const pb = res.subData[i]
        const index = this.phoneBook.findIndex(
          item => item.phoneId === pb.phoneId,
        )
        if (index === -1) {
          this.phoneBook.push(pb)
        } else {
          this.phoneBook[index] = pb
        }
      }
      if (this.phoneBookComp) {
        this.phoneBookComp.asyncNodeSelectStatus(res.subData)
      }
    },
    asyncRxGroup(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }

      // 处理读取回来的接收组数据
      if (this.receiveGroupComp) {
        this.receiveGroupComp.asyncRxGroup(res.subData)
      }
    },
    getZoneChIdList(zoneData) {
      if (!zoneData || !Array.isArray(zoneData.list)) {
        return []
      }
      return zoneData.list.slice(0, zoneData.count)
    },
    asyncZoneDataList(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }
      // 保留区域实际的信道id列表
      const syncCb = item => {
        item.list = this.getZoneChIdList(item)
        return item
      }
      this.zoneDataList = this.asyncDataList(
        res.subData,
        this.zoneDataList,
        this.zoneDataIndex,
        'zoneId',
        syncCb,
      )
      this.loadZoneLeafNodes(res.subData)
    },
    asyncChannelDataList(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }
      const processItem = item => {
        if (item.scanRoamListType === 0xff || item.scanList === 0xff) {
          item.channelScanList = 0xff
        } else {
          // 开启了扫描或漫游组
          const prefix = item.scanRoamListType === 1 ? 'r' : 's'
          item.channelScanList = prefix + item.scanList
        }
        return item
      }
      this.channelDataList = this.asyncDataList(
        res.subData,
        this.channelDataList,
        this.channelsIndex,
        'chId',
        processItem,
      )
      this.loadChannelNodes(res.subData)
    },
    asyncScanConfig(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }
      this.scanConfig = merge(this.scanConfig, res.subData[0])
    },
    asyncScanGroup(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }

      const mapCb = item => {
        item.membersList = item.membersList.slice(0, item.memberCount)
        return item
      }
      this.scanGroup = this.asyncDataList(
        res.subData,
        this.scanGroup,
        this.scanGroupIndex,
        'scanId',
        mapCb,
      )
      if (this.scanGroup.length && this.currScanGroupId < 0) {
        this.currScanGroupId = this.scanGroup[0].scanId
      }
      this.loadScanGroupNode()
    },
    asyncRoamConfig(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }
      this.roamConfig = merge(this.roamConfig, res.subData[0])
    },
    asyncRoamGroup(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }

      const mapCb = item => {
        item.roamChList = item.roamChList.slice(0, item.roamChCount)
        return item
      }
      this.roamGroup = this.asyncDataList(
        res.subData,
        this.roamGroup,
        this.roamGroupIndex,
        'roamId',
        mapCb,
      )
      if (this.roamGroup.length && this.currRoamGroupId < 0) {
        this.currRoamGroupId = this.roamGroup[0].roamId
      }
      this.loadRoamGroupNode()
    },
    asyncDmrConfig(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }

      this.dmrConfig = merge(this.dmrConfig, res.subData[0])
    },
    asyncPatrolConfig(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }

      this.patrolConfig = merge(this.patrolConfig, res.subData[0])
    },
    asyncUpsideDownSettings(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }

      this.upsideDown = merge(this.upsideDown, res.subData[0])
    },
    asyncAloneWorkSettings(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }

      this.aloneWork = merge(this.aloneWork, res.subData[0])
    },
    async asyncLocalConfig(config) {
      switch (config.tableId) {
        // 设备信息
        case TableIndex.DeviceInfo:
          this.asyncDeviceWriteInfo(config)
          break
        // 身份信息
        case TableIndex.IdentityInfo:
          this.asyncIdentityInfo(config)
          break
        // // 写频读密码
        // case TableIndex.WriteFreqReadPassword:
        //   this.asyncReadPassword(config)
        //   break
        // // 写频写密码
        // case TableIndex.WriteFreqWritePassword:
        //   this.asyncWritePassword(config)
        //   break
        // 总体设置
        case TableIndex.GeneralSettings:
          this.asyncGeneralSettings(config)
          break
        // UI设置
        case TableIndex.UISettings:
          this.asyncUISettings(config)
          break
        // 按键设置
        case TableIndex.KeysSettings:
          this.asyncButtonsDefine(config)
          break
        // 菜单设置
        case TableIndex.MenuSettings:
          this.asyncMenuSettings(config)
          break
        // GPS设置
        case TableIndex.GpsSettings:
          this.asyncGpsSettings(config)
          break
        // 巡逻系统
        case TableIndex.PatrolSystemSettings:
          this.asyncPatrolConfig(config)
          break
        // 倒放设置
        case TableIndex.UpsideDownSettings:
          this.asyncUpsideDownSettings(config)
          break
        // 单独工作设置
        case TableIndex.AloneWorkSettings:
          this.asyncAloneWorkSettings(config)
          break
        // 数字警报
        case TableIndex.DigitalAlarmSettings:
          this.asyncDigitalAlertList(config)
          break
        // 模拟警报
        case TableIndex.AnalogAlarmSettings:
          this.asyncAnalogAlertList(config)
          break
        // 扫描基础设置
        case TableIndex.ScanConfigSettings:
          this.asyncScanConfig(config)
          break
        // 扫描列表
        case TableIndex.ScanListSettings:
          this.asyncScanGroup(config)
          break
        // 漫游设置
        case TableIndex.RoamConfigSettings:
          this.asyncRoamConfig(config)
          break
        // 漫游列表
        case TableIndex.RoamListSettings:
          this.asyncRoamGroup(config)
          break
        // DMR设置
        case TableIndex.DmrConfigSettings:
          this.asyncDmrConfig(config)
          break
        // 加密配置
        case TableIndex.EncryptionConfigSettings:
          this.asyncEncryptConfig(config)
          break
        // 密钥列表
        case TableIndex.EncryptionListSettings:
          this.asyncEncryptList(config)
          break
        case TableIndex.EncryptionARC4List:
          this.asyncEncryptARC4List(config)
          break
        case TableIndex.EncryptionARSList:
          this.asyncEncryptARSList(config)
          break
        // 联系人列表
        case TableIndex.DMRContactsSettings:
          this.asyncAddressBook(config)
          break
        // 接收组列表
        case TableIndex.ReceiveGroupSettings:
          this.asyncRxGroup(config)
          break
        // 预制短信
        case TableIndex.PreMakeSmsSettings:
          this.asyncShortMessage(config)
          break
        // 区域列表
        case TableIndex.ZoneSettings:
          this.asyncZoneDataList(config)
          break
        // 信道列表
        case TableIndex.ChannelSettings:
          this.asyncChannelDataList(config)
          break
        // 电话簿
        case TableIndex.PhoneBookSettings:
          this.asyncPhoneBook(config)
          break
        // 虚拟集群
        case TableIndex.VirtualCluster:
          this.asyncVirtualCluster(config)
          break
        // 站点信息
        case TableIndex.SiteInfo:
          this.asyncSiteInfoList(config)
          break
      }
    },
    loadSiteInfoListNode() {
      const parentNodeKey = this.string(TableIndex.SiteInfo)
      const parentNode = this.getNodeByKey(parentNodeKey)
      if (!parentNode) {
        bfglob.console.warn('站点信息父结点不存在')
        return
      }
      const children = []
      for (let i = 0; i < this.siteInfoList.length; i++) {
        const data = this.siteInfoList[i]
        const key = this.getSiteInfoKey(data.id)
        const node = this.getNodeByKey(key)
        if (node) {
          this.updateNodeTitle({
            key,
            title: data.name,
            data,
          })
          continue
        }
        children.push(this.createSiteInfoNode(data))
      }
      this.addNodeChildren(parentNode, children)
      parentNode.setExpanded(true)
    },
    asyncSiteInfoList(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }

      // 保存本地数据
      for (let i = 0; i < res.subData.length; i++) {
        const pb = res.subData[i]
        // 过滤无效的频率数据
        pb.frequency = pb.frequency.filter(
          freq => freq.rxFreq > 0 || freq.txFreq > 0,
        )
        const index = this.siteInfoList.findIndex(item => item.id === pb.id)
        if (index === -1) {
          this.siteInfoList.push(pb)
        } else {
          this.siteInfoList[index] = pb
        }
      }
      if (this.siteInfoList.length && this.currSiteId < 0) {
        this.currSiteId = this.siteInfoList[0].id
      }
      this.loadSiteInfoListNode()
    },
    asyncVirtualCluster(res) {
      if (!res || !Array.isArray(res.subData)) {
        return
      }
      const config = res.subData[0]
      // 过滤无效的信道ID
      config.channelList = config.channelList.filter(chId => chId !== 0xffff)
      this.virtualCluster = merge(this.virtualCluster, config)
    },
    readDataConfig() {
      if (!this.canRead() || this.isReading) {
        return
      }
      // 开始读取数据提示
      bfNotify.messageBox(this.$t('msgbox.startReading'))
      if (this.selectedDeviceDmrId) {
        this.selectedDeviceDmrId = ''
      } else {
        this.clearDeviceDataConfig()
      }

      this.readDataFlow().then(() => {
        // 缓存信道数据，以便初始化信道时，合并同ID的信道参数
        this.channelDataListCache = cloneDeep(this.channelDataList)
        // 替换编程密码，以便判断是否重置密码
        this.programReadPassword.md5Key = this.programWritePassword.md5Key =
          '*'.repeat(8)
        this.isModifyReadPwassword = this.isModifyWritePwassword = false
      })
    },

    /* 写入数据操作 */
    checkDeviceFreqValid(deviceInfo) {
      if (!deviceInfo || !Array.isArray(deviceInfo.result)) {
        return false
      }
      const info = deviceInfo.result[0]
      if (!info || !Array.isArray(info.frequencyRange)) {
        return false
      }
      if (
        info.frequencyRange.filter(item => {
          return this.deviceWriteInfo.frequencyRange.some(v => {
            return item.min !== v.min || item.max !== v.max
          })
        }).length === 0
      ) {
        this.showWriteInFailInfo(this.$t('writeFreq.frequencyRangeError'))
        this.setWriteEndStatus()
        return false
      }
      return true
    },
    getBeforeEncodeData(type) {
      switch (type) {
        case TableIndex.DeviceInfo:
          return cloneDeep(this.deviceWriteInfo)
        case TableIndex.IdentityInfo:
          return cloneDeep(this.identityInfo)
        case TableIndex.WriteFreqReadPassword:
          return cloneDeep(this.programReadPassword)
        case TableIndex.WriteFreqWritePassword:
          return cloneDeep(this.programWritePassword)
        case TableIndex.GeneralSettings:
          if (this.timezoneRef) {
            Object.assign(this.generalSettings, this.timezoneRef.getTimezone())
          }
          return cloneDeep(this.generalSettings)
        case TableIndex.UISettings:
          return cloneDeep(this.uiSettings)
        case TableIndex.KeysSettings:
          return cloneDeep(this.buttonDefined)
        case TableIndex.MenuSettings:
          return cloneDeep(this.menuSettings)
        case TableIndex.GpsSettings:
          return cloneDeep(this.gpsSettings)
        case TableIndex.PatrolSystemSettings:
          return cloneDeep(this.patrolConfig)
        case TableIndex.UpsideDownSettings:
          return cloneDeep(this.upsideDown)
        case TableIndex.AloneWorkSettings:
          return cloneDeep(this.aloneWork)
        case TableIndex.DigitalAlarmSettings:
          return cloneDeep(this.digitalAlertList)
        case TableIndex.AnalogAlarmSettings:
          return cloneDeep(this.analogAlertList)
        case TableIndex.ScanConfigSettings:
          return cloneDeep(this.scanConfig)
        case TableIndex.ScanListSettings:
          return cloneDeep(this.scanGroup).map(item => {
            // 信道列表中无信道参数为0xFFFF
            const oldLen = item.membersList.length
            item.membersList.length = this.oneScanGroupLimit
            item.membersList.fill(0xffff, oldLen, item.membersList.length)
            return item
          })
        case TableIndex.RoamConfigSettings:
          return cloneDeep(this.roamConfig)
        case TableIndex.RoamListSettings:
          return cloneDeep(this.roamGroup).map(item => {
            // 信道列表中无信道参数为0xFFFF
            const oldLen = item.roamChList.length
            item.roamChList.length = this.oneRoamGroupLimit
            item.roamChList.fill(0xffff, oldLen, item.roamChList.length)
            return item
          })
        case TableIndex.DmrConfigSettings:
          return cloneDeep(this.dmrConfig)
        case TableIndex.EncryptionConfigSettings:
          return cloneDeep(this.encryptConfig)
        case TableIndex.EncryptionListSettings:
          return cloneDeep(this.encryptList).map((item, i) => {
            item.encodeId = i
            return item
          })
        case TableIndex.EncryptionARC4List:
          return cloneDeep(this.encryptARC4List).map((item, i) => {
            item.encodeId = i
            return item
          })
        case TableIndex.EncryptionARSList:
          return cloneDeep(this.encryptARSList).map((item, i) => {
            item.encodeId = i
            return item
          })
        case TableIndex.DMRContactsSettings:
          return cloneDeep(this.selectedAddressBook)
        case TableIndex.ReceiveGroupSettings:
          return this.receiveGroupComp
            ? this.receiveGroupComp.getWriteRxGroupList()
            : []
        case TableIndex.PreMakeSmsSettings:
          return cloneDeep(this.smsContent)
        case TableIndex.ZoneSettings:
          return cloneDeep(this.zoneDataList).map(item => {
            // 区域信道列表中无信道参数为0xFFFF
            const oldLen = item.list.length
            item.list.length = this.zoneListLimit
            item.list.fill(0xffff, oldLen, item.list.length)
            return item
          })
        case TableIndex.ChannelSettings:
          return cloneDeep(this.channelDataList)
        // .map(channel => {
        //   if (this.receiveGroupComp) {
        //     channel.receiveGroup = this.receiveGroupComp.getGroupIdByChId(channel.chId)
        //   } else {
        //     channel.receiveGroup = 0xFFFF
        //   }
        //   return channel
        // })
        case TableIndex.PhoneBookSettings:
          const phoneBooks = this.phoneBook.filter(item => {
            return !!bfglob.gphoneBook.getDataByIndex(item.phoneNo)
          })
          return phoneBooks
        case TableIndex.VirtualCluster:
          return cloneDeep(this.virtualCluster)
        case TableIndex.SiteInfo:
          return cloneDeep(this.siteInfoList)
      }
    },
    validateAllRules() {
      return new Promise((resolve, reject) => {
        const validateList = [
          {
            ref: 'generalSettings',
            msg: this.$t('writeFreq.generalSettingsFormValidate'),
          },
          // { ref: 'digitAlarm', msg: this.$t('writeFreq.digitalAlarmFormValidate') },
          // { ref: 'scanGroup', msg: this.$t('writeFreq.scanGroupFormValidate') }
          {
            ref: 'virtualCluster',
            msg: this.$t('writeFreq.virtualClusterFormValidate'),
          },
        ]
        // 信道列表中有数据时才需要验证
        if (this.channelDataList.length) {
          // if (!this.channelDataList.includes(this.channelData)) {
          //   this.channelData = this.channelDataList[0]
          // }
          // validateList.push({ ref: 'channelData', msg: this.$t('writeFreq.channelDataFormValidate') })
        }
        const iterator = validateList[Symbol.iterator]()

        const validate = item => {
          if (item.done) {
            resolve()
          }
          const { ref, msg } = item.value
          this.formValidate(ref)
            .then(() => {
              validate(iterator.next())
            })
            .catch(() => {
              reject(msg)
            })
        }

        validate(iterator.next())
      })
    },
    writeInFrequency() {
      if (!this.canWrite()) {
        return
      }
      // 表单验证通过后才能写入数据
      this.validateAllRules().then(this.writeDataConfig)
    },

    exportConfig() {
      // 只导出设备的写频配置，不导出设备的接收组、信道数据、设备的dmrId等
      const IndexEntries = Object.entries(TableIndex)
      const exclude = [
        TableIndex.WriteFreqReadPassword,
        TableIndex.WriteFreqWritePassword,
      ]

      const jsonData = {}
      for (const [, tableId] of IndexEntries) {
        if (exclude.includes(tableId)) {
          continue
        }
        const config = this.getBeforeEncodeData(tableId)
        switch (tableId) {
          case TableIndex.generalSettings:
            // 将常规设置中的设备dmrId和设备名称去掉
            delete config.deviceName
            break
          case TableIndex.DmrConfigSettings:
            delete config.dmrId
        }

        jsonData[tableId] = Array.isArray(config) ? config : [config]
      }

      this.exportData(jsonData)
    },
    newConfig() {
      this.selectedDeviceDmrId = ''
      this.clearDeviceDataConfig(true)
      // 清除设备频率信息
      this.deviceWriteInfo = Object.assign(this.deviceWriteInfo, {
        minFrequency: 0,
        maxFrequency: 0,
      })
      this.generalSettings = cloneDeep(GeneralSettings)
      this.uiSettings = cloneDeep(UISettings)
      this.buttonDefined = cloneDeep(ButtonDefined)
      this.menuSettings = cloneDeep(MenuSettings)
      this.gpsSettings = cloneDeep(GpsSettings)
      this.upsideDown = cloneDeep(UpsideDown)
      this.aloneWork = cloneDeep(AloneWork)
      this.scanConfig = cloneDeep(ScanConfig)
      this.roamConfig = cloneDeep(RoamConfig)
      this.dmrConfig = cloneDeep(DMRConfig)
      this.patrolConfig = cloneDeep(PatrolConfig)
      this.channelDataListCache = []
      this.virtualCluster = cloneDeep(VirtualCluster)
      this.initSmsData()
    },
    redrawViewport(retry = 0) {
      if (retry > 10) {
        return
      }
      this.menuTreeRef.updateViewport()
      setTimeout(() => {
        const viewport = this.menuTreeRef.getTree().viewport
        const count = this.menuTreeRef.getViewportCount()
        if (
          isNaN(viewport.count) ||
          isNaN(viewport.start) ||
          count !== viewport.count
        ) {
          this.redrawViewport(++retry)
        }
      }, 0)
    },
    string(key) {
      return key + ''
    },
    sendCountFormat(val) {
      if (val === 0) {
        return this.$t('dialog.keepSending')
      }
      return val
    },
    politeRetryFormat(val) {
      if (val === -1) {
        return this.$t('dialog.keepSending')
      }
      return val
    },
  },
  computed: {
    currentChannelIdList() {
      return this.channelDataList.map(item => item.chId)
    },
    siteInfoIndex() {
      return this.siteInfoList.findIndex(v => v.id === this.currSiteId)
    },
    currScanGroupIndex() {
      return this.scanGroup.findIndex(v => v.scanId === this.currScanGroupId)
    },
    scanGroupUsedId() {
      return (this.scanGroup || []).map(item => item.scanId)
    },
    currRoamGroupIndex() {
      return this.roamGroup.findIndex(v => v.roamId === this.currRoamGroupId)
    },
    roamGroupUsedId() {
      return (this.roamGroup || []).map(item => item.roamId)
    },
    siteInfoUsedId() {
      return (this.siteInfoList || []).map(item => item.id)
    },
    // 信道ID与区域ID的索引
    channelInZoneIndex() {
      return this.zoneDataList
        .map(item => {
          return item.list
            .map(chId => {
              return { [chId]: item.zoneId }
            })
            .reduce((p, c) => Object.assign(p, c), {})
        })
        .reduce((p, c) => Object.assign(p, c), {})
    },

    maxPreHintTime() {
      return this.upsideDown.entryDelay >= 69 ? 69 : this.upsideDown.entryDelay
    },
    disableStrangeNumber() {
      return this.generalSettings.concatSettings.showContactContent !== 1
    },
    timezoneRef() {
      return this.$refs.timezone
    },
    timeZone: {
      get() {
        const {
          year,
          month,
          date,
          hours,
          minutes,
          seconds,
          gmtStandard,
          hoursDiff,
          minutesDiff,
          timezoneId,
        } = this.generalSettings
        return {
          year,
          month,
          date,
          hours,
          minutes,
          seconds,
          gmtStandard,
          hoursDiff,
          minutesDiff,
          timezoneId,
        }
      },
      set(val) {
        Object.assign(this.generalSettings, val)
      },
    },
    InsertDataAfterClear() {
      return InsertDataAfterClear
    },
    contextmenuNodeKeys() {
      return [
        this.string(TableIndex.DigitalAlarmSettings),
        this.string(TableIndex.AnalogAlarmSettings),
        this.string(TableIndex.ScanListSettings),
        this.string(TableIndex.RoamListSettings),
        this.string(TableIndex.SiteInfo),
      ]
    },
    menuTreeSource() {
      return [
        {
          key: this.string(TableIndex.DeviceInfo),
          title: this.$t('dialog.deviceInfo'),
          icon: false,
          folder: true,
        },
        {
          key: TableIndex.GeneralSettings + '-base',
          title: this.$t('dialog.generalSetting'),
          icon: false,
          folder: true,
          expanded: true,
          children: [
            {
              key: this.string(TableIndex.GeneralSettings),
              title: this.$t('dialog.basicConfig'),
              icon: false,
              folder: false,
            },
            {
              key: this.string(TableIndex.UISettings),
              title: this.$t('writeFreq.uiSettings'),
              icon: false,
              folder: false,
            },
            {
              key: `${TableIndex.WriteFreqReadPassword}-${TableIndex.WriteFreqWritePassword}`,
              title: this.$t('dialog.programmingPwd'),
              icon: false,
              folder: false,
            },
          ],
        },
        {
          key: this.string(TableIndex.KeysSettings),
          title: this.$t('dialog.buttonDefinition'),
          icon: false,
          folder: true,
        },
        {
          key: this.string(TableIndex.MenuSettings),
          title: this.$t('dialog.menuSettings'),
          icon: false,
          folder: true,
        },
        {
          key: this.string(TableIndex.GpsSettings),
          title: this.$t('dialog.satellitePositionSetting'),
          icon: false,
          folder: true,
        },
        {
          key: this.string(TableIndex.UpsideDownSettings),
          title: this.$t('writeFreq.upsideDown'),
          icon: false,
          folder: true,
        },
        {
          key: this.string(TableIndex.AloneWorkSettings),
          title: this.$t('writeFreq.workAlone'),
          icon: false,
          folder: true,
        },
        {
          key: TableIndex.DigitalAlarmSettings + '-base',
          title: this.$t('writeFreq.emergencyAlarmSystem'),
          icon: false,
          folder: true,
          expanded: true,
          children: [
            {
              key: this.string(TableIndex.DigitalAlarmSettings),
              title: this.$t('writeFreq.digitalEmergencyAlarm'),
              icon: false,
              folder: true,
              expanded: true,
            },
            {
              key: this.string(TableIndex.AnalogAlarmSettings),
              title: this.$t('writeFreq.analogEmergencyAlarm'),
              icon: false,
              folder: true,
              expanded: true,
            },
          ],
        },
        {
          key:
            TableIndex.ScanConfigSettings + '-' + TableIndex.ScanListSettings,
          title: this.$t('writeFreq.scan'),
          icon: false,
          folder: true,
          expanded: true,
          children: [
            {
              key: this.string(TableIndex.ScanConfigSettings),
              title: this.$t('writeFreq.scanSettings'),
              icon: false,
              folder: true,
              expanded: true,
            },
            {
              key: this.string(TableIndex.ScanListSettings),
              title: this.$t('writeFreq.scanList'),
              icon: false,
              folder: true,
              expanded: true,
            },
          ],
        },
        {
          key:
            TableIndex.RoamConfigSettings + '-' + TableIndex.RoamListSettings,
          title: this.$t('writeFreq.roaming'),
          icon: false,
          folder: true,
          expanded: true,
          children: [
            {
              key: this.string(TableIndex.RoamConfigSettings),
              title: this.$t('writeFreq.roamSettings'),
              icon: false,
              folder: true,
              expanded: true,
            },
            {
              key: this.string(TableIndex.RoamListSettings),
              title: this.$t('writeFreq.roamList'),
              icon: false,
              folder: true,
              expanded: true,
            },
          ],
        },
        {
          key: TableIndex.DmrConfigSettings + '-base',
          title: this.$t('dialog.dmrService'),
          icon: false,
          folder: true,
          expanded: true,
          children: [
            {
              key: this.string(TableIndex.DmrConfigSettings),
              title: this.$t('writeFreq.dmrBaseSettings'),
              icon: false,
              folder: true,
              expanded: true,
            },
            {
              key: this.string(TableIndex.EncryptionConfigSettings),
              title: this.$t('writeFreq.encryptionConfig'),
              icon: false,
              folder: true,
              expanded: true,
            },
            {
              key: this.string(TableIndex.DMRContactsSettings),
              title: this.$t('dialog.dmrContact'),
              icon: false,
              folder: true,
            },
            {
              key: this.string(TableIndex.ReceiveGroupSettings),
              title: this.$t('dialog.rxGroup'),
              icon: false,
              folder: true,
            },
            {
              key: this.string(TableIndex.PreMakeSmsSettings),
              title: this.$t('dialog.preMadeSms'),
              icon: false,
              folder: true,
            },
          ],
        },
        {
          key: this.string(TableIndex.PhoneBookSettings),
          title: this.$t('dialog.phoneBook'),
          icon: false,
          folder: true,
        },
        {
          key: this.string(TableIndex.PatrolSystemSettings),
          title: this.$t('dialog.patrolSystem'),
          icon: false,
          folder: true,
        },
        {
          key: this.string(TableIndex.VirtualCluster),
          title: this.$t('writeFreq.virtualCluster'),
          icon: false,
          folder: true,
        },
        {
          key: TableIndex.ZoneSettings + '-base',
          title: this.$t('writeFreq.zone'),
          icon: false,
          folder: true,
          expanded: true,
        },
        {
          key: this.string(TableIndex.SiteInfo),
          title: this.$t('writeFreq.siteInfo'),
          icon: false,
          folder: true,
        },
      ]
    },
    menuTreeOpts() {
      return {
        source: this.menuTreeSource,
        checkbox: false,
        click: this.menuTreeNodeClick,
      }
    },
    menuTreeContextmenu() {
      return [
        {
          title: this.$t('dialog.add'),
          cmd: MenuTreeContextmenu.ADD,
        },
        {
          title: this.$t('dialog.delete'),
          cmd: MenuTreeContextmenu.DELETE,
        },
      ]
    },
    menuTreeContextmenuOption() {
      return {
        menu: this.menuTreeContextmenu,
        beforeOpen: this.contextmenuBeforeOpen,
        select: this.contextmenuSelect,
      }
    },

    hasFreqRange() {
      return (
        this.deviceWriteInfo.maxFrequency > 0 &&
        this.deviceWriteInfo.minFrequency > 0
      )
    },
    disWriteBtn() {
      return (
        this.disReadBtn ||
        !this.selectedDeviceDmrId ||
        !this.hasFreqRange ||
        this.selectedChannels.length === 0
      )
    },
    disReadBtn() {
      return (
        this.noQWebServer || this.noDevice || this.isWriting || this.isReading
      )
    },
    noQWebServer() {
      return !this.QWebServer
    },

    showDeviceInfo() {
      return this.selectMenu === this.string(TableIndex.DeviceInfo)
    },
    showGeneralSettings() {
      return this.selectMenu === this.string(TableIndex.GeneralSettings)
    },
    showUISettings() {
      return this.selectMenu === this.string(TableIndex.UISettings)
    },
    showProgramPasswordSettings() {
      return (
        this.selectMenu ===
        `${TableIndex.WriteFreqReadPassword}-${TableIndex.WriteFreqWritePassword}`
      )
    },
    showButtonSettings() {
      return this.selectMenu === this.string(TableIndex.KeysSettings)
    },
    showShortMessage() {
      return this.selectMenu === this.string(TableIndex.PreMakeSmsSettings)
    },
    showEncryptSettings() {
      return (
        this.selectMenu === this.string(TableIndex.EncryptionConfigSettings)
      )
    },
    showMenuSettings() {
      return this.selectMenu === this.string(TableIndex.MenuSettings)
    },
    showGpsSettings() {
      return this.selectMenu === this.string(TableIndex.GpsSettings)
    },
    showUpsideDown() {
      return this.selectMenu === this.string(TableIndex.UpsideDownSettings)
    },
    showWorkAlone() {
      return this.selectMenu === this.string(TableIndex.AloneWorkSettings)
    },
    showDigitalAlert() {
      return this.selectMenu === this.string(TableIndex.DigitalAlarmSettings)
    },
    showAnalogAlert() {
      return this.selectMenu === this.string(TableIndex.AnalogAlarmSettings)
    },
    showDigitalAddress() {
      return this.selectMenu === this.string(TableIndex.DMRContactsSettings)
    },
    showPhoneBook() {
      return this.selectMenu === this.string(TableIndex.PhoneBookSettings)
    },
    showRxGroup() {
      return this.selectMenu === this.string(TableIndex.ReceiveGroupSettings)
    },
    showDmrConfigSettings() {
      return this.selectMenu === this.string(TableIndex.DmrConfigSettings)
    },
    showZoneRootData() {
      return this.selectMenu === TableIndex.ZoneSettings + '-base'
    },
    showZoneSettings() {
      return this.selectMenu === this.string(TableIndex.ZoneSettings)
    },
    showChannelItem() {
      return this.selectMenu === this.string(TableIndex.ChannelSettings)
    },
    showScanConfig() {
      return this.selectMenu === this.string(TableIndex.ScanConfigSettings)
    },
    showScanList() {
      return this.selectMenu === this.string(TableIndex.ScanListSettings)
    },
    showRoamConfig() {
      return this.selectMenu === this.string(TableIndex.RoamConfigSettings)
    },
    showRoamList() {
      return this.selectMenu === this.string(TableIndex.RoamListSettings)
    },
    showPatrolSystem() {
      return this.selectMenu === this.string(TableIndex.PatrolSystemSettings)
    },
    showVirtualCluster() {
      return this.selectMenu === this.string(TableIndex.VirtualCluster)
    },
    showSiteInfo() {
      return this.selectMenu === this.string(TableIndex.SiteInfo)
    },

    addressBookCallTypes() {
      return CallType
    },

    generalSettingsRules() {
      const pwdRule = [
        validateRules.mustLength(['change', 'blur'], 6),
        validateRules.mustNumber(['change', 'blur']),
      ]

      return {
        deviceName: [
          validateRules.required(),
          validateRules.maxLen(['change', 'blur'], 16),
        ],
        powerOnPwd: pwdRule,
      }
    },
    savePowerModeList() {
      return [
        {
          label: this.$t('dialog.nothing'),
          value: 0,
        },
        {
          label: '1:1',
          value: 1,
        },
        {
          label: '1:2',
          value: 2,
        },
        {
          label: '1:3',
          value: 3,
        },
        {
          label: '1:4',
          value: 4,
        },
      ]
    },
    notInSavePowerMode() {
      return this.generalSettings.baseSettings.savePowerMode === 0
    },
    allowCallInstructionList() {
      // 呼叫允许指示 ,000 无  001 模拟  010 模拟和数字 011 数字
      return [
        {
          label: this.$t('dialog.nothing'),
          value: 0,
        },
        {
          label: this.$t('dialog.analog'),
          value: 1,
        },
        {
          label: this.$t('dialog.analogAndDigital'),
          value: 2,
        },
        {
          label: this.$t('dialog.digital'),
          value: 3,
        },
      ]
    },
    recordCompressionRatioList() {
      // 录音压缩比 0 不压缩, 1 3.5倍  当录音使能==0时不可用
      return [
        {
          label: this.$t('dialog.nonCompacting'),
          value: 0,
        },
        {
          label: '3.5',
          value: 1,
        },
      ]
    },
    syncTime() {
      return this.generalSettings.syncTime
    },
    dmrIdLabel() {
      const intDmrId = this.dmrConfig.dmrId
      if (intDmrId === 0) {
        return ''
      }
      const dmrId = bfutil.int32Dmrid2Hex(intDmrId)
      return dmrId ? ` ${dmrId} / ${intDmrId}` : ''
    },
    month: {
      get() {
        return this.generalSettings.month + 1
      },
      set(val) {
        this.generalSettings.month = val - 1
      },
    },
    hours: {
      get() {
        return this.generalSettings.hour + this.generalSettings.timeZoneHour
      },
      set(val) {
        this.generalSettings.hour = val - this.generalSettings.timeZoneHour
      },
    },
    maxDate() {
      const dates = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
      return dates[this.generalSettings.month]
    },
    powerTransferEnable() {
      return !this.generalSettings.powerTransfer.enable
    },

    deviceInfoLabelPosition() {
      return this.isMobile ? 'top' : 'left'
    },
    buttonDefineLabelWidth() {
      return '170px'
    },
    softKeyFuncDefine() {
      return Object.keys(ButtonKeys)
        .filter(key => {
          return this.$te(`writeFreq.softKeyFuncDefine.${key}`)
        })
        .map(key => {
          return {
            label: this.$t(`writeFreq.softKeyFuncDefine.${key}`),
            value: ButtonKeys[key],
          }
        })
    },
    SoftKeyCallType() {
      return SoftKeyCallType
    },
    buttonDefineAddressList() {
      return this.defaultAddressList.filter(
        address => address.dmrId !== bfglob.fullCallDmrId,
      )
    },

    encryptListActiveWidth() {
      return this.isFR ? '120px' : '100px'
    },
    encryptEnable() {
      return this.encryptConfig.enable
    },
    menuHangTimeLabelWidth() {
      return this.isEn ? '160px' : '120px'
    },
    freqDisplayList() {
      // 信道显示模式，0 频率显示，1 信道显示，2 频率+信道显示
      return [
        {
          label: this.$t('dialog.freqDisplay'),
          value: 0,
        },
        {
          label: this.$t('dialog.chDisplay'),
          value: 1,
        },
        {
          label: this.$t('dialog.freqAndChDisplay'),
          value: 2,
        },
      ]
    },

    digitalAlertComp() {
      return this.$refs.digitalAlert
    },
    addressBookComp() {
      return this.$refs[this.addrBookTreeId]
    },
    phoneBookComp() {
      return this.$refs[this.phoneBookTreeId]
    },
    receiveGroupComp() {
      return this.$refs[this.refReceiveGroup]
    },
    scanGroupComp() {
      return this.$refs.scanGroup
    },
    roamGroupComp() {
      return this.$refs.roamGroup
    },
    rootZoneComp() {
      return this.$refs.rootZone
    },
    parentZoneComp() {
      return this.$refs.parentZone
    },
    leafZoneComp() {
      return this.$refs.leafZone
    },
    zoneLeafChannelComp() {
      return this.$refs.zoneLeafChannel
    },

    // 列表类型数据索引
    encryptListIndex() {
      return this.createDataListIndex(this.encryptList, 'keyId')
    },
    encryptARC4ListIndex() {
      return this.createDataListIndex(this.encryptARC4List, 'keyId')
    },
    encryptARSListIndex() {
      return this.createDataListIndex(this.encryptARSList, 'keyId')
    },
    digitalAlertListIndex() {
      return this.createDataListIndex(this.digitalAlertList)
    },
    analogAlertListIndex() {
      return this.createDataListIndex(this.analogAlertList)
    },
    zoneDataIndex() {
      return this.createDataListIndex(this.zoneDataList, 'zoneId')
    },
    channelsIndex() {
      return this.createDataListIndex(this.channelDataList, 'chId')
    },
    scanGroupIndex() {
      return this.createDataListIndex(this.scanGroup, 'scanId')
    },
    roamGroupIndex() {
      return this.createDataListIndex(this.roamGroup, 'roamId')
    },

    isDChannel() {
      return this.oneChannel.chType === 0
    },
    isAChannel() {
      return this.oneChannel.chType === 1
    },
    isDAChannel() {
      return this.oneChannel.chType === 2
    },
    isConnectNetworking() {
      return this.oneChannel.baseSettings.networking
    },
    ipSiteConnect() {
      return this.oneChannel.callingSettings.ipSiteConnect
    },
    maxColorCode() {
      return this.oneChannel.callingSettings.throughMode ? 14 : 15
    },
    sameFreq() {
      return this.oneChannel.rxFreq === this.oneChannel.txFreq
    },
    isVirtualCluster() {
      return !!this.oneChannel.encryptionSettings.virtualCluster
    },
    allowPermitConditions() {
      return this.oneChannel.permitConditions === 0
    },
    onlyReceive() {
      return this.oneChannel.baseSettings.onlyReceive
    },
    isScanList() {
      return this.oneChannel.scanRoamListType === 0
    },
    isRoamList() {
      return this.oneChannel.scanRoamListType === 1
    },
    disAutoScan() {
      return !this.isScanList
    },
    disAutoRoam() {
      return !this.isRoamList
    },
    disPlosive() {
      return this.onlyReceive || this.oneChannel.subsonicEncode === 0xffff
    },
    disableSVTSiteInfo() {
      return this.oneChannel.encryptionSettings.virtualCluster === false
    },
    freqOffsetRules() {
      return []
    },
    channelRules() {
      const minFrequency = this.deviceWriteInfo.minFrequency ?? DefaultFrequency
      const maxFrequency =
        this.deviceWriteInfo.maxFrequency ?? DefaultFrequency + 80
      const min = bfutil.frequencyMhz2Hz(minFrequency)
      const max = bfutil.frequencyMhz2Hz(maxFrequency)
      const smg = `${minFrequency} ~ ${maxFrequency}`
      const frequency = [
        validateRules.required(['blur']),
        validateRules.range(['blur'], min, max, smg),
      ]
      return {
        chName: [validateRules.required(['blur'])],
        rxFreq: frequency,
        txFreq: frequency,
      }
    },
    chTypeList() {
      return [
        {
          label: this.$t('writeFreq.digitalChannel'),
          value: 0,
        },
        {
          label: this.$t('writeFreq.analogChannel'),
          value: 1,
        },
        {
          label: this.$t('writeFreq.digitalAnalogChannel'),
          value: 2,
        },
      ]
    },
    channelBandwidthList() {
      // 信道带宽 "0:12.5K 1:25K"
      return [
        {
          label: '12.5K',
          value: 0,
        },
        {
          label: '25K',
          value: 1,
        },
      ]
    },
    chScanRoamGroupList() {
      const list = [
        {
          label: this.$t('dialog.nothing'),
          value: 0xff,
        },
      ]
      const roamList = this.roamGroup.map(item => {
        return {
          label: item.name,
          value: 'r' + item.roamId,
        }
      })
      const scanList = this.scanGroup.map(item => {
        return {
          label: item.name,
          value: 's' + item.scanId,
        }
      })

      // 勾选联网，只能使用漫游
      if (this.isConnectNetworking) {
        if (this.ipSiteConnect) {
          return list.concat(roamList)
        }
        return list
      }

      // 漫游、扫描都可以使用
      // IP站点连接未勾选，无漫游
      if (this.ipSiteConnect) {
        return list.concat(scanList, roamList)
      }

      return list.concat(scanList)
    },
    chTimeSlotCalList() {
      return [
        {
          label: this.$t('dataTable.fail'),
          value: 0,
        },
        {
          label: this.$t('dataTable.pass'),
          value: 1,
        },
        {
          label: this.$t('writeFreq.firstChoice'),
          value: 2,
        },
      ]
    },
    slotModeList() {
      return [
        {
          label: this.$t('dialog.timeSlot', { num: 1 }),
          value: 0,
        },
        {
          label: this.$t('dialog.timeSlot', { num: 2 }),
          value: 1,
        },
        {
          label: this.$t('dialog.virtualCluster'),
          value: 2,
        },
      ]
    },
    voicePriorityList() {
      const list = [
        {
          label: this.$t('dialog.low'),
          value: 1,
        },
        {
          label: this.$t('dialog.mid'),
          value: 2,
        },
        {
          label: this.$t('dialog.high'),
          value: 3,
        },
      ]

      if (this.isConnectNetworking) {
        return list
      }

      return [
        {
          label: this.$t('dialog.nothing'),
          value: 0,
        },
        ...list,
      ]
    },
    specifyTxTimeSlotList() {
      return [
        {
          label: this.$t('dialog.nothing'),
          value: 0,
        },
        {
          label: this.$t('dialog.timeSlot', { num: 1 }),
          value: 1,
        },
        {
          label: this.$t('dialog.timeSlot', { num: 2 }),
          value: 2,
        },
      ]
    },
    SVTSiteInfoList() {
      const rxFreq = this.oneChannel.rxFreq
      const txFreq = this.oneChannel.txFreq
      const list = this.siteInfoList
        .filter(item => {
          return item?.frequency?.some(
            freq => freq.rxFreq === rxFreq && freq.txFreq === txFreq,
          )
        })
        .map(item => {
          return {
            label: item.name,
            value: item.id,
          }
        })
      return [
        {
          label: this.$t('dialog.nothing'),
          value: 0xff,
        },
        ...list,
      ]
    },
    duplexModelList() {
      // 双工模式 "0:单工 1:双工"
      return [
        {
          label: this.$t('writeFreq.simplex'),
          value: 0,
        },
        {
          label: this.$t('writeFreq.duplex'),
          value: 1,
        },
      ]
    },
    preEmphasisList() {
      return [
        {
          label: this.$t('dialog.nothing'),
          value: 0,
        },
        {
          label: this.$t('writeFreq.deEmphasisAndPreEmphasis'),
          value: 1,
        },
      ]
    },
    algorithmList() {
      // 加密类型 当前使用：0、1、5、6
      // 0：异或 1：增强型异或 2：ARC4 3：AES256 4：动态加密 5：Moto Arc4 6：Moto AES256 7：不加密
      return [
        {
          label: this.$t('writeFreq.xor'),
          value: 0,
        },
        {
          label: this.$t('writeFreq.enhancedXor'),
          value: 1,
        },
        // {
        //   label: 'ARC4',
        //   value: 2,
        // },
        // {
        //   label: 'AES256',
        //   value: 3,
        // },
        // {
        //   label: this.$t('dialog.dynamicencryption'),
        //   value: 4,
        // },
        {
          label: this.$t('writeFreq.advancedEncryptionARC4'),
          value: 5,
        },
        {
          label: this.$t('writeFreq.advancedEncryptionARS'),
          value: 6,
        },
      ]
    },
    encryptKeys() {
      return {
        0: this.encryptList,
        1: this.encryptList,
        5: this.encryptARC4List,
        6: this.encryptARSList,
      }
    },
    encryptKeyList() {
      // 根据加密类型返回加密列表候选
      const keys =
        this.encryptKeys[this.oneChannel.encryptionSettings.type] || []
      return keys.map(item => {
        return {
          label: item.keyName,
          value: item.keyId,
        }
      })
    },
    receiveToneTypeList() {
      // 接收亚音类型 "0:无 1:亚音频 2:亚音数码 3:反向亚音数码"
      let list = []
      if (!this.isDAChannel) {
        list = [
          {
            label: this.$t('dialog.nothing'),
            value: 0,
          },
        ]
      }
      return list.concat([
        {
          label: this.$t('writeFreq.ctcss'),
          value: 1,
        },
        {
          label: this.$t('writeFreq.cdcss'),
          value: 2,
        },
        {
          label: this.$t('writeFreq.cdcssInvert'),
          value: 3,
        },
      ])
    },
    transmitToneTypeList() {
      // 发射亚音类型 "0:无 1:亚音频 2:亚音数码 3:反向亚音数码"
      return [
        {
          label: this.$t('dialog.nothing'),
          value: 0,
        },
        {
          label: this.$t('writeFreq.ctcss'),
          value: 1,
        },
        {
          label: this.$t('writeFreq.cdcss'),
          value: 2,
        },
        {
          label: this.$t('writeFreq.cdcssInvert'),
          value: 3,
        },
      ]
    },
    disRxAnalogToneCode() {
      return this.oneChannel.receiveToneType !== 1
    },
    disRxDigitalToneCode() {
      return this.oneChannel.receiveToneType < 2
    },
    disTxAnalogToneCode() {
      return this.oneChannel.transmitToneType !== 1
    },
    disTxDigitalToneCode() {
      return this.oneChannel.transmitToneType < 2
    },
    analogToneCode() {
      return Object.keys(AnalogCode)
        .map(key => {
          return {
            label: key,
            value: AnalogCode[key],
          }
        })
        .sort((a, b) => (a.value > b.value ? 1 : a.value < b.value ? -1 : 0))
    },
    digitalToneCode() {
      // 数字亚音码暂时添加“无”选项，以便正常显示0值
      // [
      //   {
      //     label: this.$t('dialog.nothing'),
      //     value: 0,
      //   },
      // ]

      return Object.keys(DigitalCode)
        .map(key => {
          return {
            label: key,
            value: DigitalCode[key],
          }
        })
        .sort((a, b) => (a.value > b.value ? 1 : a.value < b.value ? -1 : 0))
    },
    squelchLevelList() {
      // 静噪等级  0：常开 1：正常 2：加强
      return [
        {
          label: this.$t('writeFreq.normallyOpen'),
          value: 0,
        },
        {
          label: this.$t('writeFreq.normal'),
          value: 1,
        },
        {
          label: this.$t('writeFreq.strengthen'),
          value: 2,
        },
      ]
    },
    rxSquelchModeList() {
      // 接收静噪模式 "0:载波 1:亚音  2:可选信令 3:亚音与可选信令 4:亚音或可选信令"
      if (this.isDAChannel) {
        return [
          {
            label: this.$t('writeFreq.ctcssCdcss'),
            value: 1,
          },
        ]
      }
      return [
        {
          label: this.$t('writeFreq.carrier'),
          value: 0,
        },
        {
          label: this.$t('writeFreq.ctcssCdcss'),
          value: 1,
        },
      ]
    },
    squelchModeList() {
      // 切换信道静噪模式 "0:接收静噪模式 1:监听静噪模式"
      return [
        {
          label: this.$t('writeFreq.receiveSquelchMode'),
          value: 0,
        },
        {
          label: this.$t('writeFreq.monitorSquelchMode'),
          value: 1,
        },
      ]
    },
    tailToneList() {
      // 尾音选择 "0:标准 1:非标准"
      return [
        {
          label: this.$t('dialog.standard'),
          value: 0,
        },
        {
          label: this.$t('dialog.nonStandard'),
          value: 1,
        },
      ]
    },
    plosiveList() {
      // 爆破音 "0:标准 1:非标准"
      return [
        {
          label: this.$t('dialog.standard'),
          value: 0,
        },
        {
          label: this.$t('dialog.nonStandard'),
          value: 1,
        },
      ]
    },
    receiveGroupList() {
      const def = [
        {
          label: this.$t('dialog.nothing'),
          value: 0xffff,
        },
      ]
      const rxGroupList = this.rxGroupList.map(item => {
        return {
          label: item.groupName,
          value: item.groupId,
        }
      })
      return def.concat(rxGroupList)
    },
    defaultAddressList() {
      const cache = []
      return [
        {
          label: this.$t('dialog.nothing'),
          value: 0xffff,
        },
      ].concat(
        this.selectedAddressBook
          .concat(this.originAddressBook)
          .filter(address => {
            if (cache.includes(address.id)) {
              return false
            }
            cache.push(address.id)
            return true
          })
          .map(address => {
            return {
              label: address.name,
              value: address.id,
            }
          }),
      )
    },
    emergencySysLabel() {
      const labels = {
        0: this.$t('writeFreq.digitalEmergencyAlertSystem'),
        1: this.$t('writeFreq.analogEmergencyAlertSystem'),
        2: this.$t('writeFreq.emergencyAlertSystem'),
      }
      return labels[this.oneChannel.chType]
    },
    emergencySysIdList() {
      const def = [
        {
          label: this.$t('dialog.nothing'),
          value: 0xff,
        },
      ]
      let sourceList = this.digitalAlertList
      if (this.oneChannel.chType === 1) {
        sourceList = this.analogAlertList
      }

      return def.concat(
        sourceList.map(item => {
          return {
            label: item.name,
            value: item.id,
          }
        }),
      )
    },
    txPowerTypes() {
      // 发射功率	"0:低功率 1:高功率" 默认0
      return [
        {
          label: this.$t('dialog.low'),
          value: 0,
        },
        {
          label: this.$t('dialog.high'),
          value: 1,
        },
      ]
    },
    permissionConditionsList() {
      // 发射准许条件 "0:始终 1:信道空闲 2:可用彩色码(CTCSS/CDCSS)"
      return [
        {
          label: this.$t('writeFreq.always'),
          value: 0,
        },
        {
          label: this.$t('dialog.channelIdle'),
          value: 1,
        },
        {
          label: this.$t('dialog.availableColorCode'),
          value: 2,
          disabled: this.isVirtualCluster,
        },
      ]
    },
    callingTxPermitConditionList() {
      // 呼叫中发射准许条件 "0:同发射准许条件一致 1:始终  2:可用彩色码" 其中，只有在“时隙选择”为双时隙时，该参数才允许配置为“优先打断”
      const list = [
        {
          label: this.$t('writeFreq.sameLaunchPermCondition'),
          value: 0,
        },
        {
          label: this.$t('writeFreq.always'),
          value: 1,
        },
        {
          label: this.$t('dialog.availableColorCode'),
          value: 2,
          disabled: this.isVirtualCluster,
        },
      ]
      if (this.oneChannel.chType === 2) {
        list.push({
          label: this.$t('writeFreq.priorityInterrupt'),
          value: 3,
        })
      }
      return list
    },
    busyChannelLockList() {
      // 信道繁忙锁定 "0:始终  1:信道空闲  2:CTCSS/CDCSS"
      return [
        {
          label: this.$t('writeFreq.always'),
          value: 0,
        },
        {
          label: this.$t('dialog.channelIdle'),
          value: 1,
        },
        {
          label: 'CTCSS/CDCSS',
          value: 2,
        },
      ]
    },
    channelSettingComp() {
      return this.$refs.channelSetting
    },
    patrolConfigComp() {
      return this.$refs.patrolConfig
    },
    directMode() {
      return this.generalSettings.baseSettings.directMode
    },

    workAloneUnEnable() {
      return !this.aloneWork.enable
    },
    remindTimeMax() {
      const max = this.aloneWork.responseTime * 60 - 1
      return max > 255 ? 255 : max
    },
    upsideDownUnEnable() {
      return !this.upsideDown.enable
    },
    disableTriggerTilt() {
      return this.upsideDownUnEnable || this.upsideDown.triggerMode === 1
    },
    aloneWorkOptList() {
      // 单独工作响应操作 0 按钮  1 语音发射
      return [
        {
          label: this.$t('writeFreq.button'),
          value: 0,
        },
        {
          label: this.$t('writeFreq.voiceLaunch'),
          value: 1,
        },
      ]
    },
    triggerModeList() {
      // 触发方式 "0：仅倾斜 1：仅静止 2：倾斜或静止"
      return [
        {
          label: this.$t('writeFreq.onlyTilt'),
          value: 0,
        },
        {
          label: this.$t('writeFreq.onlyStill'),
          value: 1,
        },
        {
          label: this.$t('writeFreq.tiltOrStill'),
          value: 2,
        },
      ]
    },
    triggerTiltList() {
      // 触发倾斜度 "0:30度 1:45度 2:60度"
      return [
        {
          label: 30,
          value: 0,
        },
        {
          label: 45,
          value: 1,
        },
        {
          label: 60,
          value: 2,
        },
      ]
    },
    writeDataOption() {
      const list = [
        {
          type: TableIndex.GeneralSettings,
          failedMsg: this.$t('msgbox.writeRegularSettingsFailed'),
        },
        {
          type: TableIndex.UISettings,
          failedMsg: this.$t('msgbox.writeUiConfigFailed'),
        },
        {
          type: TableIndex.KeysSettings,
          failedMsg: this.$t('msgbox.writeDeySettingsFailed'),
        },
        {
          type: TableIndex.MenuSettings,
          failedMsg: this.$t('msgbox.writeMenuFailed'),
        },
        {
          type: TableIndex.GpsSettings,
          failedMsg: this.$t('msgbox.writeGpsDataFailed'),
        },
        {
          type: TableIndex.PatrolSystemSettings,
          failedMsg: this.$t('msgbox.writePatrolSystemConfigFailed'),
        },
        {
          type: TableIndex.UpsideDownSettings,
          failedMsg: this.$t('msgbox.writeUpsideDownConfigFailed'),
        },
        {
          type: TableIndex.AloneWorkSettings,
          failedMsg: this.$t('msgbox.writeWorkAloneConfigFailed'),
        },
        {
          type: TableIndex.DigitalAlarmSettings,
          failedMsg: this.$t('msgbox.writeDigitalAlarmFailed'),
        },
        {
          type: TableIndex.AnalogAlarmSettings,
          failedMsg: this.$t('msgbox.writeAnalogEmergencyAlertFailed'),
        },
        {
          type: TableIndex.ScanConfigSettings,
          failedMsg: this.$t('msgbox.writeScanConfigFailed'),
        },
        {
          type: TableIndex.ScanListSettings,
          failedMsg: this.$t('msgbox.writeScanListFailed'),
        },
        {
          type: TableIndex.RoamConfigSettings,
          failedMsg: this.$t('msgbox.writeRoamConfigFailed'),
        },
        {
          type: TableIndex.RoamListSettings,
          failedMsg: this.$t('msgbox.writeRoamListFailed'),
        },
        {
          type: TableIndex.DmrConfigSettings,
          failedMsg: this.$t('msgbox.writeDmrBasicConfigFailed'),
        },
        {
          type: TableIndex.EncryptionConfigSettings,
          failedMsg: this.$t('msgbox.writeEncryptConfigFailed'),
        },
        {
          type: TableIndex.EncryptionListSettings,
          failedMsg: this.$t('msgbox.writeEncryptKeyFailed'),
        },
        {
          type: TableIndex.EncryptionARC4List,
          failedMsg: this.$t('msgbox.writeEncryptionARC4KeyFailed'),
        },
        {
          type: TableIndex.EncryptionARSList,
          failedMsg: this.$t('msgbox.writeEncryptionARSKeyFailed'),
        },
        {
          type: TableIndex.DMRContactsSettings,
          failedMsg: this.$t('msgbox.writeAddressBookFailed'),
        },
        {
          type: TableIndex.ReceiveGroupSettings,
          failedMsg: this.$t('msgbox.writeReceivingGroupFailed'),
        },
        {
          type: TableIndex.PreMakeSmsSettings,
          failedMsg: this.$t('msgbox.writeSMSFailed'),
          option: { limit: 1 },
        },
        {
          type: TableIndex.ZoneSettings,
          failedMsg: this.$t('msgbox.writeZoneDataFailed'),
        },
        {
          type: TableIndex.ChannelSettings,
          failedMsg: this.$t('msgbox.writeChannelDataFailed'),
          option: { limit: 2 },
        },
        {
          type: TableIndex.PhoneBookSettings,
          failedMsg: this.$t('msgbox.writePhoneBookFailed'),
        },
        {
          type: TableIndex.VirtualCluster,
          failedMsg: this.$t('msgbox.writeVirtualClusterFailed'),
        },
        {
          type: TableIndex.SiteInfo,
          failedMsg: this.$t('msgbox.writeSiteInfoFailed'),
        },
      ]

      if (this.isModifyReadPwassword) {
        list.push({
          type: TableIndex.WriteFreqReadPassword,
          failedMsg: this.$t('msgbox.writeReadPasswordFailed'),
        })
      }
      if (this.isModifyWritePwassword) {
        list.push({
          type: TableIndex.WriteFreqWritePassword,
          failedMsg: this.$t('msgbox.writeWritePasswordFailed'),
        })
      }

      return list
    },
    getClassInstance() {
      return getClassInstance
    },
    Model() {
      return Model
    },

    chDisplayModeList() {
      // 信道显示模式，0 频率显示，1 信道显示，2 频率+信道显示
      return [
        {
          label: this.$t('dialog.freqDisplay'),
          value: 0,
        },
        {
          label: this.$t('dialog.chDisplay'),
          value: 1,
        },
        {
          label: this.$t('dialog.freqAndChDisplay'),
          value: 2,
        },
      ]
    },
    contactContentList() {
      // 显示联系人内容 0：联系人别名和ID 1：联系人别名 2：联系人ID
      return [
        {
          label: this.$t('writeFreq.contactAliasAndId'),
          value: 0,
        },
        {
          label: this.$t('writeFreq.contactAlias'),
          value: 1,
        },
        {
          label: this.$t('writeFreq.contactId'),
          value: 2,
        },
      ]
    },
    callingOutToneList() {
      // 呼出提示音 "0:关闭 1:开启 2:模拟允许 3:数字允许"，默认值1 （即原 呼叫允许音）
      return [
        {
          label: this.$t('dialog.close'),
          value: 0,
        },
        {
          label: this.$t('dialog.enable'),
          value: 1,
        },
        {
          label: this.$t('writeFreq.analogAllowed'),
          value: 2,
        },
        {
          label: this.$t('writeFreq.digitalAllowed'),
          value: 3,
        },
      ]
    },
    chooseAnnouncerList() {
      // 选择播音人 51：男声1 52：男声2 3：女声1 53：女声2 55：女童声，参数值为10进制
      return [
        {
          label: this.$t('writeFreq.maleVoice1'),
          value: 51,
        },
        {
          label: this.$t('writeFreq.maleVoice2'),
          value: 52,
        },
        {
          label: this.$t('writeFreq.femaleVoice1'),
          value: 3,
        },
        {
          label: this.$t('writeFreq.femaleVoice2'),
          value: 53,
        },
        {
          label: this.$t('writeFreq.girlVoice'),
          value: 55,
        },
      ]
    },
    backlightList() {
      // 背光灯模式 "0:常暗 1:常亮 2:自动"
      return [
        {
          label: this.$t('dialog.close'),
          value: 0,
        },
        {
          label: this.$t('dialog.enable'),
          value: 1,
        },
        {
          label: this.$t('writeFreq.auto'),
          value: 2,
        },
      ]
    },
    muteAll() {
      return this.uiSettings.callingSoundSettings.muteAll
    },
    notStealthMode() {
      return !this.uiSettings.stealthMode
    },
    disabledAllLED() {
      return !this.uiSettings.ledSettings.disabledAllLED
    },
    vibrationEnable() {
      return !this.uiSettings.vibrationSettings.vibration
    },
    autoKeyboardLock() {
      return this.uiSettings.autoKeyboardLock
    },
    defaultKeyboardInputList() {
      // 默认键盘输入 "0：无 1：单键呼叫 2：允许" 默认0
      return [
        {
          label: this.$t('dialog.nothing'),
          value: 0,
        },
        {
          label: this.$t('writeFreq.oneTouchCall'),
          value: 1,
        },
        {
          label: this.$t('writeFreq.allow'),
          value: 2,
        },
      ]
    },
    defaultKnobFuncList() {
      // 默认旋钮功能 "0：仅音量 1：仅信道 2：信道和音量"
      return [
        {
          label: this.$t('writeFreq.onlyVolume'),
          value: 0,
        },
        {
          label: this.$t('writeFreq.onlyChannel'),
          value: 1,
        },
        {
          label: this.$t('writeFreq.ChannelAndVolume'),
          value: 2,
        },
      ]
    },
    keyNames() {
      // [橙色键,功能1键,功能2键 ,方向上键,方向下键 ,确认键,返回键,拨号键,挂机键]
      return [
        this.$t('writeFreq.lockKeys.orangeKey'),
        this.$t('writeFreq.lockKeys.func1Key'),
        this.$t('writeFreq.lockKeys.func2Key'),
        this.$t('writeFreq.lockKeys.upKey'),
        this.$t('writeFreq.lockKeys.downKey'),
        this.$t('writeFreq.lockKeys.confirmKey'),
        this.$t('writeFreq.lockKeys.backKey'),
        this.$t('writeFreq.lockKeys.p1Key'),
        this.$t('writeFreq.lockKeys.p2Key'),
      ]
    },
    oneTouchKeyNames() {
      // 单键呼叫：1～5
      // 数字键盘：0～9,*,#
      const oneTouchKeys = []
      for (let i = 1; i < 6; i++) {
        oneTouchKeys.push(this.$t('writeFreq.oneTouchKey', { key: i }))
      }
      const numericKeyboards = []
      for (let i = 1; i < 10; i++) {
        numericKeyboards.push(this.$t('writeFreq.numericKeyboard', { key: i }))
      }
      numericKeyboards.push(this.$t('writeFreq.numericKeyboard', { key: '0' }))
      numericKeyboards.push(this.$t('writeFreq.numericKeyboard', { key: '*' }))
      numericKeyboards.push(this.$t('writeFreq.numericKeyboard', { key: '#' }))

      return oneTouchKeys.concat(numericKeyboards)
    },
    callModeList() {
      // 0: 数字模式 0xFF 无
      return [
        {
          label: this.$t('dialog.nothing'),
          value: 0xff,
        },
        {
          label: this.$t('writeFreq.digitalMode'),
          value: 0,
        },
      ]
    },
    disabledMenuAll() {
      return !this.menuSettings.menuEnable
    },
    disabledMenuDevice() {
      return this.disabledMenuAll || !this.menuSettings.interfaceSettings.enable
    },
    invalidPowerOnPwd() {
      return this.generalSettings.powerOnPwd.length !== 6
    },
    disabledMenuPowerOnPwd() {
      return this.disabledMenuDevice || this.invalidPowerOnPwd
    },
    disabledMenuChannel() {
      return this.disabledMenuAll || !this.menuSettings.channelSettings.enable
    },
    disabledMenuEncrypt() {
      return this.disabledMenuChannel || !this.menuSettings.encrypt
    },
    disabledMenuDeviceInfo() {
      return (
        this.disabledMenuAll || !this.menuSettings.deviceInfoSettings.enable
      )
    },
    disabledMenuScan() {
      return !this.menuSettings.scanSettings.enable
    },
    disabledMenuRoam() {
      return !this.menuSettings.roamSettings.enable
    },
    disabledMenuCallRecord() {
      return !this.menuSettings.callRecordSettings.enable
    },
    disabledMenuAnnex() {
      return !this.menuSettings.annex
    },
    disabledMenuGps() {
      return this.disabledMenuAnnex || !this.menuSettings.gpsSettings.enable
    },
    disabledMenuPatrol() {
      return this.disabledMenuAnnex || !this.menuSettings.patrolSettings.enable
    },
    disabledMenuRecord() {
      return this.disabledMenuAnnex || !this.menuSettings.recordSettings.enable
    },
    disabledMenuContacts() {
      return !this.menuSettings.contacts
    },
    disabledMenuContactList() {
      return !this.menuSettings.contactList
    },
    disabledMenuManualDial() {
      return !this.menuSettings.dialSettings.manualDial
    },
    disabledMenuControl() {
      return !this.menuSettings.controlSettings.enable
    },
    disabledMenuSms() {
      return !this.menuSettings.smsSettings.enable
    },
    disabledMenuPhoneBook() {
      return !this.menuSettings.phoneBookSettings.enable
    },
    gpsSettingsEnable() {
      return this.gpsSettings.mode !== 0
    },
    gpsModeList() {
      // GPS开关 "0:关 1:省电 2:高性能"
      return [
        {
          label: this.$t('writeFreq.off'),
          value: 0,
        },
        {
          label: this.$t('writeFreq.powerSavingMode'),
          value: 1,
        },
        {
          label: this.$t('writeFreq.highPerformanceMode'),
          value: 2,
        },
      ]
    },
    navTypeList() {
      // 0:GPS 1:北斗 2：GPS+北斗"
      return [
        {
          label: 'GPS',
          value: 0,
        },
        {
          label: this.$t('writeFreq.beidou'),
          value: 1,
        },
        {
          label: this.$t('writeFreq.gpsAndBeidou'),
          value: 2,
        },
      ]
    },
    disableEmergencyAlarm() {
      return !this.patrolConfig.emergencyAlarm
    },
    alarmTypeList() {
      return [
        {
          label: this.$t('dialog.common'),
          value: 0,
        },
        {
          label: this.$t('dialog.silent'),
          value: 1,
        },
      ]
    },
    callingContactList() {
      return [
        {
          label: this.$t('writeFreq.theSelected'),
          value: 0xfffe,
        },
      ].concat(this.defaultAddressList.filter(item => item.value !== 0xffff))
    },
    disableMonitorTrack() {
      return !this.patrolConfig.enableMonitorTrack
    },
    disableRfid() {
      return !this.patrolConfig.rfidSettings.enable
    },
    rfidModeList() {
      // 有源RFID模式 "0：到位自动读卡 1：按键触发读卡"
      return [
        {
          label: this.$t('dialog.autoCardReading'),
          value: 0,
        },
        {
          label: this.$t('dialog.triggerCardReading'),
          value: 1,
        },
      ]
    },
    rfidPowerList() {
      // 功率 "0(-18dbm) 1(-12dbm)  2(-6dbm)  3(0dbm)"
      return [
        {
          label: '-18dbm',
          value: 0,
        },
        {
          label: '-12dbm',
          value: 1,
        },
        {
          label: '-6dbm',
          value: 2,
        },
        {
          label: '0dbm',
          value: 3,
        },
      ]
    },
    rfidReplyList() {
      // 应答 "0：数传指令应答 1：采用芯片自动应答机制"
      return [
        {
          label: this.$t('dialog.dataTrsCmdRes'),
          value: 0,
        },
        {
          label: this.$t('dialog.chipAutoResMechanism'),
          value: 1,
        },
      ]
    },
    digitalAlertRules() {
      return {
        name: [validateRules.required(['blur'])],
      }
    },
    digitalAlertTypeList() {
      // 警报类型 "0:禁止 1:仅鸣笛 2:常规 3:静默 4:静默加语音 5:警报鸣笛"
      return [
        {
          label: this.$t('writeFreq.forbid'),
          value: 0,
        },
        {
          label: this.$t('writeFreq.onlyWhistle'),
          value: 1,
        },
        {
          label: this.$t('dialog.common'),
          value: 2,
        },
        {
          label: this.$t('dialog.silent'),
          value: 3,
        },
        {
          label: this.$t('writeFreq.silenceAndVoice'),
          value: 4,
        },
        {
          label: this.$t('writeFreq.alarmAndWhistle'),
          value: 5,
        },
      ]
    },
    analogAlertTypeList() {
      // 警报类型 "0:禁止 1:仅鸣笛"
      return [
        {
          label: this.$t('writeFreq.forbid'),
          value: 0,
        },
        {
          label: this.$t('writeFreq.onlyWhistle'),
          value: 1,
        },
      ]
    },
    disableDigitalAlertMode() {
      return [0, 1, 5].includes(this.digitalAlert.type)
    },
    digitalAlertModeList() {
      // 警报模式 "0:紧急警报 1:紧急警报和呼叫 2:仅紧急呼叫"
      return [
        {
          label: this.$t('dialog.emergency'),
          value: 0,
        },
        {
          label: this.$t('writeFreq.emergencyAlarmAndCall'),
          value: 1,
        },
        {
          label: this.$t('writeFreq.emergencyAlarmAndVoice'),
          value: 2,
        },
      ]
    },
    disableReplyChannel() {
      return [0, 1].includes(this.digitalAlert.type)
    },
    replyChannelList() {
      // 过滤没有设置发射组或是模拟信道类型的信道
      const list = this.channelDataList.filter(channel => {
        return channel.chType !== 1 && channel.defaultTarget !== 0xffff
      })

      return [
        {
          label: this.$t('writeFreq.theSelected'),
          value: 0xfffe,
        },
      ].concat(
        list.map(channel => {
          return {
            label: channel.chName,
            value: channel.chId,
          }
        }),
      )
    },
    disabledAutoEmergencyCall() {
      return this.disableDigitalAlertMode || this.digitalAlert.mode === 0
    },
    disablePoliteRetry() {
      return (
        [0, 1].includes(this.digitalAlert.type) || this.digitalAlert.mode === 2
      )
    },
    politeRetry: {
      get() {
        return this.digitalAlert.politeRetry === 0xffff
          ? -1
          : this.digitalAlert.politeRetry
      },
      set(val) {
        this.digitalAlert.politeRetry = val === -1 ? 0xffff : val
      },
    },
    disableAnalogAlertMode() {
      return [0, 1, 5].includes(this.analogAlert.type)
    },
    disableAnalogReplyChannel() {
      return [0, 1].includes(this.analogAlert.type)
    },
    disabledAnalogAutoEmergencyCall() {
      return this.disableAnalogAlertMode || this.analogAlert.mode === 0
    },
    disableAnalogPoliteRetry() {
      return (
        [0, 1].includes(this.analogAlert.type) || this.analogAlert.mode === 2
      )
    },
    analogPoliteRetry: {
      get() {
        return this.analogAlert.politeRetry === 0xffff
          ? -1
          : this.analogAlert.politeRetry
      },
      set(val) {
        this.analogAlert.politeRetry = val === -1 ? 0xffff : val
      },
    },
    alarmSquelchModeList() {
      // 报警静噪模式 "0：载波 1：亚音"
      return [
        {
          label: this.$t('writeFreq.carrier'),
          value: 0,
        },
        {
          label: this.$t('writeFreq.ctcssCdcss'),
          value: 1,
        },
      ]
    },
    disEncryptConfigEnable() {
      return !this.encryptConfig.enable
    },
    disChannelEncrypt() {
      return (
        !this.oneChannel.encryptionSettings.enable ||
        this.disEncryptConfigEnable
      )
    },
    disAdvancedEncryption() {
      return (
        this.disChannelEncrypt ||
        ![5, 6].includes(this.oneChannel.encryptionSettings.type)
      )
    },
    defaultTxChannelNetworkList() {
      // 默认发射信道网络 "0：数字 1：模拟"
      return [
        {
          label: this.$t('dialog.digital'),
          value: 0,
        },
        {
          label: this.$t('dialog.analog'),
          value: 1,
        },
      ]
    },
    OperationOrderTable() {
      return OperationOrderTable
    },
    TableIndex() {
      return TableIndex
    },
  },
  watch: {
    'analogAlert.mode'(val) {
      if (val === 0) {
        this.analogAlert.autoEmergencyCall = false
      }
    },
    'digitalAlert.mode'(val) {
      if (val === 0) {
        this.digitalAlert.autoEmergencyCall = false
      }
    },
    'aloneWork.responseTime'(val) {
      const responseTime = val * 60
      if (responseTime < this.aloneWork.remindTime) {
        this.aloneWork.remindTime = responseTime - 1
      }
    },
    'upsideDown.entryDelay'(val) {
      if (val < this.upsideDown.preHintTime) {
        this.upsideDown.preHintTime = val
      }
    },
    // 联系人显示内容显示别名时，可以不显示陌生号码
    disableStrangeNumber(val) {
      if (val) {
        this.generalSettings.concatSettings.showStrangeNumber = true
      }
    },
    smsContent: {
      deep: true,
      handler(data) {
        this.$nextTick(() => {
          // 短信内容变化时，检测按键定义中单键呼叫功能的设置
          this.detectButtonDefinedFromSmsChange()
        })
      },
    },
    currChannelId(val) {
      const oneChannel = this.channelDataList.find(item => item.chId === val)
      if (oneChannel) {
        this.oneChannel = oneChannel
      }
    },
    currDigitalAlertId(val) {
      const digitalAlert = this.digitalAlertList.find(item => item.id === val)
      if (digitalAlert) {
        this.digitalAlert = digitalAlert
      }
    },
    currAnalogAlertId(val) {
      const analogAlert = this.analogAlertList.find(item => item.id === val)
      if (analogAlert) {
        this.analogAlert = analogAlert
      }
    },
    //todo 循环修改点
    sendCount(val) {
      this.patrolConfig.sendCount = val === 0 ? 0xff : val
    },
    disabledMenuPhoneBook(val) {
      for (const key in this.menuSettings.phoneBookSettings) {
        this.menuSettings.phoneBookSettings[key] = !val
      }
    },
    disabledMenuSms(val) {
      for (const key in this.menuSettings.smsSettings) {
        this.menuSettings.smsSettings[key] = !val
      }
    },
    disabledMenuContacts(val) {
      this.menuSettings.newContact = !val
      this.menuSettings.contactList = !val
      this.menuSettings.dialSettings.manualDial = !val
      this.menuSettings.controlSettings.enable = !val
    },
    disabledMenuContactList(val) {
      for (const key in this.menuSettings.contactSettings) {
        this.menuSettings.contactSettings[key] = !val
      }
    },
    disabledMenuManualDial(val) {
      for (const key in this.menuSettings.dialSettings) {
        this.menuSettings.dialSettings[key] = !val
      }
    },
    disabledMenuControl(val) {
      for (const key in this.menuSettings.controlSettings) {
        this.menuSettings.controlSettings[key] = !val
      }
    },
    disabledMenuRecord(val) {
      for (const key in this.menuSettings.recordSettings) {
        this.menuSettings.recordSettings[key] = !val
      }
    },
    disabledMenuPatrol(val) {
      for (const key in this.menuSettings.patrolSettings) {
        this.menuSettings.patrolSettings[key] = !val
      }
    },
    disabledMenuGps(val) {
      for (const key in this.menuSettings.gpsSettings) {
        this.menuSettings.gpsSettings[key] = !val
      }
    },
    disabledMenuAnnex(val) {
      this.menuSettings.gpsSettings.enable = !val
      this.menuSettings.patrolSettings.enable = !val
      this.menuSettings.recordSettings.enable = !val
    },
    disabledMenuCallRecord(val) {
      for (const key in this.menuSettings.callRecordSettings) {
        this.menuSettings.callRecordSettings[key] = !val
      }
    },
    disabledMenuRoam(val) {
      for (const key in this.menuSettings.roamSettings) {
        this.menuSettings.roamSettings[key] = !val
      }
    },
    disabledMenuScan(val) {
      for (const key in this.menuSettings.scanSettings) {
        this.menuSettings.scanSettings[key] = !val
      }
    },
    disabledMenuDeviceInfo(val) {
      for (const key in this.menuSettings.deviceInfoSettings) {
        this.menuSettings.deviceInfoSettings[key] = !val
      }
    },
    disabledMenuEncrypt(val) {
      for (const key in this.menuSettings.encryptSettings) {
        this.menuSettings.encryptSettings[key] = !val
      }
    },
    disabledMenuChannel(val) {
      const keys = ['powerLevel', 'offline', 'squelchLevel']
      for (const key in this.menuSettings.channelSettings) {
        if (keys.includes(key)) {
          continue
        }
        this.menuSettings.channelSettings[key] = !val
      }
      for (const key in this.menuSettings.channel2Settings) {
        this.menuSettings.channel2Settings[key] = !val
      }
      this.menuSettings.encrypt = !val
    },
    disabledMenuPowerOnPwd(val) {
      this.menuSettings.interface2Settings.powerOnPwd = false
    },
    disabledMenuDevice(val) {
      for (const key in this.menuSettings.interfaceSettings) {
        this.menuSettings.interfaceSettings[key] = !val
      }
      for (const key in this.menuSettings.interface2Settings) {
        // 如果常规设置中开机密码没有设置，则开机密码菜单不可用
        if (key === 'powerOnPwd') {
          if (!this.generalSettings.powerOnPwd) {
            this.menuSettings.interface2Settings[key] = false
            continue
          }
        }
        this.menuSettings.interface2Settings[key] = !val
      }
      const keys = ['powerLevel', 'offline', 'squelchLevel']
      for (let i = 0; i < keys.length; i++) {
        const k = keys[i]
        this.menuSettings.channelSettings[k] = !val
      }
    },
    disabledMenuAll(val) {
      if (val) {
        this.menuSettings.interfaceSettings.enable = false
        this.menuSettings.channelSettings.enable = false
        this.menuSettings.deviceInfoSettings.enable = false
      }
    },
    notStealthMode(val) {
      if (val) {
        const properties = [
          'shieldedLedLight',
          'shieldedKeyboardLock',
          'stealthModeBacklight',
          'stealthModeVibrationShield',
          'shieldedHeadphones',
          'shieldedMicrophone',
        ]
        properties.forEach(prop => {
          this.uiSettings.stealthModeSettings[prop] = false
        })
      }
    },
    disabledAllLED(val) {
      this.uiSettings.ledSettings.emittingLed = !val
      this.uiSettings.ledSettings.receiveLed = !val
      this.uiSettings.ledSettings.scanRoamStatusLed = !val
      this.uiSettings.ledSettings.lowBatteryLed = !val
      this.uiSettings.ledSettings.callHangsLed = !val
    },
    vibrationEnable(val) {
      this.uiSettings.vibrationSettings.singleCall = !val
      this.uiSettings.vibrationSettings.message = !val
      this.uiSettings.vibrationSettings.callTone = !val
    },
    // mixins计算属性
    selectDeviceData(data) {
      this.clearPrivateConfig()

      this.$nextTick(() => {
        if (data) {
          // 将选中的设备中关于信道等数据同步到界面中
          this.syncDeviceDataIntoConfig(data)
        }
      })
    },
    '$i18n.locale'(val) {
      // 切换语言后，重新更新菜单树节点名称
      this.updateNodeTitleFromLocaleChange()
      this.treeLoaded()
    },
    isConnectNetworking(val) {
      if (val) {
        // 联网下，呼叫等级必须大于0
        if (this.oneChannel.voicePriority === 0) {
          this.oneChannel.voicePriority = 1
        }
        // 联网下，只能使用漫游
        if (this.oneChannel.channelScanList === 0xff) {
          this.oneChannel.baseSettings.autoScan = false
          this.oneChannel.callingSettings.autoRoam = false
        } else if ((this.oneChannel.channelScanList + '').startsWith('s')) {
          // 扫描
          this.oneChannel.channelScanList = 0xff
          this.oneChannel.baseSettings.autoScan = false
        } else if ((this.oneChannel.channelScanList + '').startsWith('r')) {
          // 漫游
          // this.oneChannel.callingSettings.autoRoam = false
        }
        this.oneChannel.baseSettings.emergencyAlarmIndication = false
        this.oneChannel.baseSettings.emergencyCallTip = false
        this.oneChannel.emergencySysId = 0xff
      } else {
        this.oneChannel.baseSettings.localCall = false
      }
    },
    onlyReceive(val) {
      if (val) {
        this.oneChannel.callingSettings.ipSiteConnect = false
      }
    },
    ipSiteConnect(val) {
      // IP站点连接未勾选，则扫描列表不可能选择漫游组和自动漫游配置
      if (!val) {
        this.oneChannel.scanList = 0xff
        this.oneChannel.callingSettings.autoRoam = false
        // 重置漫游组已经配置的信道，删除当前信道
        this.resetRoamGroupSelectedChannel()
      }
    },
    sameFreq(val) {
      if (val) {
        this.oneChannel.callingSettings.ipSiteConnect = false
        // 收、发频率一致时，不允许配置虚拟集群
        this.oneChannel.encryptionSettings.virtualCluster = false
      }
    },
    disableSVTSiteInfo(val) {
      if (val) {
        //未勾选虚拟集群时，svt站点信息设置为‘无’
        this.oneChannel.SVTSiteInfo = 0xff
      }
    },
    'alertConfig.aloneWorkTime'(val) {
      const aloneWorkTime = val * 60
      if (this.alertConfig.aloneWorkRemindTime > aloneWorkTime) {
        this.alertConfig.aloneWorkRemindTime = aloneWorkTime
      }
    },
    'oneChannel.baseSettings.emergencyAlarmIndication'(val) {
      if (!val) {
        this.oneChannel.baseSettings.emergencyAlarmConfirm = false
      }
    },
    'oneChannel.receiveToneType'(val) {
      this.oneChannel.rxSquelchMode = val === 0 ? 0 : 1
    },
    'oneChannel.channelScanList'(val) {
      // 如果扫描/漫游组ID为0xFF，则无扫描/漫游列表类型
      if (val === 0xff) {
        this.oneChannel.scanRoamListType = 0xff
        this.oneChannel.scanList = 0xff
        this.oneChannel.baseSettings.autoScan = false
        this.oneChannel.callingSettings.autoRoam = false
        return
      }
      // val以'r'开头则为漫游列表类型，'s'为扫描列表类型
      const isScanList = val.startsWith('s')
      const isRoamList = val.startsWith('r')
      this.oneChannel.scanRoamListType = isRoamList ? 1 : isScanList ? 0 : 0xff
      this.oneChannel.scanList = parseInt(val.replace(/r|s/gi, '') || '0')
      if (isScanList) {
        this.oneChannel.callingSettings.autoRoam = false
      }
      if (isRoamList) {
        this.oneChannel.baseSettings.autoScan = false
      }
    },
    'oneChannel.permitConditions'(val) {
      if (val === 0) {
        this.oneChannel.callingSettings.singleCallConfirm = false
      }
    },
    disEncryptConfigEnable(val) {
      if (val) {
        this.oneChannel.encryptionSettings.enable = false
      }
    },
  },
  components: {
    WriteFreqFooter,
    selectDevice,
    deviceInfo,
    TableTree,
    addressBook: () =>
      import('@/components/interphoneWf/common/addressBook.vue'),
    phoneBook: () => import('@/components/interphoneWf/common/phoneBook.vue'),
    frequencyMhz: () => import('@/components/common/FrequencyMhz'),
    shortMessage: () => import('@/components/interphoneWf/common/shortMessage'),
    receiveGroup: () => import('@/components/interphoneWf/common/receiveGroup'),
    scanGroupList: () =>
      import('@/components/interphoneWf/common/scanGroupList'),
    roamGroupList: () =>
      import('@/components/interphoneWf/common/roamGroupList'),
    zoneChannelTable: () =>
      import('@/components/interphoneWf/common/zoneChannelTable'),
    bfInputNumber: () => import('@/components/common/bfInputNumber'),
    td930TimeZone: defineAsyncComponent(
      () => import('@/components/common/timezone/td930-timezone.vue'),
    ),
    multistageZone: () =>
      import('@/components/interphoneWf/common/multistageZone'),
    preSetChannel: () =>
      import('@/components/interphoneWf/common/preSetChannel'),
    virtualCluster930svtV0: defineAsyncComponent(
      () =>
        import(
          '@/components/interphoneWf/common/virtualCluster/virtualCluster_930svt_v0'
        ),
    ),
    freqMapOffset: defineAsyncComponent(
      () => import('@/components/interphoneWf/common/freqMapOffset'),
    ),
    siteInfo: defineAsyncComponent(
      () => import('@/components/interphoneWf/common/SiteInfo.vue'),
    ),
  },
  mounted() {
    this.initDigitAlertList()
    this.initAnalogAlertList()
    this.initScanGroupList()
    this.initRoamGroupList()
    this.initSiteInfoList()

    bfglob.on('wf:redrawTree', this.redrawViewport)
  },
  beforeUnmount() {
    bfglob.off('wf:redrawTree', this.redrawViewport)
  },
}
</script>

<style lang="scss">
@use '@/css/interphoneWf/button-define.scss' as *;
@use '@/css/interphoneWf/tree-card-layout.scss' as *;
</style>
