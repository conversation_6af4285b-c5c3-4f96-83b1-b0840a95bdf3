<template>
  <el-form
    ref="zoneIdSetting"
    :model="zoneSetting"
    label-position="top"
    :rules="zoneIdRules"
    class="zoneId-setting"
  >
    <el-form-item :label="$t('dialog.areaName')" prop="name">
      <el-input v-model="zoneSetting.name" :maxlength="16" />
    </el-form-item>
    <el-form-item
      v-if="showSubZone"
      :label="$t('dialog.firstLevelArea')"
      prop="userName"
    >
      <el-input :value="mainZoneName" :maxlength="16" disabled />
    </el-form-item>
    <el-form-item
      v-else-if="showUserZone"
      :label="$t('dialog.secondaryArea')"
      prop="userName"
    >
      <el-input :value="subZoneName" :maxlength="16" disabled />
    </el-form-item>
  </el-form>
</template>

<script>
import { DefModel } from '@/writingFrequency/repeater'
import bfutil from '@/utils/bfutil'
import validateRules from '@/utils/validateRules'
import bfproto from '@/modules/protocol'

const TableId = bfproto.lookupEnum('TableId')
const OneMainZone = {
  // 值范围：0-3
  id: 0,
  name: '',
}
const OneSubZone = {
  // 值范围：0-2999
  id: 0,
  name: '',
  mainId: 0,
}
const OneUserZone = {
  // 值范围：0-2999
  id: 0,
  name: '',
  // 值范围：0-2999
  subId: 0,
  // 对应信道成员ID
  // 值范围：0-3775
  chId: [],
  // 值范围：0-16
  chNum: 0,
}

export default {
  name: 'TR925ZoneIdSetting',
  props: {
    repeaterData: {
      type: [Object, undefined],
    },
    repeater: {
      type: String,
      default: '',
    },
    getRepeaterId: {
      type: Function,
      default: bfutil.noop,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    packageName: {
      type: String,
      default: '',
    },
    deviceModel: {
      type: String,
      default: DefModel,
    },
    saveMethod: {
      type: Function,
      default: bfutil.noop,
    },
    dataId: {
      type: Number,
      default: -1,
    },
    tableId: {
      type: Number,
      default: TableId.RepeaterMainZone,
    },
  },
  data() {
    return {
      zoneSetting: {
        ...OneMainZone,
        ...OneSubZone,
        ...OneUserZone,
      },
    }
  },
  methods: {
    getMainZoneData(mainId) {
      return Object.keys(this.mainZones || {})
        .filter(key => {
          return this.mainZones[key].id === mainId
        })
        .map(key => {
          return this.mainZones[key]
        })
        .shift()
    },
    getSubZoneData(subId) {
      return Object.keys(this.subZones || {})
        .filter(key => {
          return this.subZones[key].id === subId
        })
        .map(key => {
          return this.subZones[key]
        })
        .shift()
    },
    getUserZoneData(userId) {
      return Object.keys(this.userZones || {})
        .filter(key => {
          return this.userZones[key].id === userId
        })
        .map(key => {
          return this.userZones[key]
        })
        .shift()
    },
    autoFillMainZone() {
      const oneMainZone = this.getMainZoneData(this.dataId)
      this.zoneSetting = {
        ...this.zoneSetting,
        ...oneMainZone,
      }
    },
    autoFillSubZone() {
      const oneSubZone = this.getSubZoneData(this.dataId)
      this.zoneSetting = {
        ...this.zoneSetting,
        ...oneSubZone,
      }
    },
    autoFillUserZone() {
      const oneSubZone = this.getUserZoneData(this.dataId)
      this.zoneSetting = {
        ...this.zoneSetting,
        ...oneSubZone,
      }
    },
    autoFillData(tableId = this.tableId) {
      switch (tableId) {
        case TableId.RepeaterMainZone:
          this.autoFillMainZone()
          break
        case TableId.RepeaterSubZone:
          this.autoFillSubZone()
          break
        case TableId.RepeaterUserZone:
          this.autoFillUserZone()
          break
      }
    },
    getAreaZoneDataByIds(tableId, dataId) {
      switch (tableId) {
        case TableId.RepeaterMainZone:
          return this.getMainZoneData(dataId)
        case TableId.RepeaterSubZone:
          return this.getSubZoneData(dataId)
        case TableId.RepeaterUserZone:
          return this.getUserZoneData(dataId)
        default:
          return undefined
      }
    },
    saveZoneDataName(tableId, dataId, data) {
      switch (tableId) {
        case TableId.RepeaterMainZone:
          this.mainZones[dataId] = data
          break
        case TableId.RepeaterSubZone:
          this.subZones[dataId] = data
          break
        case TableId.RepeaterUserZone:
          this.userZones[dataId] = data
          break
      }
    },
  },
  computed: {
    zoneIdRules() {
      return {
        name: [validateRules.required()],
      }
    },
    showSubZone() {
      return this.tableId === TableId.RepeaterSubZone
    },
    showUserZone() {
      return this.tableId === TableId.RepeaterUserZone
    },
    mainZoneName() {
      if (!this.showSubZone) {
        return ''
      }
      const mainZone = this.getMainZoneData(this.dataId)

      return (mainZone && mainZone.name) || ''
    },
    subZoneName() {
      if (!this.showUserZone) {
        return ''
      }
      const subZone = this.getSubZoneData(this.dataId)

      return (subZone && subZone.name) || ''
    },
    wfSettings() {
      return this.repeaterData && this.repeaterData.writeFrequencySetting
    },
    mainZones() {
      return (
        this.wfSettings && this.wfSettings[TableId[TableId.RepeaterMainZone]]
      )
    },
    subZones() {
      return (
        this.wfSettings && this.wfSettings[TableId[TableId.RepeaterSubZone]]
      )
    },
    userZones() {
      return (
        this.wfSettings && this.wfSettings[TableId[TableId.RepeaterUserZone]]
      )
    },
  },
  watch: {
    // 根据表的id和dataId的变化，读取对应数据，填充到表单中
    tableId(val) {
      this.autoFillData(val)
    },
    dataId(val) {
      this.autoFillData(this.tableId)
    },
    // 修改区域数据名称时，自动保存到相应的数据结构中
    'zoneSetting.name'(val) {
      const zoneData = this.getAreaZoneDataByIds(this.tableId, this.dataId)
      if (!zoneData || zoneData.name === val) {
        return
      }
      this.saveZoneDataName(this.tableId, this.dataId, {
        ...zoneData,
        name: val,
      })
    },
  },
}
</script>

<style></style>
