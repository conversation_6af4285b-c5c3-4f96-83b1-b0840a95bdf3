<template>
  <el-form
    ref="groupingSetting"
    :model="groupData"
    label-width="110px"
    :rules="groupingRules"
    class="grouping-setting"
  >
    <el-form-item :label="$t('dialog.groupName')" prop="userName">
      <el-input
        v-model="groupData.userName"
        :maxlength="16"
        :disabled="disUserName"
      />
    </el-form-item>
    <el-form-item label-width="0" class="transfer-form-item">
      <el-transfer
        v-model="groupData.contactArray"
        class="dmr-grouping-transfer"
        :props="transferProps"
        :data="originContact"
        :titles="titles"
        :format="format"
      />
    </el-form-item>
  </el-form>
</template>

<script>
import { DefModel } from '@/writingFrequency/repeater'
import bfutil from '@/utils/bfutil'
import validateRules from '@/utils/validateRules'
import bfproto from '@/modules/protocol'
import { cloneDeep } from 'lodash'

const TableId = bfproto.lookupEnum('TableId')
const GroupData = {
  groupId: 0,
  userName: '',
  contactArray: [],
}

export default {
  name: 'TR925ContactGrouping',
  props: {
    repeaterData: {
      type: [Object, undefined],
    },
    repeater: {
      type: String,
      default: '',
    },
    getRepeaterId: {
      type: Function,
      default: bfutil.noop,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    packageName: {
      type: String,
      default: '',
    },
    deviceModel: {
      type: String,
      default: DefModel,
    },
    saveMethod: {
      type: Function,
      default: bfutil.noop,
    },
    tableId: {
      type: Number,
      default: 0,
    },
    dataId: {
      type: Number,
      default: -1,
    },
  },
  data() {
    return {
      groupData: {
        ...GroupData,
      },
    }
  },
  methods: {
    // 过滤穿梭框数据方法
    filterMethod(query, item) {
      return item.userName.includes(query)
    },
    setDefUserName() {
      this.groupData.userName = this.$t('dialog.frequentContacts')
    },
    saveContactGroupData() {
      this.contactGroup[this.groupData.groupId] = cloneDeep(this.groupData)
    },
  },
  computed: {
    groupingRules() {
      return {
        userName: [validateRules.required()],
      }
    },
    // 数据项为DMR通讯录数据项
    transferProps() {
      return {
        key: 'contactId',
        label: 'userName',
      }
    },
    format() {
      return {
        noChecked: '${total}',

        hasChecked: '${checked}/${total}',
      }
    },
    titles() {
      return [this.$t('dialog.contactList'), this.$t('dialog.memberList')]
    },
    disUserName() {
      return this.groupData.groupId === 0
    },
    wfSettings() {
      return this.repeaterData && this.repeaterData.writeFrequencySetting
    },
    contactGroup() {
      return (
        this.wfSettings &&
        this.wfSettings[TableId[TableId.RepeaterDmrContactGroup]]
      )
    },
    originContact() {
      return (
        (this.wfSettings &&
          this.wfSettings[TableId[TableId.RepeaterDmrContact]]) ||
        []
      )
    },
  },
  watch: {
    dataId: {
      immediate: true,
      handler(val) {
        if (TableId.RepeaterDmrContactGroup !== this.tableId || val < 0) {
          return
        }

        this.groupData.groupId = val
        if (this.contactGroup && this.contactGroup[val]) {
          this.groupData = cloneDeep(this.contactGroup[val])
        }
        // 0固定为常用联系人组，名称固定不变
        if (val === 0) {
          this.setDefUserName()
        }
      },
    },
    '$i18n.locale'(val) {
      if (this.groupData.groupId === 0) {
        this.setDefUserName()
      }
    },
    groupData: {
      deep: true,
      handler(newVal) {
        // 保存该联系人分组数据
        this.saveContactGroupData()
      },
    },
  },
}
</script>

<style>
.dmr-grouping-transfer {
  display: flex;
  justify-content: center;
  height: 100%;
}

.dmr-grouping-transfer .el-transfer-panel__body {
  flex: auto;
}

.dmr-grouping-transfer .el-transfer-panel {
  flex: auto;
  display: flex;
  flex-direction: column;
}

.dmr-grouping-transfer .el-transfer__buttons {
  flex: none;
  padding: 0 20px;
  align-self: center;
}

.grouping-setting .transfer-form-item {
  flex: auto;
}

.grouping-setting .el-form-item:last-child {
  margin-bottom: 0;
}

.dmr-grouping-transfer .el-checkbox-group.el-transfer-panel__list,
.grouping-setting {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.dmr-grouping-transfer
  .el-checkbox-group.el-transfer-panel__list
  .el-transfer-panel__item {
  flex: auto;
  flex-grow: 0;
  padding: 0 15px;
  margin-right: 0;
}

.grouping-setting .transfer-form-item .el-form-item__content {
  height: 100%;
}
</style>
