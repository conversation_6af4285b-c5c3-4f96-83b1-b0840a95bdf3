<template>
  <el-form
    ref="bdSetting"
    :model="bdSetting"
    label-position="top"
    class="position-setting"
    :rules="bdSettingRules"
  >
    <el-row :gutter="20" class="no-margin-x">
      <el-col :xs="24">
        <el-form-item>
          <el-checkbox v-model="bdEnable">
            <span v-text="$t('dialog.bdsSwitch')" />
          </el-checkbox>
        </el-form-item>
      </el-col>
      <el-col :xs="24">
        <el-form-item :label="$t('dialog.beidouContact')" prop="bdTargetId">
          <el-select
            v-model="bdSetting.bdTargetId"
            :placeholder="$t('dialog.select')"
            filterable
            :no-match-text="$t('dialog.noMatchText')"
            :disabled="!bdEnable"
          >
            <el-option
              v-for="(item, i) in bdTargetList"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xs="24">
        <el-form-item :label="$t('dialog.bdGateId')" prop="bdGateId">
          <el-input-number
            v-model="bdSetting.bdGateId"
            :min="16777000"
            :max="16777099"
            :step="1"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24">
        <el-form-item>
          <el-checkbox v-model="remoteCtrlEnable">
            <span v-text="$t('dialog.devRemoteCtrl')" />
          </el-checkbox>
        </el-form-item>
      </el-col>
      <el-col :xs="24">
        <el-form-item :label="$t('dialog.remoteCtrlPwd')" prop="remoteUiPwd">
          <el-input v-model="bdSetting.remoteUiPwd" :maxlength="8" />
        </el-form-item>
      </el-col>
      <el-col :xs="24">
        <el-form-item :label="$t('dialog.devDisAuthCode')" prop="remoteDisCode">
          <el-input v-model="bdSetting.remoteDisCode" :maxlength="16" />
        </el-form-item>
      </el-col>
      <el-col :xs="24">
        <el-form-item :label="$t('dialog.devEnAuthCode')" prop="remoteEnCode">
          <el-input v-model="bdSetting.remoteEnCode" :maxlength="16" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import { DefModel } from '@/writingFrequency/repeater'
import bfutil from '@/utils/bfutil'
import validateRules from '@/utils/validateRules'

export default {
  name: 'TR925BdSetting',
  props: {
    repeaterData: {
      type: [Object, undefined],
    },
    repeater: {
      type: String,
      default: '',
    },
    getRepeaterId: {
      type: Function,
      default: bfutil.noop,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    packageName: {
      type: String,
      default: '',
    },
    deviceModel: {
      type: String,
      default: DefModel,
    },
    saveMethod: {
      type: Function,
      default: bfutil.noop,
    },
  },
  data() {
    return {
      bdSetting: {
        bdEnable: 1,
        bdTargetId: '',
        bdGateId: '',
        remoteCtrlEnable: '',
        remoteUiPwd: '',
        remoteDisCode: '0123456789ABCDEF',
        remoteEnCode: 'FEDCBA9876543210',
      },
    }
  },
  methods: {},
  computed: {
    bdEnable: {
      get() {
        return this.bdSetting.bdEnable === 1
      },
      set(val) {
        if (val) {
          this.bdSetting.bdEnable = 1
        } else {
          this.bdSetting.bdEnable = 0
        }
      },
    },
    remoteCtrlEnable: {
      get() {
        return this.bdSetting.remoteCtrlEnable === 1
      },
      set(val) {
        if (val) {
          this.bdSetting.remoteCtrlEnable = 1
        } else {
          this.bdSetting.remoteCtrlEnable = 0
        }
      },
    },
    bdTargetList() {
      // 值范围：0-99
      return []
    },
    bdSettingRules() {
      const remoteCodeRule = (trigger = 'blur') => {
        // remoteDisCode,remoteEnCode 固定16个数字或大写字母型ASCII码
        const reg = /[0-9A-F]{16}/
        return {
          validator: (rule, value, callback) => {
            if (value === '' || reg.test(value)) {
              callback()
            } else {
              callback(
                new Error(this.$t('msgbox.fixed16NumbersOrUppercaseLetters')),
              )
            }
          },
          trigger: trigger,
        }
      }
      return {
        remoteUiPwd: [validateRules.mustNumber()],
        remoteDisCode: [remoteCodeRule()],
        remoteEnCode: [remoteCodeRule()],
      }
    },
  },
}
</script>

<style></style>
