<template>
  <el-form
    ref="commonSetting"
    :model="commonSetting"
    label-width="95px"
    label-position="top"
    :rules="commonSettingRules"
  >
    <el-row :gutter="20">
      <el-col :xs="24" :sm="12">
        <el-form-item :label="$t('dialog.repeaterName')" prop="devName">
          <el-input v-model="commonSetting.devName" :maxlength="8" />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item
          :label="$t('dialog.bootInterface')"
          prop="poweronUiEnable"
          class="form-item-switch"
        >
          <el-switch
            v-model="poweronUiEnable"
            :active-text="$t('dialog.on')"
            :inactive-text="$t('dialog.off')"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item :label="$t('dialog.powerOnPwd')" prop="poweronPwd">
          <el-input
            v-model="commonSetting.poweronPwd"
            :maxlength="6"
            :minlength="6"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item
          :label="$t('dialog.PowerOnPwdErrorThreshold')"
          prop="poweronPwdThreshold"
        >
          <el-input-number
            v-model="commonSetting.poweronPwdThreshold"
            :disabled="diPoweronPwdThreshold"
            :min="0"
            :max="10"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item :label="$t('dialog.languageType')" prop="languageType">
          <el-select
            v-model="commonSetting.languageType"
            :placeholder="$t('dialog.select')"
            :no-match-text="$t('dialog.noMatchText')"
          >
            <el-option
              v-for="(item, i) in languageTypeList"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item
          :label="$t('dialog.chHangTime', { unit: 'ms' })"
          prop="chHangTime"
        >
          <el-input-number
            v-model="commonSetting.chHangTime"
            :min="1000"
            :max="7000"
            :step="500"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item
          :label="$t('dialog.groupHangTime', { unit: 'ms' })"
          prop="groupHangTime"
        >
          <el-input-number
            v-model="commonSetting.groupHangTime"
            :min="0"
            :max="7000"
            :step="500"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item
          :label="$t('dialog.praviteHangTime', { unit: 'ms' })"
          prop="praviteHangTime"
        >
          <el-input-number
            v-model="commonSetting.praviteHangTime"
            :min="0"
            :max="7000"
            :step="500"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item
          :label="$t('dialog.emerHangTime', { unit: 'ms' })"
          prop="emerHangTime"
        >
          <el-input-number
            v-model="commonSetting.emerHangTime"
            :min="5"
            :max="7000"
            :step="500"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item
          :label="$t('dialog.analogHangTime', { unit: 's' })"
          prop="analogHangTime"
        >
          <el-input-number
            v-model="commonSetting.analogHangTime"
            :min="0"
            :max="7"
            :step="1"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item
          :label="$t('dialog.autoSignalDurtion', { unit: 'ms' })"
          prop="autoSignalDurtion"
        >
          <el-input-number
            v-model="commonSetting.autoSignalDurtion"
            :min="300"
            :max="600"
            :step="50"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item
          :label="$t('dialog.autoSignalInterval', { unit: 's' })"
          prop="autoSignalInterval"
        >
          <el-input-number
            v-model="commonSetting.autoSignalInterval"
            :min="10"
            :max="300"
            :step="10"
          />
        </el-form-item>
      </el-col>

      <el-col :xs="24" :sm="12">
        <el-form-item label="" prop="toneEnable">
          <el-checkbox v-model="toneEnable">
            <span v-text="$t('dialog.soundTip')" />
          </el-checkbox>
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item label="" prop="ledEnable">
          <el-checkbox v-model="ledEnable">
            <span v-text="$t('dialog.soundTip')" />
          </el-checkbox>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :xs="24" :sm="12">
        <el-form-item
          :label="$t('dialog.selectZoneCh')"
          prop="selectZoneCh"
          class="form-item-switch"
        >
          <el-switch
            v-model="selectZoneCh"
            :active-text="$t('dialog.on')"
            :inactive-text="$t('dialog.off')"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item :label="$t('dialog.defaultMainArea')">
          <el-select
            v-model="commonSetting.defaultZoneid.mainZoneId"
            :placeholder="$t('dialog.select')"
            :disabled="disDefaultZoneid"
            :no-match-text="$t('dialog.noMatchText')"
          >
            <el-option
              v-for="(item, i) in mainZoneIdData"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item :label="$t('dialog.defaultFromArea')">
          <el-select
            v-model="commonSetting.defaultZoneid.subZoneId"
            :placeholder="$t('dialog.select')"
            :disabled="disDefaultZoneid"
            :no-match-text="$t('dialog.noMatchText')"
          >
            <el-option
              v-for="(item, i) in subZoneIdData"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item :label="$t('dialog.defaultUserArea')">
          <el-select
            v-model="commonSetting.defaultZoneid.userZoneId"
            :placeholder="$t('dialog.select')"
            :disabled="disDefaultZoneid"
            :no-match-text="$t('dialog.noMatchText')"
          >
            <el-option
              v-for="(item, i) in userZoneIdData"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item :label="$t('dialog.defaultChannel')">
          <el-select
            v-model="commonSetting.defaultChannel"
            :placeholder="$t('dialog.select')"
            :disabled="disDefaultZoneid"
            :no-match-text="$t('dialog.noMatchText')"
          >
            <el-option
              v-for="(item, i) in channelData"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item label-width="0" class="center actions">
          <el-button
            type="primary"
            :disabled="disabled"
            @click="queryCommonSetting"
            v-text="$t('dialog.querySetting')"
          />
          <el-button
            type="warning"
            :disabled="disabled"
            @click="writeInCommonSetting"
            v-text="$t('dialog.writeIn')"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import repeaterWfMod, { DefModel } from '@/writingFrequency/repeater'
import bfutil from '@/utils/bfutil'
import { tr925PackageName } from '@/modules/protocol'
import { merge } from 'lodash'

const CommonSetting = {
  devName: '',
  poweronUiEnable: 1,
  poweronPwd: '',
  poweronPwdThreshold: 3,
  languageType: 0,
  chHangTime: 3000,
  groupHangTime: 3000,
  praviteHangTime: 3000,
  emerHangTime: 3000,
  analogHangTime: 3,
  autoSignalDurtion: 300,
  autoSignalInterval: 30,
  toneEnable: 1,
  ledEnable: 1,

  // 默认区域信道设置
  selectZoneCh: 1,
  defaultZoneid: {
    mainZoneId: 0,
    subZoneId: 0,
    userZoneId: 0,
  },
  defaultChannel: 0,
}

export default {
  name: 'TR925CommonSetting',
  props: {
    repeaterData: {
      type: [Object, undefined],
    },
    repeater: {
      type: String,
      default: '',
    },
    getRepeaterId: {
      type: Function,
      default: bfutil.noop,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    packageName: {
      type: String,
      default: '',
    },
    deviceModel: {
      type: String,
      default: DefModel,
    },
    saveMethod: {
      type: Function,
      default: bfutil.noop,
    },
  },
  data() {
    return {
      commonSetting: {
        ...CommonSetting,
      },
      // 一级区域数据
      mainZoneIdData: [],
      // 二级区域数据
      subZoneIdData: [],
      // 三级区域数据
      userZoneIdData: [],
      // 信道数据
      channelData: [],
    }
  },
  methods: {
    coverEnableValue(val, key) {
      const value = val ? 1 : 0
      if (this.commonSetting[key] !== value) {
        this.commonSetting[key] = value
      }

      return value
    },

    saveConfig(data) {
      this.commonSetting = merge(this.commonSetting, data)
      this.saveMethod('commonSetting', data)
    },
    // 常规设置
    queryCommonSetting() {
      repeaterWfMod
        .queryConfig(this.defQueryOption)
        .then(res => {
          return this.saveConfig(res)
        })
        .catch(err => {
          bfglob.console.error('queryCommonSetting', err)
        })
    },
    writeInCommonSetting() {
      this.$refs.commonSetting.validate(valid => {
        if (!valid) {
          return false
        }

        const options = merge(this.defQueryOption, {
          paraBin: {
            operation: 6,
          },
        })

        repeaterWfMod
          .writeInData(this.commonSetting, options)
          .then(res => {
            return this.saveConfig(this.commonSetting)
          })
          .catch(err => {
            bfglob.console.error('writeInCommonSetting', err)
          })
      })
    },
  },
  computed: {
    defQueryOption() {
      return {
        sid: this.repeater,
        paraInt: this.getRepeaterId(),
        paraBin: {
          operation: 5,
          tableId: 2,
        },
        decodeMsgType: 'RepeaterCommonSetting',
        packageName: tr925PackageName,
      }
    },
    commonSettingRules() {
      return {
        devName: [
          {
            required: true,
            message: this.$t('dialog.requiredRule'),
            trigger: 'blur',
          },
          // {
          //   validator: (rule, value, callback) => {
          //     // 检测输入的字符串是否有中文
          //     bfutil.cannotIncludeChineseRule(rule, value, callback)
          //   },
          //   trigger: ['blur']
          // }
        ],
      }
    },
    // 常规设置计算属性
    poweronUiEnable: {
      get() {
        return this.commonSetting.poweronUiEnable === 1
      },
      set(val) {
        this.coverEnableValue(val, 'poweronUiEnable')
      },
    },
    toneEnable: {
      get() {
        return this.commonSetting.toneEnable === 1
      },
      set(val) {
        this.coverEnableValue(val, 'toneEnable')
      },
    },
    ledEnable: {
      get() {
        return this.commonSetting.ledEnable === 1
      },
      set(val) {
        this.coverEnableValue(val, 'ledEnable')
      },
    },
    selectZoneCh: {
      get() {
        return this.commonSetting.selectZoneCh === 1
      },
      set(val) {
        this.coverEnableValue(val, 'selectZoneCh')
      },
    },
    diPoweronPwdThreshold() {
      return this.commonSetting.poweronPwd.length !== 6
    },
    languageTypeList() {
      return [
        {
          label: this.$t('header.CN'),
          value: 0,
        },
        {
          label: this.$t('header.EN'),
          value: 1,
        },
      ]
    },
    disDefaultZoneid() {
      return !this.selectZoneCh
    },
  },
}
</script>

<style></style>
