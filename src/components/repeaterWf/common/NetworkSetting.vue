<template>
  <el-form
    ref="networkSetting"
    label-width="auto"
    :model="networkSetting"
    :rules="networkSettingRules"
  >
    <el-form-item
      v-if="showIpEnable"
      :label="$t('dialog.networkSwitch')"
      prop="ipEnable"
    >
      <el-select v-model="networkSetting.ipEnable" :maxlength="16">
        <el-option
          v-for="(item, i) in ipEnableList"
          :key="i"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item :label="$t('dialog.ipSettingMode')" prop="ipMode">
      <el-select
        v-model="networkSetting.ipMode"
        :maxlength="16"
        :disabled="notEnableNetwork"
      >
        <el-option
          v-for="(item, i) in ipSettingModeList"
          :key="i"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item :label="$t('dialog.networkIp')" prop="ipAddr">
      <IpInput v-model="networkSetting.ipAddr" :disabled="ipModeAuto" />
    </el-form-item>
    <el-form-item :label="$t('dialog.networkMask')" prop="ipMask">
      <IpInput v-model="networkSetting.ipMask" :disabled="ipModeAuto" />
    </el-form-item>
    <el-form-item :label="$t('dialog.gatewayIp')" prop="ipGateway">
      <IpInput v-model="networkSetting.ipGateway" :disabled="ipModeAuto" />
    </el-form-item>
    <el-form-item :label="$t('dialog.dnsServerIp')" prop="ipDns">
      <IpInput v-model="networkSetting.ipDns" :disabled="ipModeAuto" />
    </el-form-item>
    <el-form-item label=" " class="center actions">
      <el-button
        type="primary"
        :disabled="disabled"
        @click="queryNetworkSetting"
        v-text="$t('dialog.querySetting')"
      />
      <el-button
        type="warning"
        :disabled="disabled"
        @click="updateNetworkSetting"
        v-text="$t('dialog.writeIn')"
      />
    </el-form-item>
  </el-form>
</template>

<script>
import repeaterWfMod from '@/writingFrequency/repeater'
import bfutil, { decodeInt32Ip } from '@/utils/bfutil'
import { kcpPackageName } from '@/modules/protocol'
import { cloneDeep, merge } from 'lodash'
import { defineAsyncComponent } from 'vue'

const NetworkSetting = {
  ipEnable: 1,
  ipMode: 0,
  ipAddr: 0,
  ipMask: 0,
  ipGateway: 0,
  ipDns: 0,
}

export default {
  name: 'NetworkSetting',
  props: {
    repeaterData: {
      type: [Object, undefined],
    },
    repeater: {
      type: String,
      default: '',
    },
    getRepeaterId: {
      type: Function,
      default: bfutil.noop,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    packageName: {
      type: String,
      default: '',
    },
    deviceModel: {
      type: String,
      default: 'TR805005',
    },
    saveMethod: {
      type: Function,
      default: bfutil.noop,
    },
  },
  data() {
    return {
      networkSetting: {
        ...NetworkSetting,
      },
    }
  },
  methods: {
    clearValidate() {
      this.$refs.networkSetting.clearValidate()
    },
    getNetworkSettings() {
      if (!this.repeaterData) {
        return undefined
      }
      const writeFrequencySetting = this.repeaterData.writeFrequencySetting
      if (!writeFrequencySetting) {
        return undefined
      }
      const networkSetting = writeFrequencySetting.networkSetting
      if (!networkSetting) {
        return undefined
      }

      return cloneDeep(networkSetting)
    },

    // 保存配置数据
    saveConfig(data) {
      this.networkSetting = merge(this.networkSetting, data)

      this.saveMethod('networkSetting', data)
    },

    // network setting
    queryNetworkSetting() {
      const options = merge(this.defQueryOption, {
        paraBin: {
          tableId: 4,
        },
      })

      repeaterWfMod
        .queryConfig(options)
        .then(res => {
          return this.saveConfig(res)
        })
        .catch(err => {
          bfglob.console.error('queryNetworkSetting', err)
        })
    },
    updateNetworkSetting() {
      this.$refs.networkSetting.validate(valid => {
        if (!valid) {
          return false
        }

        const options = merge(this.defQueryOption, {
          paraBin: {
            operation: 6,
            tableId: 4,
          },
        })

        repeaterWfMod
          .writeInData(this.networkSetting, options)
          .then(res => {
            return this.saveConfig(this.networkSetting)
          })
          .catch(err => {
            bfglob.console.error('updateNetworkSetting', err)
          })
      })
    },
  },
  computed: {
    defQueryOption() {
      return {
        paraBin: {
          operation: 5,
        },
        sid: this.repeater,
        paraInt: this.getRepeaterId(),
        decodeMsgType: 'RepeaterIpSetting',
        packageName: this.packageName || kcpPackageName,
      }
    },
    networkSettingRules() {
      const checkIpMode = (rule, value, callback) => {
        if (this.networkSetting.ipMode === 1) {
          // 自动模式
          callback()
        } else {
          const ipv4 = (value && decodeInt32Ip(value)) || value
          const ipReg =
            /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
          if (ipReg.test(ipv4)) {
            callback()
          } else {
            callback(new Error(this.$t('msgbox.mustIp')))
          }
        }
      }
      return {
        ipAddr: [
          {
            validator: (rule, value, callback) => {
              checkIpMode(rule, value, callback)
            },
            trigger: ['blur', 'change'],
          },
        ],
        ipMask: [
          {
            validator: (rule, value, callback) => {
              checkIpMode(rule, value, callback)
            },
            trigger: ['blur', 'change'],
          },
        ],
        ipGateway: [
          {
            validator: (rule, value, callback) => {
              checkIpMode(rule, value, callback)
            },
            trigger: ['blur', 'change'],
          },
        ],
        ipDns: [
          {
            validator: (rule, value, callback) => {
              checkIpMode(rule, value, callback)
            },
            trigger: ['blur', 'change'],
          },
        ],
      }
    },
    ipModeAuto() {
      return (
        (this.showIpEnable && this.notEnableNetwork) ||
        this.networkSetting.ipMode === 1
      )
    },
    ipSettingModeList() {
      return [
        {
          label: this.$t('dialog.handmode'),
          value: 0,
        },
        {
          label: this.$t('dialog.automode'),
          value: 1,
        },
      ]
    },
    ipEnableList() {
      return [
        {
          label: this.$t('dialog.off'),
          value: 0,
        },
        {
          label: this.$t('dialog.on'),
          value: 1,
        },
      ]
    },
    showIpEnable() {
      const deviceModels = ['TR092500', 'TR092501', 'TR09250M']

      return deviceModels.includes(this.deviceModel)
    },
    notEnableNetwork() {
      return this.showIpEnable && this.networkSetting.ipEnable === 0
    },
  },
  watch: {
    ipModeAuto(val) {
      if (val) {
        this.clearValidate()
        this.networkSetting = merge(this.networkSetting, {
          ipAddr: 0,
          ipMask: 0,
          ipGateway: 0,
          ipDns: 0,
        })
      } else {
        const networkSetting = this.getNetworkSettings()
        if (networkSetting && networkSetting.ipMode === 0) {
          this.networkSetting = merge(this.networkSetting, networkSetting)
        }
      }
    },
    'repeaterData.writeFrequencySetting.networkSetting': {
      immediate: true,
      deep: true,
      handler(val) {
        this.networkSetting = merge({}, val || NetworkSetting)
        this.$nextTick(() => {
          this.clearValidate()
        })
      },
    },
  },
  components: {
    IpInput: defineAsyncComponent(() => import('@/components/common/IpInput')),
  },
}
</script>

<style></style>
