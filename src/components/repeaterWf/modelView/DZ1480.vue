<template>
  <div class="dz148-wrapper">
    <el-tabs
      v-model="settingRadio"
      tab-position="left"
      class="repeaterCmd"
      :class="{ 'is-mobile': isMobile }"
    >
      <el-tab-pane
        lazy
        :label="$t('dialog.simulcastRepeater')"
        name="8"
        class="simulcast-repeater-pane"
      >
        <el-table
          :data="simulcastRepeaterList"
          border
          size="small"
          style="width: 100%"
        >
          <el-table-column type="index" width="50" label="#" />
          <el-table-column :label="$t('dialog.repeaterName')" min-width="140">
            <template #default="scope">
              {{ getControllerName(scope.row.deviceId) }}
            </template>
          </el-table-column>
          <el-table-column label="DMRID" min-width="140">
            <template #default="scope">
              {{ toHexDmrId(scope.row.deviceId) }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('dialog.txFrequency')" min-width="120">
            <template #default="scope">
              {{ scope.row.txFreq ? frequencyHz2Mhz(scope.row.txFreq) : '' }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('dialog.rxFrequency')" min-width="120">
            <template #default="scope">
              {{ scope.row.rxFreq ? frequencyHz2Mhz(scope.row.rxFreq) : '' }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('dataTable.IPAddress')" min-width="140">
            <template #default="scope">
              {{ decodeInt32Ip(scope.row.ipAddr) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="slotId"
            :label="$t('writeFreq.currentSlot')"
            min-width="100"
          />
          <el-table-column
            :label="$t('writeFreq.dualTimeSlot')"
            min-width="100"
          >
            <template #default="scope">
              <!--是否双时隙IP互联-->
              <span
                v-if="scope.row.dualslotIpmc === 1"
                v-text="$t('dialog.yes')"
              />
              <span v-else v-text="$t('dialog.no')" />
            </template>
          </el-table-column>
          <el-table-column :label="$t('dataTable.registered')" min-width="100">
            <template #default="scope">
              <span v-if="checkIsReg(scope.row)"
                ><!--注册-->{{ $t('dialog.yes') }}</span
              >
              <span v-else><!--注销-->{{ $t('dialog.no') }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('dataTable.removeSimulcastSystem')"
            min-width="140"
          >
            <template #default="scope">
              <span v-if="scope.row.cfcbDisable === 1"
                ><!--移出同播系统-->
                {{ $t('dialog.yes') }}</span
              >
              <span v-else><!--加入同播系统-->{{ $t('dialog.no') }}</span>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="#"
            width="110"
            class-name="actions"
          >
            <template #default="scope">
              <el-button
                type="primary"
                icon="more"
                circle
                :disabled="!checkIsReg(scope.row)"
                @click="simulcastRepeaterAction(scope.row)"
              />
              <el-button
                type="primary"
                icon="refresh"
                circle
                :disabled="scope.row.ctrlStats !== 1"
                @click="querySimulcastRepeaterInfo(scope.row)"
              />
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <!-- 同播中继写频对话框 -->
    <el-dialog
      v-model="visible"
      :title="$t('dialog.simulcastRepeater')"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :fullscreen="fullscreen"
      append-to-body
      class="dz148-dialog simulcast-repeater-dialog"
      :class="$i18n.locale"
      @close="closeDlgFn"
      @open="openDlgFn"
    >
      <el-tabs
        v-model="repeaterRadio"
        tab-position="left"
        class="simulcast-repeater-tabs repeaterCmd"
        :class="{ 'is-mobile': isMobile }"
      >
        <el-tab-pane
          lazy
          :label="$t('writeFreq.channelConfig')"
          :name="clientCmd.CH_CONFIG + ''"
          class="repeaterCurChInfo"
        >
          <el-form
            ref="channelSetting"
            :model="channelSetting"
            label-width="auto"
            label-position="top"
            class="grid grid-cols-2 gap-3"
          >
            <el-form-item :label="$t('dialog.chType')" prop="chType">
              <el-select
                v-model="channelSetting.chType"
                popper-class="dz1480-select-dropdown"
                :placeholder="$t('dialog.select')"
                filterable
                :no-match-text="$t('dialog.noMatchText')"
              >
                <el-option
                  v-for="item in channelTypes"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('dialog.chName')" prop="chName">
              <el-input v-model="channelSetting.chName" :maxlength="16" />
            </el-form-item>
            <el-form-item :label="$t('dialog.txPower')" prop="txPower">
              <el-select
                v-model="channelSetting.txPower"
                popper-class="dz1480-select-dropdown"
                :placeholder="$t('dialog.select')"
                filterable
                :no-match-text="$t('dialog.noMatchText')"
              >
                <el-option
                  v-for="item in powerList"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('dialog.customPower')" prop="customPower">
              <el-input-number
                v-model="channelSetting.customPower"
                :min="1"
                :max="50"
                :disabled="channelSetting.txPower !== 3"
              />
            </el-form-item>
            <el-form-item :label="$t('dialog.rxFrequency')" prop="rxFreq">
              <FrequencyMhz v-model="channelSetting.rxFreq" :maxlength="9" />
            </el-form-item>
            <el-form-item :label="$t('dialog.txFrequency')" prop="txFreq">
              <FrequencyMhz v-model="channelSetting.txFreq" :maxlength="9" />
            </el-form-item>
            <el-form-item :label="$t('dialog.colorCodes')" prop="cc">
              <el-input-number
                v-model="channelSetting.cc"
                :min="0"
                :max="15"
                :step="1"
              />
            </el-form-item>
            <el-form-item label="" class="col-span-2 actions">
              <el-button
                type="primary"
                :disabled="notRepeater"
                @click="queryChannelConfig"
                v-text="$t('dialog.query')"
              />
              <el-button
                type="primary"
                :disabled="notRepeater"
                @click="setChannelConfig"
                v-text="$t('dialog.writeIn')"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane
          lazy
          :label="$t('dialog.switchChannel')"
          :name="clientCmd.CH_SWITCH + ''"
          class="flex justify-center repeaterCurChSet"
        >
          <el-form
            ref="switchChannel"
            :model="channel"
            label-width="auto"
            class="flex flex-col gap-3"
          >
            <el-form-item :label="$t('writeFreq.areaId')">
              <el-input-number v-model="channel.zoneId" :min="1" :max="127" />
            </el-form-item>
            <el-form-item :label="$t('dialog.chId')">
              <el-input-number v-model="channel.chId" :min="1" :max="127" />
            </el-form-item>
            <el-form-item label=" " class="actions">
              <el-button
                type="primary"
                class="!w-full"
                :disabled="notRepeater"
                @click="switchChannel"
                v-text="$t('dialog.writeIn')"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane
          lazy
          :label="$t('dialog.switchTransmitPower')"
          :name="clientCmd.TX_POWER_SWITCH + ''"
          class="flex justify-center repeaterCurPowerSet"
        >
          <el-form
            ref="powerSwitch"
            :model="power"
            label-width="auto"
            class="flex flex-col gap-3"
          >
            <el-form-item :label="$t('dialog.txPower')">
              <el-select
                v-model="power.txPower"
                popper-class="dz1480-select-dropdown"
                :placeholder="$t('dialog.select')"
                filterable
                :no-match-text="$t('dialog.noMatchText')"
              >
                <el-option
                  v-for="item in powerList"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('dialog.customPower')">
              <el-input-number
                v-model="power.customPower"
                :min="1"
                :max="50"
                :disabled="power.txPower !== 3"
              />
            </el-form-item>

            <el-form-item label=" " class="actions">
              <el-button
                type="primary"
                class="!w-full"
                :disabled="notRepeater"
                @click="switchPower"
                v-text="$t('dialog.writeIn')"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane
          lazy
          :label="$t('dialog.simulcastSystem')"
          :name="clientCmd.CFCB_SYS_ADD + ''"
          class="flex justify-center simulcastSystem"
        >
          <div
            ref="simulcastSystem"
            class="flex flex-col justify-center w-46 gap-3 actions"
          >
            <el-button
              type="primary"
              :disabled="notRepeater || !cfcbDisable"
              @click="registrySimulcastRepeater"
              v-text="$t('dialog.join')"
            />
            <el-button
              type="warning"
              class="!ml-0"
              :disabled="notRepeater || cfcbDisable"
              @click="removeSimulcastRepeater"
              v-text="$t('dialog.exit')"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane
          lazy
          :label="$t('dialog.restartRepeater')"
          :name="clientCmd.DEVICE_REBOOT + ''"
          class="flex justify-center restartRepeater"
        >
          <div
            ref="restartRepeater"
            class="flex flex-col justify-center w-46 gap-3 actions"
          >
            <el-button
              type="primary"
              :disabled="notRepeater"
              @click="restartSimulcastRepeater"
              v-text="$t('dialog.restartRepeater')"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script>
import bfproto, { kcpPackageName } from '@/modules/protocol'
import { messageBox } from '@/utils/notify'
import {
  decodeInt32Ip,
  frequencyHz2Mhz,
  frequencyMhz2Hz,
  objToArray,
  toHexDmrId,
} from '@/utils/bfutil'
import { defineAsyncComponent } from 'vue'

import {
  CLIENT_CMD,
  queryChannelConfig,
  querySimulcastRepeaterInfo,
  registrySimulcastRepeater,
  removeSimulcastRepeater,
  restartSimulcastRepeater,
  saveSimulcastInfo,
  setChannelConfig,
  switchChannel,
  switchPower,
} from '@/writingFrequency/repeater/dz1480'
import RepeaterWfMixin from '@/utils/repeaterWfMixin'

export default {
  name: 'DZ1480',
  mixins: [RepeaterWfMixin],
  props: {
    repeater: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      channelSettingModel: 'setting',
      settingRadio: '8',
      visible: false,
      repeaterRadio: CLIENT_CMD.CH_CONFIG + '',
      simulcastRepeaterList: [],
      currentRow: null,

      power: {
        txPower: 0,
        customPower: 1,
      },
      channel: {
        zoneId: 1,
        chId: 2,
      },
      channelSetting: {
        chType: 0,
        chName: 'CH 1',
        txPower: 0,
        customPower: 1,
        rxFreq: 0,
        txFreq: 0,
        cc: 0,
      },
    }
  },
  methods: {
    decodeInt32Ip,
    toHexDmrId(deviceId) {
      return toHexDmrId(deviceId, false)
    },
    getControllerName(intDmrId) {
      const dmrId = this.toHexDmrId(intDmrId)
      const controller = bfglob.gcontrollers.getDataByIndex(dmrId)
      return controller?.selfId ?? ''
    },
    queryChannelConfig() {
      if (!this.currentRow) {
        return
      }
      queryChannelConfig(this.defaultOptions)
        .then(res => {
          messageBox(this.$t('msgbox.selSuccess'), 'success')
          this.channelSetting = {
            ...this.channelSetting,
            ...res,
          }
        })
        .catch(err => {
          bfglob.console.error('queryChannelConfig err:', err)
        })
    },
    setChannelConfig() {
      if (!this.currentRow) {
        return
      }
      setChannelConfig(this.channelSetting, this.defaultOptions)
        .then(() => {
          messageBox(this.$t('msgbox.configSuccess'), 'success')
        })
        .catch(err => {
          bfglob.console.error('setChannelConfig err:', err)
        })
    },
    switchChannel() {
      if (!this.currentRow) {
        return
      }
      switchChannel(this.channel, this.defaultOptions)
        .then(() => {
          messageBox(this.$t('msgbox.configSuccess'), 'success')
        })
        .catch(err => {
          bfglob.console.error('switchChannel err:', err)
        })
    },
    switchPower() {
      if (!this.currentRow) {
        return
      }
      switchPower(this.power, this.defaultOptions)
        .then(() => {
          messageBox(this.$t('msgbox.configSuccess'), 'success')
        })
        .catch(err => {
          bfglob.console.error('switchPower err:', err)
        })
    },
    restartSimulcastRepeater() {
      if (!this.currentRow) {
        return
      }
      restartSimulcastRepeater(this.defaultOptions)
        .then(() => {
          messageBox(this.$t('msgbox.sendSuccess'), 'success')
        })
        .catch(err => {
          bfglob.console.error('restartSimulcastRepeater err:', err)
        })
    },
    setSimulcastRepeaterCfcbState(deviceId, status) {
      const index = this.simulcastRepeaterList.findIndex(
        item => item.deviceId === deviceId,
      )
      if (index === -1) {
        return
      }
      const info = { ...this.simulcastRepeaterList[index] }
      info.cfcbDisable = status
      this.simulcastRepeaterList[index] = info

      if (this.currentRow?.deviceId !== deviceId) {
        return
      }
      this.currentRow = {
        ...this.currentRow,
        cfcbDisable: status,
      }
    },
    registrySimulcastRepeater() {
      if (!this.currentRow) {
        return
      }
      registrySimulcastRepeater(this.defaultOptions)
        .then(res => {
          messageBox(this.$t('msgbox.sendSuccess'), 'success')
          this.setSimulcastRepeaterCfcbState(res.deviceId, 0)
        })
        .catch(err => {
          bfglob.console.error('registrySimulcastRepeater err:', err)
        })
    },
    removeSimulcastRepeater() {
      if (!this.currentRow) {
        return
      }
      removeSimulcastRepeater(this.defaultOptions)
        .then(res => {
          messageBox(this.$t('msgbox.configSuccess'), 'success')
          this.setSimulcastRepeaterCfcbState(res.deviceId, 1)
        })
        .catch(err => {
          bfglob.console.error('removeSimulcastRepeater err:', err)
        })
    },
    querySimulcastRepeaterInfo(row) {
      this.currentRow = row
      querySimulcastRepeaterInfo(this.defaultOptions)
        .then(data => {
          saveSimulcastInfo(data)
        })
        .catch(console.error)
    },
    simulcastRepeaterAction(row) {
      this.currentRow = row
      this.visible = true
    },
    openSimulcastOperation(controller) {
      if (!this.checkIsReg(controller.simulcastInfo)) {
        return
      }
      this.visible = true
      this.currentRow = { ...controller.simulcastInfo }
    },
    openDlgFn() {
      // 发布关闭最小化导航区对应按钮事件
      // bfglob.emit('closeMiniNavBtn', 'open_vrepeaterWriteFrequency')
    },
    closeDlgFn() {
      // bfutil.closeDialogCallback(this)
    },
    frequencyMhz2Hz,
    frequencyHz2Mhz,
    // 新的同播中继上线，需要添加到操作列表中
    syncOneSimulcastInfo(simulcastInfo) {
      const index = this.simulcastRepeaterList.findIndex(
        item => item.deviceId === simulcastInfo.deviceId,
      )
      if (index === -1) {
        this.simulcastRepeaterList.push(simulcastInfo)
      } else {
        this.simulcastRepeaterList[index] = simulcastInfo
      }
    },
    listenControllerOnlineState(controller) {
      if (!controller) {
        return
      }

      const intDmrId = parseInt(controller.dmrId, 16)
      const index = this.simulcastRepeaterList.findIndex(
        item => item.deviceId === intDmrId,
      )
      if (index === -1) {
        return
      }

      // 给同播中继状态信息对象添加在线标记
      this.simulcastRepeaterList[index]['ctrlStats'] = controller.ctrlStats
    },
    checkIsReg(row) {
      return row.isReg === 1 || row.isReg === 2
    },
  },
  computed: {
    clientCmd() {
      return CLIENT_CMD
    },
    defaultOptions() {
      return {
        rpcCmdFields: {
          sid: this.repeater,
        },
        deviceId: this.currentRow.deviceId,
      }
    },
    powerList() {
      /**功率等级 （低:0、中:1、高:2、自定义:3）**/
      return [
        {
          label: this.$t('dialog.low'),
          value: 0,
        },
        {
          label: this.$t('dialog.mid'),
          value: 1,
        },
        {
          label: this.$t('dialog.high'),
          value: 2,
        },
        {
          label: this.$t('dialog.customize'),
          value: 3,
        },
      ]
    },
    channelTypes() {
      // 数字信道0 模拟信道1 数模兼容信道2
      return [
        {
          label: this.$t('dialog.digitalChannel'),
          value: 0,
        },
        {
          label: this.$t('dialog.analogChannel'),
          value: 1,
        },
        {
          label: this.$t('dialog.digitalAnalogChannel'),
          value: 2,
        },
      ]
    },
    // 没有选中继设备，或中继不在线，则禁用按钮功能
    notRepeater() {
      return (
        !this.repeater || !this.currentRow || !this.checkIsReg(this.currentRow)
      )
    },
    cfcbDisable() {
      /**1:移出同频同播系统, 0:正常使用同频同播系统**/
      return this.currentRow && this.currentRow.cfcbDisable === 1
    },
  },
  watch: {
    // 选中的同播控制器DMRID
    repeater(val) {
      const controller = bfglob.gcontrollers.getDataByIndex(val)
      if (!controller) {
        return
      }

      const controllers = objToArray(bfglob.gcontrollers.getAll())
      const repeaters = controllers.filter(
        item => item.simulcastParent === controller.rid,
      )
      if (!repeaters.length) {
        return
      }

      this.simulcastRepeaterList = repeaters.map(r => {
        if (r.simulcastInfo) {
          r.simulcastInfo.ctrlStats = r.ctrlStats
          return r.simulcastInfo
        }
        const t = bfproto.bfdx_proto_msg_T(
          'ClientRepeatorInfoReport',
          kcpPackageName,
        )
        const info = t.create()
        info.deviceId = parseInt(r.dmrId, 16)
        info.ctrlStats = r.ctrlStats
        return info
      })
    },
    // 切换同播中继时，将频率同步到信道配置中
    currentRow: {
      deep: true,
      handler(val) {
        if (!val) {
          return
        }
        const props = ['rxFreq', 'txFreq']
        props.forEach(prop => {
          this.channelSetting[prop] = val[prop]
        })
      },
    },
  },
  components: {
    FrequencyMhz: defineAsyncComponent(
      () => import('@/components/common/FrequencyMhz'),
    ),
    // generateDmrId: defineAsyncComponent(() => import('@/components/common/generateDmrId')),
    // RepeaterInfo: defineAsyncComponent(() => import('@/components/repeaterWf/common/RepeaterInfo')),
    // SwitchTransmitPower: defineAsyncComponent(() => import('@/components/repeaterWf/common/SwitchTransmitPower')),
    // SwitchChannel: defineAsyncComponent(() => import('@/components/repeaterWf/common/SwitchChannel')),
    // NetworkSetting: defineAsyncComponent(() => import('@/components/repeaterWf/common/NetworkSetting')),
    // RepeaterKey: defineAsyncComponent(() => import('@/components/repeaterWf/common/RepeaterKey')),
    // ServerSetting: defineAsyncComponent(() => import('@/components/repeaterWf/common/ServerSetting')),
    // TR805005Channel: defineAsyncComponent(() => import('@/components/repeaterWf/common/TR805005Channel')),
  },
  mounted() {
    bfglob.on('openSimulcastOperation', this.openSimulcastOperation)
    bfglob.on('simulcastInfo', this.syncOneSimulcastInfo)
    bfglob.on('update_controller_stats', this.listenControllerOnlineState)
  },
  beforeUnmount() {
    bfglob.off('openSimulcastOperation', this.openSimulcastOperation)
    bfglob.off('simulcastInfo', this.syncOneSimulcastInfo)
    bfglob.off('update_controller_stats', this.listenControllerOnlineState)
  },
}
</script>

<style lang="scss">
@import url('@/css/repeaterWf/dz148.scss');

.el-overlay .dz148-dialog.simulcast-repeater-dialog {
  & > .el-dialog__body {
    height: 40vh;

    .el-input-number {
      width: 100%;
      min-width: 200px;
    }
  }
}
</style>
