<template>
  <section class="tr925-wrap">
    <div class="tr925-body">
      <div :id="menuTreeId" :ref="menuTreeId" class="tr925-menu-tree" />
      <main class="tr925-main">
        <component
          :is="item"
          v-for="(item, index) in dynamicComponents"
          v-show="item.name === tableName"
          :key="index"
          :ref="item.name"
          :repeater="repeater"
          :repeaterData="repeaterData"
          :getRepeaterId="getRepeaterId"
          :saveMethod="saveMethod"
          :tableId="tableId"
          :dataId="dataId"
          deviceModel="TR092501"
          :disabled="notRepeater"
        />
      </main>
    </div>
    <!--<div class='tr925-operator-group'>-->
    <!--<el-button type='primary'-->
    <!--:size='optGroupSize'-->
    <!--v-text='$t("dialog.readData")'>-->
    <!--</el-button>-->
    <!--<el-button type='warning'-->
    <!--:size='optGroupSize'-->
    <!--v-text='$t("dialog.writeIn")'>-->
    <!--</el-button>-->
    <!--</div>-->
  </section>
</template>

<script>
import 'jquery.fancytree/dist/modules/jquery.fancytree.persist'
import bfutil from '@/utils/bfutil'
import bfproto from '@/modules/protocol'
import { merge } from 'lodash'
import bftree from '@/utils/bftree'
import i18n from '@/modules/i18n'
import { defineAsyncComponent } from 'vue'

const TableId = bfproto.lookupEnum('TableId')
const TableNames = {
  1: 'RepeaterInfo',
  2: 'TR925CommonSetting',
  3: 'RepeaterKey',
  4: 'TR925MenuSetting',
  5: 'TR925PreMadeSms',
  6: 'TR925PositionSetting',
  7: 'TR925BdSetting',
  8: 'TR925BdContact',
  9: 'NetworkSetting',
  10: 'ServerSetting',
  13: 'TR925DmrSetting',
  14: 'TR925DmrContact',
  15: 'TR925ContactGrouping',
  16: 'TR925ZoneIdSetting',
  17: 'TR925ZoneIdSetting',
  18: 'TR925ZoneIdSetting',
  19: 'TR925Channel',
}

export default {
  name: 'TR092501',
  props: {
    repeaterData: {
      type: [Object, undefined],
    },
    repeater: {
      type: String,
      default: '',
    },
    getRepeaterId: {
      type: Function,
      default: bfutil.noop,
    },
    saveMethod: {
      type: Function,
      default: bfutil.noop,
    },
  },
  data() {
    return {
      menuTreeId: 'tr925MenuTree',
      // 当前显示的组件对应id
      tableId: 1,
      // 传递的数据ID
      dataId: -1,
      // 所有写频配置组件
      dynamicComponents: {},
      // ?<tableId> 正则匹配命名规则,将匹配的结果放到对应的命名空间中
      nodeKeyReg: /(?<tableId>^[0-9]+)-(?<dataId>[0-9]+$)/,
    }
  },
  methods: {
    getIdsFromNodeKey(key) {
      // 多级数据ID匹配正则，结构为表ID-数据ID，如16-0
      const matchRes = key.match(this.nodeKeyReg)
      if (!matchRes) {
        return undefined
      }

      return {
        tableId: parseInt(matchRes.groups.tableId),
        dataId: parseInt(matchRes.groups.dataId),
      }
    },
    menuTreeNodeClick(event, data) {
      const key = data.node.key
      switch (key) {
        case '' + TableId.RepeaterInfo:
        case '' + TableId.RepeaterCommonSetting:
        case '' + TableId.RepeaterKeyFunctionSetting:
        case '' + TableId.RepeaterMenuSetting:
        case '' + TableId.RepeaterPresetSms:
        case '' + TableId.RepeaterPositionSetting:
        case '' + TableId.RepeaterBdSetting:
        case '' + TableId.RepeaterBdContact:
        case '' + TableId.RepeaterIpSetting:
        case '' + TableId.RepeaterServerSetting:
        case '' + TableId.RepeaterDmrSetting:
        case '' + TableId.RepeaterDmrContact:
        case '' + TableId.RepeaterOneChannel:
          this.tableId = parseInt(key)
          this.dataId = -1
          break
        default:
          const ids = this.getIdsFromNodeKey(key)
          if (ids) {
            this.tableId = ids.tableId
            this.dataId = ids.dataId
          }
      }
    },
    getAddMenu(disabled = false) {
      return [{ title: this.$t('dialog.add'), cmd: 'add', disabled }]
    },
    getDelMenu(disabled = false) {
      return [{ title: this.$t('dialog.delete'), cmd: 'del', disabled }]
    },
    loadContextmenu() {
      const $el = $(this.$refs[this.menuTreeId])
      $el.contextmenu({
        delegate: 'span.fancytree-node',
        autoFocus: true,
        menu: [],
        beforeOpen: (event, ui) => {
          // 右键菜单打开前，需要判断哪些节点能打开菜单，且判断命令选项是否禁用
          const node = $.ui.fancytree.getNode(ui.target)
          if (!node) {
            return false
          }
          node.setActive()

          const replaceMenu = menu => {
            $(this.$refs[this.menuTreeId]).contextmenu('replaceMenu', menu)
          }

          const customMenu = node.data.customMenu
          if (typeof node.data.customMenu === 'undefined') {
            return false
          }

          // 判断添加菜单项是否禁用
          let add_disabled = customMenu.add === 0
          // 判断是否禁用添加DMR联系人分组
          if (
            node.key === '' + TableId.RepeaterDmrContactGroup &&
            node.children.length === 16
          ) {
            add_disabled = true
          }

          // 判断区域数据是否已经添加到上限值，以禁用添加命令选项
          // 判断是否禁用添加一级区域
          if (node.key === 'area' && node.children.length === 4) {
            add_disabled = true
          }
          // 判断是否禁用添加二级区域
          if (
            node.key.startsWith(`${TableId.RepeaterMainZone}-`) &&
            node.children.length === 3000
          ) {
            add_disabled = true
          }
          // 判断是否禁用添加三级区域
          if (
            node.key.startsWith(`${TableId.RepeaterSubZone}-`) &&
            node.children.length === 3000
          ) {
            add_disabled = true
          }
          // 判断是否禁用添加信道数据,每个三级区域最多有16个信道成员
          if (
            node.key.startsWith(`${TableId.RepeaterUserZone}-`) &&
            node.children.length === 16
          ) {
            add_disabled = true
          }

          // 包含添加和删除菜单项
          const hasAddCmd = typeof customMenu.add !== 'undefined'
          const hasDelCmd = typeof customMenu.del !== 'undefined'
          if (hasAddCmd && hasDelCmd) {
            replaceMenu([
              ...this.getAddMenu(add_disabled),
              ...this.getDelMenu(customMenu.del === 0),
            ])
          } else if (hasAddCmd) {
            replaceMenu(this.getAddMenu(add_disabled))
          } else if (hasDelCmd) {
            // 只包含删除菜单项
            replaceMenu(this.getDelMenu(customMenu.del === 0))
          } else {
            return false
          }
        },
        select: (event, ui) => {
          const node = $.ui.fancytree.getNode(ui.target)
          if (!node) {
            bfglob.console.warn(
              'TR925 nav tree contextmenu err, the tree node is null',
              ui.target,
            )
            return
          }

          // ids 存在，则为动态创建的菜单选项
          const ids = this.getIdsFromNodeKey(node.key)

          switch (ui.cmd) {
            case 'add':
              if (ids) {
                switch (ids.tableId) {
                  // 添加二级区域菜单
                  case TableId.RepeaterMainZone:
                    this.addSubZoneData(ids.dataId)
                    break
                  // 添加三级区域菜单
                  case TableId.RepeaterSubZone:
                    this.addUserZoneData(ids.dataId)
                    break
                  // 添加三级区域下信道菜单
                  case TableId.RepeaterUserZone:
                    this.addChannelData(ids.dataId)
                    break
                }
              } else {
                switch (node.key) {
                  // 联系人分组菜单
                  case '' + TableId.RepeaterDmrContactGroup:
                    this.addOneDmrContactGroup()
                    break
                  // 添加一级区域菜单
                  case 'area':
                    this.addMainZoneData()
                    break
                }
              }
              break
            case 'del':
              if (ids) {
                switch (ids.tableId) {
                  // 删除联系人分组菜单选项
                  case TableId.RepeaterDmrContactGroup:
                    this.delOneDmrContactGroup(ids.dataId)
                    break
                  // 删除一级区域菜单
                  case TableId.RepeaterMainZone:
                    this.delMainZoneData(ids.dataId)
                    break
                  // 删除二级区域菜单
                  case TableId.RepeaterSubZone:
                    this.delSubZoneData(ids.dataId)
                    break
                  // 删除三级区域菜单
                  case TableId.RepeaterUserZone:
                    this.delUserZoneData(ids.dataId)
                    break
                  // 删除三级区域下信道菜单
                  case TableId.RepeaterOneChannel:
                    this.delChannelData(ids.dataId, true)
                    break
                }
              }
              break
            default:
          }
        },
      })

      const noData = i18n.global.t('dialog.noMatchText')
      $el.fancytree('getTree').options.strings.noData = noData
      $('.fancytree-statusnode-nodata .fancytree-title').html(noData)
    },
    initMenuTree() {
      const treeEl = this.$refs[this.menuTreeId]
      if (!treeEl) {
        this.$nextTick(() => {
          this.initMenuTree()
        })
        return
      }

      const getStoreKey = key => {
        return `${bfglob.userInfo.rid}--${key}`
      }

      // 初始化fancytree
      const $treeEl = $(treeEl)
      $treeEl.fancytree({
        extensions: ['persist'],
        selectMode: 1,
        source: this.menus,
        persist: {
          expandLazy: true,
          // store: "auto" // 'cookie', 'local': use localStore, 'session': sessionStore
          // 保存当前树的节点展开、选中等状态数据，以支持重新加载树时节点状态持久化
          // 数据保存在sessionStorage中，浏览器当前标签页中有效
          store: {
            get: function (key) {
              const _key = getStoreKey(key)
              this.info(`get("${_key}")`)
              return window.sessionStorage.getItem(_key)
            },
            set: function (key, value) {
              const _key = getStoreKey(key)
              this.info(`set("${_key}","${value}")`)
              window.sessionStorage.setItem(_key, value)
            },
            remove: function (key) {
              const _key = getStoreKey(key)
              this.info(`remove("${_key}")`)
              window.sessionStorage.removeItem(_key)
            },
          },
        },
        debugLevel: 0,
        sortChildren: true,
        strings: {
          noData: this.$t('dialog.noMatchText'),
        },
        click: this.menuTreeNodeClick,
      })
      // $treeEl.find(".ui-fancytree").addClass('fancytree-connectors');

      this.loadContextmenu()
    },

    // 添加一个DMR联系人分组
    getNextId(usedIds = [], limit = 1600) {
      let id = 0
      while (id < limit) {
        const _id = id + ''
        if (!usedIds.includes(_id)) {
          return id
        }
        id++
      }

      return -1
    },
    addOneDmrContactGroup(limit = 16) {
      const usedIds = Object.keys(this.dmrContactGroup || {})
      // 联系人分组id 0-16
      if (usedIds.length === limit) {
        return
      }

      const id = this.getNextId(usedIds, limit)
      if (id === -1) {
        return
      }

      const data = {
        groupId: id,
        userName:
          id === 0
            ? this.$t('dialog.frequentContacts')
            : `${this.$t('dialog.contactGrouping')} ${id}`,
        contactArray: [],
      }

      const cacheKey = TableId[TableId.RepeaterDmrContactGroup]
      if (typeof this.wfSettings[cacheKey] === 'undefined') {
        this.wfSettings[cacheKey] = {}
      }
      this.dmrContactGroup[id] = data
    },
    delOneDmrContactGroup(dataId) {
      if (typeof dataId !== 'number') {
        return
      }

      delete this.dmrContactGroup[dataId]
    },

    // 添加/删除一级菜单
    addMainZoneData(limit = 4) {
      const usedIds = Object.keys(this.mainZone || {})
      // 一级区域id值范围：0-3
      if (usedIds.length === limit) {
        return
      }

      const id = this.getNextId(usedIds, limit)
      if (id === -1) {
        return
      }

      const data = {
        id: id,
        name: `${this.$t('dialog.mainZone', { id: id + 1 })}`,
      }

      const cacheKey = TableId[TableId.RepeaterMainZone]
      if (typeof this.wfSettings[cacheKey] === 'undefined') {
        this.wfSettings[cacheKey] = {}
      }
      this.mainZone[id] = data

      // 联级添加二级菜单
      this.addSubZoneData(id)
    },
    delMainZoneData(dataId) {
      if (typeof dataId !== 'number') {
        return
      }

      delete this.mainZone[dataId]

      // 联级删除二级菜单
      Object.keys(this.subZone)
        // 过滤掉非当前删除的一级区域下的二级区域数据
        .filter(key => {
          return this.subZone[key].mainId === dataId
        })
        .forEach(key => {
          this.delSubZoneData(this.subZone[key].id)
        })
    },
    // 添加/删除二级菜单
    addSubZoneData(mainId, limit = 3000) {
      const usedIds = Object.keys(this.subZone || {})
      // 一级区域id值范围：0-2999
      if (usedIds.length === limit) {
        return
      }

      const id = this.getNextId(usedIds, limit)
      if (id === -1) {
        return
      }

      const data = {
        id: id,
        mainId,
        name: `${this.$t('dialog.subZone', { id: id + 1 })}`,
      }

      const cacheKey = TableId[TableId.RepeaterSubZone]
      if (typeof this.wfSettings[cacheKey] === 'undefined') {
        this.wfSettings[cacheKey] = {}
      }
      this.subZone[id] = data

      // 联级添加三级菜单
      this.addUserZoneData(id)
    },
    delSubZoneData(dataId) {
      if (typeof dataId !== 'number') {
        return
      }

      delete this.userZone[dataId]

      // 联级删除三级菜单
      Object.keys(this.userZone)
        // 过滤掉非当前删除的二级区域下的三级区域数据
        .filter(key => {
          return this.userZone[key].subId === dataId
        })
        .forEach(key => {
          this.delUserZoneData(this.userZone[key].id)
        })
    },
    // 添加/删除三级菜单
    addUserZoneData(subId, limit = 3000) {
      const usedIds = Object.keys(this.userZone || {})
      // 一级区域id值范围：0-2999
      if (usedIds.length === limit) {
        return
      }

      const id = this.getNextId(usedIds, limit)
      if (id === -1) {
        return
      }

      const data = {
        id: id,
        name: `${this.$t('dialog.userZone', { id: id + 1 })}`,
        subId,
        chNum: 0,
        chId: [],
      }

      const cacheKey = TableId[TableId.RepeaterUserZone]
      if (typeof this.wfSettings[cacheKey] === 'undefined') {
        this.wfSettings[cacheKey] = {}
      }
      this.userZone[id] = data

      // 联级添加信道菜单
      this.addChannelData(id)
    },
    delUserZoneData(dataId) {
      if (typeof dataId !== 'number') {
        return
      }

      // 先缓存三级区域数据，以便删除信道数据使用
      const cacheUserZone = {
        ...this.userZone[dataId],
      }

      delete this.userZone[dataId]

      // 联级删除三级区域数据下所有信道数据
      cacheUserZone.chId &&
        cacheUserZone.chId.forEach(chId => {
          this.delChannelData(chId)
        })
    },

    // 添加/删除信道菜单，每个三级区域最多有16个信道成员
    addChannelData(userId, limit = 3776) {
      const usedIds = Object.keys(this.channels || {})
      // 一级区域id值范围：0-3775
      if (usedIds.length === limit) {
        return
      }

      const id = this.getNextId(usedIds, limit)
      if (id === -1) {
        return
      }

      // 只生成关键字段属性
      const data = {
        chId: id,
        chName: `${this.$t('dialog.digitalChannel')} ${id + 1}`,
      }

      const cacheKey = TableId[TableId.RepeaterOneChannel]
      if (typeof this.wfSettings[cacheKey] === 'undefined') {
        this.wfSettings[cacheKey] = {}
      }
      this.channels[id] = data

      // 关联到对应的三级区域数据中
      const cacheUserZone = { ...this.userZone[userId] }
      if (!cacheUserZone.chId) {
        cacheUserZone.chId = []
      }
      cacheUserZone.chId.push(id)
      cacheUserZone.chNum = cacheUserZone.chId.length
      this.userZone[userId] = cacheUserZone
    },
    delChannelData(dataId) {
      if (typeof dataId !== 'number') {
        return
      }

      // 删除信道时，需要修改三级区域的chId,chNum属性
      delete this.channels[dataId]
    },

    // 动态加载组件
    dynamicLoadingComponent(src) {
      if (typeof src !== 'string' || !src) {
        return new Promise(resolve => {
          resolve(undefined)
        })
      }

      return import(`@/components/repeaterWf/common/${src}.vue`).then(res => {
        return res.default || res
      })
    },

    // 创建DMR通讯录联系人分组导航菜单
    setDmrContactGroupNodeOption(data) {
      return {
        key: `${TableId.RepeaterDmrContactGroup}-${data.groupId}`,
        title:
          data.userName ||
          `${this.$t('dialog.contactGrouping')} ${data.groupId + 1}`,
        customMenu: {
          del: data.groupId === 0 ? 0 : 1,
        },
      }
    },
    createDmrContactGroupingMenu() {
      const dmrContactData =
        this.wfSettings[TableId[TableId.RepeaterDmrContactGroup]]
      if (!dmrContactData) {
        return []
      }

      return Object.keys(dmrContactData).map(key => {
        // key 为信道数据id
        const data = dmrContactData[key]
        return this.setDmrContactGroupNodeOption(data)
      })
    },

    // 创建区域导航菜单
    setChannelNodeOption(data) {
      return {
        key: `${TableId.RepeaterOneChannel}-${data.chId}`,
        title:
          data.chName ||
          this.$t('dialog.digitalChannel', { id: data.chId + 1 }),
        customMenu: {
          del: data.chId === 0 ? 0 : 1,
        },
      }
    },
    createChannelMenu(userZoneChId) {
      // userZoneChId 为三级区域数据中包含的信道数据id数组
      const wfSettings =
        (this.repeaterData && this.repeaterData.writeFrequencySetting) || {}
      const channels = wfSettings[TableId[TableId.RepeaterOneChannel]]
      if (!channels) {
        return []
      }

      return Object.keys(channels)
        .filter(key => {
          // 过滤方法，找到指定三级区域下所有信道数据
          return userZoneChId.includes(channels[key].chId)
        })
        .map(key => {
          // key 为信道数据id
          const data = channels[key]
          return {
            ...this.setChannelNodeOption(data),
          }
        })
    },
    setUserZoneNodeOption(data) {
      return {
        key: `${TableId.RepeaterUserZone}-${data.id}`,
        title: data.name || this.$t('dialog.userZone', { id: data.id + 1 }),
        folder: true,
        customMenu: {
          add: 1,
          del: data.id === 0 ? 0 : 1,
        },
        children: [],
      }
    },
    createUserZoneMenu(subId) {
      const wfSettings =
        (this.repeaterData && this.repeaterData.writeFrequencySetting) || {}
      const userZoneData = wfSettings[TableId[TableId.RepeaterUserZone]]
      if (!userZoneData) {
        return []
      }

      return Object.keys(userZoneData)
        .filter(key => {
          // 过滤方法，找到指定subId的三级区域数据
          return userZoneData[key].subId === subId
        })
        .map(key => {
          // key 为三级区域的数据id
          const data = userZoneData[key]
          return {
            ...this.setUserZoneNodeOption(data),
            children: this.createChannelMenu(data.chId),
          }
        })
    },
    setSubZoneNodeOption(data) {
      return {
        key: `${TableId.RepeaterSubZone}-${data.id}`,
        title: data.name || this.$t('dialog.subZone', { id: data.id + 1 }),
        folder: true,
        customMenu: {
          add: 1,
          del: data.id === 0 ? 0 : 1,
        },
        children: [],
      }
    },
    createSubZoneMenu(mainId) {
      const wfSettings =
        (this.repeaterData && this.repeaterData.writeFrequencySetting) || {}
      const subZoneData = wfSettings[TableId[TableId.RepeaterSubZone]]
      if (!subZoneData) {
        return []
      }

      return Object.keys(subZoneData)
        .filter(key => {
          // 过滤方法，找到指定mainId的二级区域数据
          return subZoneData[key].mainId === mainId
        })
        .map(key => {
          // key 为二级区域的数据id
          const data = subZoneData[key]
          return {
            ...this.setSubZoneNodeOption(data),
            children: this.createUserZoneMenu(data.id),
          }
        })
    },
    setAreaZoneNodeOption(data) {
      return {
        key: `${TableId.RepeaterMainZone}-${data.id}`,
        title: data.name || this.$t('dialog.mainZone', { id: data.id + 1 }),
        folder: true,
        children: [],
        customMenu: {
          add: 1,
          del: data.id === 0 ? 0 : 1,
        },
      }
    },
    createAreaZoneMenu(mainZoneData) {
      return Object.keys(mainZoneData).map(key => {
        // key 为一级区域的数据id
        const data = mainZoneData[key]
        return {
          ...this.setAreaZoneNodeOption(data),
          children: this.createSubZoneMenu(data.id),
        }
      })
    },

    // 排序指定节点的子级节点
    sortSpecifiedNodeChildren(targetNode, deep = false) {
      if (!targetNode || typeof targetNode.sortChildren !== 'function') {
        return
      }

      targetNode.sortChildren((a, b) => {
        const aIds = this.getIdsFromNodeKey(a.key)
        const bIds = this.getIdsFromNodeKey(b.key)
        if (!aIds || !bIds) {
          return 0
        }

        return aIds.dataId > bIds.dataId
          ? 1
          : aIds.dataId < bIds.dataId
            ? -1
            : 0
      }, deep)
    },
    getAreaNode() {
      return bftree.getTreeNodeByRid(this.menuTreeId, 'area')
    },
    getSpecifiedNodes(tableId) {
      const areaNode = this.getAreaNode()
      if (!areaNode || !tableId) {
        return []
      }

      const getSubNodes = (children, result) => {
        for (let i = 0; i < children.length; i++) {
          const node = children[i]
          const nodeChildren = node.getChildren() || []
          if (node.key.startsWith(tableId + '-')) {
            result.push(node)
          } else {
            getSubNodes(nodeChildren, result)
          }
        }

        return result
      }

      return getSubNodes(areaNode.getChildren() || [], [])
    },
    getAllSubZoneNodes() {
      return this.getSpecifiedNodes(TableId.RepeaterSubZone)
    },
    getAllUserZoneNodes() {
      return this.getSpecifiedNodes(TableId.RepeaterUserZone)
    },
    getAllChannelNodes() {
      return this.getSpecifiedNodes(TableId.RepeaterOneChannel)
    },

    // 通过id查找对应数据
    getUserZoneByChId(chId) {
      for (const k in this.userZone) {
        const item = this.userZone[k]
        if (item.chId.includes(chId)) {
          return item
        }
      }

      return undefined
    },
  },
  computed: {
    optGroupSize() {
      return 'medium'
    },
    fullscreen() {
      return this.bfmaxi ? true : !(this.$root.layoutLevel > 0)
    },
    isEn() {
      return this.$i18n.locale === 'en'
    },
    isMobile() {
      return this.$root.layoutLevel === 0
    },
    channelTableColumns() {
      return [
        {
          prop: 'chId',
          label: this.$t('dialog.chId'),
          minWidth: this.fullscreen ? 'auto' : this.isEn ? '110px' : '80px',
        },
        {
          prop: 'chName',
          label: this.$t('dialog.chName'),
          minWidth: this.fullscreen ? 'auto' : this.isEn ? '130px' : '100px',
        },
        {
          prop: 'rxFrequency',
          label: this.$t('dialog.rxFrequency'),
          minWidth: this.fullscreen ? 'auto' : this.isEn ? '230px' : '130px',
        },
        {
          prop: 'txFrequency',
          label: this.$t('dialog.txFrequency'),
          minWidth: this.fullscreen ? 'auto' : this.isEn ? '230px' : '130px',
        },
        {
          prop: 'txPower',
          label: this.$t('dialog.txPower'),
          minWidth: this.fullscreen ? 'auto' : this.isEn ? '140px' : '100px',
        },
      ]
    },
    // 没有选中继设备，或中继不在线，则禁用按钮功能
    notRepeater() {
      return (
        !this.repeater ||
        !this.repeaterData ||
        this.repeaterData.ctrlStats !== 1
      )
    },
    // 中继的写频数据配置
    wfSettings() {
      return this.repeaterData && this.repeaterData.writeFrequencySetting
    },
    dmrContactGroup() {
      return (
        this.wfSettings &&
        this.wfSettings[TableId[TableId.RepeaterDmrContactGroup]]
      )
    },
    mainZone() {
      return (
        this.wfSettings && this.wfSettings[TableId[TableId.RepeaterMainZone]]
      )
    },
    subZone() {
      return (
        this.wfSettings && this.wfSettings[TableId[TableId.RepeaterSubZone]]
      )
    },
    userZone() {
      return (
        this.wfSettings && this.wfSettings[TableId[TableId.RepeaterUserZone]]
      )
    },
    channels() {
      return (
        this.wfSettings && this.wfSettings[TableId[TableId.RepeaterOneChannel]]
      )
    },
    // 导航菜单配置
    menus() {
      // 动态添加部分，需要设置有右键菜单
      // 自定义右键菜单，属性名为命令项，值为0/1，对应禁用/启用
      // customMenu: {
      //   add: 1,
      //   del: 0,
      // },

      if (!this.wfSettings) {
        return Object.keys(this.defaultMenus).map(key => {
          return this.defaultMenus[key]
        })
      }

      // 创建DMR通讯录联系人分组导航菜单
      const dmrGroupingMenu = this.createDmrContactGroupingMenu()

      // 递归合并联系人分组菜单
      const dmrServiceChildren = this.defaultMenus.dmrService.children
      const getRepeaterDmrContactGroup = children => {
        for (let i = 0; i < children.length; i++) {
          const item = children[i]
          if (item.key === '' + TableId.RepeaterDmrContactGroup) {
            children[i].children = dmrGroupingMenu
            return true
          }
          if (item.children && item.children.length > 0) {
            const res = getRepeaterDmrContactGroup(item.children)
            if (res) {
              return
            }
          }
        }
      }
      getRepeaterDmrContactGroup(dmrServiceChildren)

      // 创建区域数据导航菜单
      let areaMenu = []
      const mainZoneData = this.wfSettings[TableId[TableId.RepeaterMainZone]]
      if (mainZoneData) {
        areaMenu = this.createAreaZoneMenu(mainZoneData)
      }

      const objMenu = merge(this.defaultMenus, {
        dmrService: {
          children: dmrServiceChildren,
        },
        area: {
          children: areaMenu,
        },
      })
      const menus = Object.keys(objMenu).map(key => {
        return objMenu[key]
      })

      return menus
    },
    defaultMenus() {
      return {
        [TableId.RepeaterInfo]: {
          key: '' + TableId.RepeaterInfo,
          title: this.$t('dialog.repeaterInfo'),
          folder: true,
        },
        [TableId.RepeaterCommonSetting]: {
          key: '' + TableId.RepeaterCommonSetting,
          title: this.$t('dialog.generalSetting'),
          folder: true,
        },
        [TableId.RepeaterKeyFunctionSetting]: {
          key: '' + TableId.RepeaterKeyFunctionSetting,
          title: this.$t('dialog.buttonDefinition'),
          folder: true,
        },
        [TableId.RepeaterMenuSetting]: {
          key: '' + TableId.RepeaterMenuSetting,
          title: this.$t('dialog.menuSettings'),
          folder: true,
        },
        [TableId.RepeaterPresetSms]: {
          key: '' + TableId.RepeaterPresetSms,
          title: this.$t('dialog.preMadeSms'),
          folder: true,
        },
        annex: {
          key: 'annex',
          title: this.$t('dialog.annex'),
          folder: true,
          children: [
            {
              key: 'positionService',
              title: this.$t('dialog.satellitePositionService'),
              folder: true,
              children: [
                {
                  key: '' + TableId.RepeaterPositionSetting,
                  title: this.$t('dialog.satellitePositionSetting'),
                },
                {
                  key: 'beidouService',
                  title: this.$t('dialog.beidouService'),
                  folder: true,
                  children: [
                    {
                      key: '' + TableId.RepeaterBdSetting,
                      title: this.$t('dialog.beidouSetting'),
                    },
                    {
                      key: '' + TableId.RepeaterBdContact,
                      title: this.$t('dialog.beidouContact'),
                    },
                  ],
                },
              ],
            },
            {
              key: 'networkService',
              title: this.$t('dialog.networkService'),
              folder: true,
              children: [
                {
                  key: '' + TableId.RepeaterIpSetting,
                  title: this.$t('dialog.networkSetting'),
                },
                {
                  key: '' + TableId.RepeaterServerSetting,
                  title: this.$t('dialog.serverSetting'),
                },
              ],
            },
          ],
        },
        dmrService: {
          key: 'dmrService',
          title: this.$t('dialog.dmrService'),
          folder: true,
          children: [
            {
              key: '' + TableId.RepeaterDmrSetting,
              title: this.$t('dialog.basicConfig'),
            },
            {
              key: 'dmr-contact',
              title: this.$t('dialog.contact'),
              folder: true,
              children: [
                {
                  key: '' + TableId.RepeaterDmrContact,
                  title: this.$t('dialog.dmrContact'),
                },
                {
                  key: '' + TableId.RepeaterDmrContactGroup,
                  title: this.$t('dialog.contactGrouping'),
                  folder: true,
                  customMenu: {
                    add: 1,
                  },
                  children: [
                    // 默认显示一个常用联系人
                  ],
                },
              ],
            },
          ],
        },
        area: {
          key: 'area',
          title: this.$t('dialog.area'),
          folder: true,
          customMenu: {
            add: 1,
          },
          children: [],
        },
      }
    },
    tableName() {
      return TableNames[this.tableId + '']
    },
  },
  mounted() {
    this.initMenuTree()
  },
  watch: {
    '$i18n.locale'(val) {
      // 切换语言，重新加载右键菜单
      this.loadContextmenu()
    },
    dmrContactGroup: {
      deep: true,
      handler(contactGroup) {
        const key = '' + TableId.RepeaterDmrContactGroup
        const contactGroupNode = bftree.getTreeNodeByRid(this.menuTreeId, key)

        // 对象不存在，则清空联系人分组所有子节点
        if (!contactGroup) {
          contactGroupNode && contactGroupNode.removeChildren()
          return
        }

        if (!contactGroupNode) {
          return
        }
        const nodes = contactGroupNode.getChildren() || []
        // 返回二维数组 [[key1,val1],[key2,val2],...]
        const entriesGroups = Object.entries(contactGroup)

        // 遍历联系人分组数据，如果节点title与数据不同，则更新
        // 如果不存在对应的节点，则添加节点菜单
        const compare = data => {
          for (let i = 0; i < nodes.length; i++) {
            const node = nodes[i]
            const key = `${TableId.RepeaterDmrContactGroup}-${data.groupId}`
            // 判断是否需要更新
            if (node.key === key) {
              if (node.title !== data.userName) {
                // 当前分组数据有变化，更新节点
                node.setTitle(data.userName)
              }
              return
            }
          }

          // 添加节点
          contactGroupNode.addChildren(this.setDmrContactGroupNodeOption(data))
        }

        entriesGroups.forEach(data => {
          compare(data[1])
        })

        // 删除找不到对应分组数据的节点
        nodes.forEach(node => {
          const groupId = parseInt(node.key.split('-').pop())
          for (let k = 0; k < entriesGroups.length; k++) {
            const item = entriesGroups[k][1]
            if (item.groupId === groupId) {
              break
            }
            // 遍历到最后一个数据时，都未找到节点对应的数据，则标记该节点需要删除
            if (k === entriesGroups.length - 1) {
              node.remove()
            }
          }
        })

        // 排序
        this.sortSpecifiedNodeChildren(contactGroupNode)
      },
    },
    mainZone: {
      deep: true,
      handler(mainZone) {
        const mainZoneParentNode = this.getAreaNode()

        // 对象不存在，则清空所有区域子节点
        if (!mainZone) {
          mainZoneParentNode && mainZoneParentNode.removeChildren()
          return
        }

        if (!mainZoneParentNode) {
          return
        }
        const nodes = mainZoneParentNode.getChildren() || []
        // 返回二维数组 [[key1,val1],[key2,val2],...]
        const entriesGroups = Object.entries(mainZone)

        // 标记节点需要执行更新或添加操作
        const compare = data => {
          for (let i = 0; i < nodes.length; i++) {
            const node = nodes[i]
            const key = `${TableId.RepeaterMainZone}-${data.id}`
            // 判断是否需要更新
            if (node.key === key) {
              if (node.title !== data.name) {
                // 当前分组数据有变化，需要更新
                node.setTitle(data.name)
              }
              return
            }
          }

          // 添加节点
          mainZoneParentNode.addChildren(this.setAreaZoneNodeOption(data))
        }

        entriesGroups.forEach(data => {
          compare(data[1])
        })

        // 标记节点是否需要删除
        nodes.forEach(node => {
          const groupId = parseInt(node.key.split('-').pop())
          for (let k = 0; k < entriesGroups.length; k++) {
            const item = entriesGroups[k][1]
            if (item.id === groupId) {
              break
            }
            // 遍历到最后一个数据时，都未找到节点对应的数据，则标记该节点需要删除
            if (k === entriesGroups.length - 1) {
              node.remove()
            }
          }
        })

        // 排序
        this.sortSpecifiedNodeChildren(mainZoneParentNode)
      },
    },
    subZone: {
      deep: true,
      handler(subZone) {
        // 对象不存在，则清空所有二级区域节点
        const nodes = this.getAllSubZoneNodes()
        if (!subZone) {
          nodes.forEach(node => {
            node.remove()
          })
          return
        }

        // 返回二维数组 [[key1,val1],[key2,val2],...]
        const entriesGroups = Object.entries(subZone)

        // 标记节点需要执行更新或添加操作
        const compare = data => {
          for (let i = 0; i < nodes.length; i++) {
            const node = nodes[i]
            const key = `${TableId.RepeaterSubZone}-${data.id}`
            // 判断是否需要更新
            if (node.key === key) {
              if (node.title !== data.name) {
                // 当前分组数据有变化，需要更新
                node.setTitle(data.name)
              }
              return
            }
          }

          // 添加节点
          const parentKey = `${TableId.RepeaterMainZone}-${data.mainId}`
          const parentNode = bftree.getTreeNodeByRid(this.menuTreeId, parentKey)
          parentNode && parentNode.addChildren(this.setSubZoneNodeOption(data))
        }
        entriesGroups.forEach(data => {
          compare(data[1])
        })

        // 标记节点是否需要删除
        nodes.forEach(node => {
          const dataId = parseInt(node.key.split('-').pop())
          for (let k = 0; k < entriesGroups.length; k++) {
            const item = entriesGroups[k][1]
            if (item.id === dataId) {
              break
            }
            // 遍历到最后一个数据时，都未找到节点对应的数据，则标记该节点需要删除
            if (k === entriesGroups.length - 1) {
              node.remove()
            }
          }
        })

        // 排序
        const mainZoneParentNode = this.getAreaNode()
        this.sortSpecifiedNodeChildren(mainZoneParentNode, true)
      },
    },
    userZone: {
      deep: true,
      handler(userZone) {
        // 对象不存在，则清空所有三级区域节点
        const nodes = this.getAllUserZoneNodes()
        if (!userZone) {
          nodes.forEach(node => {
            node.remove()
          })
          return
        }

        // 返回二维数组 [[key1,val1],[key2,val2],...]
        const entriesGroups = Object.entries(userZone)

        // 标记节点需要执行更新或添加操作
        const compare = data => {
          for (let i = 0; i < nodes.length; i++) {
            const node = nodes[i]
            const key = `${TableId.RepeaterUserZone}-${data.id}`
            // 判断是否需要更新
            if (node.key === key) {
              if (node.title !== data.name) {
                // 当前分组数据有变化，需要更新
                node.setTitle(data.name)
              }
              return
            }
          }

          // 添加节点
          const parentKey = `${TableId.RepeaterSubZone}-${data.subId}`
          const parentNode = bftree.getTreeNodeByRid(this.menuTreeId, parentKey)
          parentNode && parentNode.addChildren(this.setUserZoneNodeOption(data))
        }
        entriesGroups.forEach(data => {
          compare(data[1])
        })

        // 标记节点是否需要删除
        nodes.forEach(node => {
          const dataId = parseInt(node.key.split('-').pop())
          for (let k = 0; k < entriesGroups.length; k++) {
            const item = entriesGroups[k][1]
            if (item.id === dataId) {
              break
            }
            // 遍历到最后一个数据时，都未找到节点对应的数据，则标记该节点需要删除
            if (k === entriesGroups.length - 1) {
              node.remove()
            }
          }
        })

        // 排序
        const mainZoneParentNode = this.getAreaNode()
        this.sortSpecifiedNodeChildren(mainZoneParentNode, true)
      },
    },
    channels: {
      deep: true,
      handler(channels) {
        // 对象不存在，则清空所有信道数据节点
        const nodes = this.getAllChannelNodes()
        if (!channels) {
          nodes.forEach(node => {
            node.remove()
          })
          return
        }

        // 返回二维数组 [[key1,val1],[key2,val2],...]
        const entriesGroups = Object.entries(channels)

        // 标记节点需要执行更新或添加操作
        const compare = data => {
          for (let i = 0; i < nodes.length; i++) {
            const node = nodes[i]
            const key = `${TableId.RepeaterOneChannel}-${data.chId}`
            // 判断是否需要更新
            if (node.key === key) {
              if (node.title !== data.chName) {
                // 当前分组数据有变化，需要更新
                node.setTitle(data.chName)
              }
              return
            }
          }

          // 添加节点
          const parentData = this.getUserZoneByChId(data.chId)
          if (!parentData) {
            return
          }

          const parentKey = `${TableId.RepeaterUserZone}-${parentData.id}`
          const parentNode = bftree.getTreeNodeByRid(this.menuTreeId, parentKey)
          parentNode && parentNode.addChildren(this.setChannelNodeOption(data))
        }
        entriesGroups.forEach(data => {
          compare(data[1])
        })

        // 标记节点是否需要删除
        nodes.forEach(node => {
          const dataId = parseInt(node.key.split('-').pop())
          for (let k = 0; k < entriesGroups.length; k++) {
            const item = entriesGroups[k][1]
            if (item.chId === dataId) {
              break
            }
            // 遍历到最后一个数据时，都未找到节点对应的数据，则标记该节点需要删除
            if (k === entriesGroups.length - 1) {
              node.remove()
            }
          }
        })

        // 排序
        const mainZoneParentNode = this.getAreaNode()
        this.sortSpecifiedNodeChildren(mainZoneParentNode, true)
      },
    },
  },
  components: {
    generateDmrId: defineAsyncComponent(
      () => import('@/components/common/generateDmrId'),
    ),
    Restart: defineAsyncComponent(
      () => import('@/components/repeaterWf/common/Restart'),
    ),
    SwitchTransmitPower: defineAsyncComponent(
      () => import('@/components/repeaterWf/common/SwitchTransmitPower'),
    ),
    SwitchChannel: defineAsyncComponent(
      () => import('@/components/repeaterWf/common/SwitchChannel'),
    ),
  },
  beforeMount() {
    Object.keys(TableNames).forEach(key => {
      this.dynamicLoadingComponent(TableNames[key]).then(res => {
        if (!res) {
          return
        }
        this.dynamicComponents[res.name] = res
      })
    })

    // 生成默认的DMR联系人分组，常用联系人分组
    this.addOneDmrContactGroup()

    // 生成默认的一级区域及下级区域数据
    this.addMainZoneData()
  },
}
</script>

<style>
.tr925-wrap {
  flex: auto;
  display: flex;
  flex-direction: column;
  height: calc(100% - 42px);
}

.tr925-body {
  display: flex;
  justify-content: center;
  flex: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.tr925-menu-tree {
  flex: none;
  border-right: 1px solid #ebeef5;
  width: 200px;
  height: 100%;
  box-sizing: border-box;
}

.tr925-menu-tree .fancytree-container {
  border-radius: 4px;
  width: 100%;
}

.tr925-main {
  flex: auto;
  box-sizing: border-box;
  overflow: auto;
}

.tr925-main > .el-form {
  padding: 10px;
  box-sizing: border-box;
}

.tr925-main .el-row {
  margin-left: 0 !important;
  margin-right: 0 !important;
}

.tr925-operator-group {
  flex: none;
  padding: 10px 10px 0;
  text-align: center;
}

.tr925-operator-group .el-button {
  width: 20%;
}
</style>
