<template>
  <el-dialog
    v-model="visible"
    :title="$t('dialog.alertTitle')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    class="bcxxResult-dialog bcxxResult_dialog"
    :fullscreen="fullscreen"
    top="0"
    width="30%"
    @close="closeDlgFn"
    @open="openDlgFn"
  >
    <div :ref="name" class="bcxxResult_body">
      <h4 v-if="result.title" class="result_title" v-text="result.title" />
      <!--      <el-button type="text" @click="rangeDeviation">点击查看与所选范围的偏差</el-button>-->
      <div class="result_content" v-html="result.content" />
      <span
        v-if="result.btn == 'gps'"
        class="select_more_linPoint_list"
        @click="checked_gpsPoint_detail(result.pointCard)"
        v-text="$t('dialog.lookGpsPointData')"
      />
      <span
        v-else-if="result.btn == 'dev'"
        class="select_more_linPoint_list"
        @click="add_new_device(result.dmrId)"
        v-text="$t('msgbox.addToSql')"
      />
      <span
        v-else-if="result.btn == 'ctrl'"
        class="select_more_linPoint_list"
        @click="add_new_controller(result.dmrId)"
        v-text="$t('msgbox.addToSql')"
      />
      <span
        v-else-if="!!result.btn"
        class="select_more_linPoint_list"
        @click="processCallback(result.btnCallback)"
        >{{ $t('msgbox.addToSql') }}</span
      >
      <span v-else />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button
          type="primary"
          @click="destroyDialog"
          v-text="$t('dialog.confirm')"
        />
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { displayFirst } from '@/utils/bfutil'
import { useRouteParams } from '@/router'

const { setRouteParams } = useRouteParams()

export default {
  inject: ['mainApp'],
  props: {
    result: {
      type: Object,
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      visible: true,
    }
  },
  methods: {
    processCallback(btnCallback) {
      this.destroyDialog()
      typeof btnCallback === 'function' && btnCallback()
    },
    openDlgFn() {
      setTimeout(() => {
        displayFirst(this.$el.firstElementChild)
      }, 100)
    },
    closeDlgFn() {
      this.destroyDialog()
    },
    checked_gpsPoint_detail: function (rfid) {
      this.destroyDialog()
      bfglob.emit('close_vsendcmd')
      if (this.$route.name === 'linePoint') {
        bfglob.emit('show_gpsLinePoint_detail', { rfid: rfid })
      } else {
        setRouteParams('linePoint', { rfid: rfid })
        this.$router.replace({ name: 'linePoint' })
      }
    },
    add_new_device: function (dmrId) {
      this.destroyDialog()
      if (this.$route.name === 'devices') {
        bfglob.emit('add_new_device', { dmrId: dmrId })
      } else {
        setRouteParams('devices', { dmrId: dmrId })
        this.$router.replace({ name: 'devices' })
      }
    },
    add_new_controller: function (dmrId) {
      this.destroyDialog()
      if (this.$route.name === 'controllers') {
        bfglob.emit('add_new_controller', { dmrId: dmrId })
      } else {
        setRouteParams('controllers', { dmrId: dmrId })
        this.$router.replace({ name: 'controllers' })
      }
    },
    destroyDialog() {
      this.visible = false
      this.$nextTick(() => {
        this.setModelOpacity()
      })
      // 发布销毁vue实例消息，清除app vue实例中的相关数据
      bfglob.emit('bcxx_destroyDialog', this.name)
    },
    // todo 点击查看与所选范围的偏差,关闭弹窗并打开发送命令页面组件的地图,并在地图绘制经纬度范围和所勾选的设备
    // 关闭弹窗并打开发送页面组件的地图
    // rangeDeviation() {
    //   bfglob.emit('rangeDeviation')
    //   this.destroyDialog()
    // }

    // 判断多少个弹窗,修改遮罩层的颜色深度 1个: opacity:0.5 ,2个: opacity: 0.6
    setModelOpacity() {
      const dialogLen = document.querySelectorAll('.bcxxResult-dialog').length
      const modal = document.querySelector('.v-modal')
      if (!modal) return
      modal.style.opacity = `${0.4 + dialogLen / 10}`
    },
  },
  mounted() {
    // 订阅bc11指令中销毁vue实例消息
    bfglob.on('bc11_destroyDialog', name => {
      this.name === name && this.destroyDialog()
    })
    this.openDlgFn()
    this.setModelOpacity()
  },
  computed: {
    fullscreen() {
      return !(this.$root.layoutLevel > 0)
    },
  },
}
</script>

<style>
.bcxxResult_body {
  padding: 0 12px;
  text-align: center;
}

.bcxxResult_dialog {
  top: 40%;
}

.bcxxResult_dialog .el-dialog__body {
  min-height: unset;
  max-height: 300px;
}

.bcxxResult_dialog .el-dialog__footer {
  text-align: center;
}

.dialog-footer button {
  width: 35%;
  min-width: 120px;
}

.range_close_btn {
  background-color: #20a0ff;
  color: #fff;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  text-align: center;
  font-size: 18px;
  cursor: pointer;
}

.result_title,
.result_content {
  line-height: 24px;
}
</style>
