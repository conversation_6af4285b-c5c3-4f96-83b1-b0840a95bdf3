import { createRouter, createWebHashHistory } from 'vue-router'
import { computed } from 'vue'
import { navItemConfig } from './dataNav'

const LoginRouteName = 'login'

// 数据平台的路由配置
// const dataManageFiles = import.meta.glob('@/platform/dataManage/*.vue')
// const dataManageRoutes = Object.keys(dataManageFiles).map(filePath => {
//   console.log('filePath', filePath)
//   const name = filePath.replace(/^\/src\/platform\/dataManage\/(.*)\.vue$/, '$1')
//   return {
//     path: name,
//     name: name,
//     component: dataManageFiles[filePath],
//     meta: {
//       navItemConfig: computed(() => navItemConfig.value[name]),
//     },
//   }
// })

const allChildrenFiles = import.meta.glob('@/platform/dataManage/**/*.vue')

// 遍历 navItemConfig 构建路由
const dataManageRoutes = Object.keys(navItemConfig.value).map(key => {
  const item = navItemConfig.value[key]
  const itemRoute = {
    path: item.name,
    name: item.name,
    meta: {
      navItemConfig: computed(() => item),
    },
  }

  if (item.default) {
    itemRoute.redirect = `${item.name}/${item.default}`

    const children = Object.keys(allChildrenFiles)
      .filter(path => path.includes(`/platform/dataManage/${item.name}/`))
      .map(path => {
        // 从路径中提取组件名
        const match = path.match(new RegExp(`^/src/platform/dataManage/${item.name}/(.*)\\.vue$`))
        const name = match?.[1]?.replace(/\.vue$/, '') ?? 'unknown'

        return {
          name,
          path: `${item.name}/${name}`,
          component: allChildrenFiles[path], // 这里可以直接给 component 用于路由懒加载
        }
      })

    itemRoute.children = children
  } else {
    itemRoute.component = allChildrenFiles[`/src/platform/dataManage/${key}.vue`]
  }
  return itemRoute
})

console.log('dataManageRoutes1', dataManageRoutes)

// // 数据菜单下的路由配置
// const dataNavFiles = import.meta.glob('@/components/dataDialog/*.vue')
// const dataNavRoutes = Object.keys(dataNavFiles).map(filePath => {
//   const name = filePath.replace(
//     /^\/src\/components\/dataDialog\/(.*)\.vue$/,
//     '$1',
//   )
//   return {
//     path: name,
//     name: name,
//     component: dataNavFiles[filePath],
//   }
// })
//
// // 数据菜单下的路由配置
// const historyNavFiles = import.meta.glob('@/components/historyDialog/*.vue')
//
// const historyNavRoutes = Object.keys(historyNavFiles).map(filePath => {
//   const name = filePath.replace(
//     /^\/src\/components\/historyDialog\/(.*)\.vue$/,
//     '$1',
//   )
//   return {
//     path: name,
//     name: name,
//     component: historyNavFiles[filePath],
//   }
// })

// 路由配置
const routes = [
  {
    path: '/upgrade',
    name: 'upgrade',
    component: () => import('@/layouts/upgrade.vue'),
  },
  {
    path: '/login',
    name: LoginRouteName,
    component: () => import('@/layouts/BfLogin.vue'),
  },
  {
    path: '/dataManage',
    name: 'manage',
    component: () => import('@/layouts/AdminLayout.vue'),
    redirect: '/dataManage/orgs',
    children: [...dataManageRoutes],
  },

  // {
  //   path: '/',
  //   component: () => import('@/layouts/Layout.vue'),
  //   redirect: '/main',
  //   children: [
  //     {
  //       path: 'main',
  //       name: 'main',
  //       component: () => import('@/components/layout/BfMain.vue'),
  //       meta: {
  //         affix: true, // 是否固定路由标签
  //       },
  //     },
  //     {
  //       path: 'notes',
  //       name: 'notes',
  //       component: () => import('@/layouts/notes.vue'),
  //     },
  //     {
  //       path: 'relatedSoftware',
  //       name: 'relatedSoftware',
  //       component: () => import('@/components/secondary/relatedSoftware.vue'),
  //     },
  //     {
  //       path: 'interphoneWf',
  //       name: 'interphoneWf',
  //       component: () => import('@/components/interphoneWf/interphoneWf.vue'),
  //     },
  //     {
  //       path: 'repeaterWriteFrequency',
  //       name: 'repeaterWriteFrequency',
  //       component: () =>
  //         import('@/components/repeaterWf/repeaterWriteFrequency.vue'),
  //     },
  //     {
  //       path: 'sendcmd',
  //       name: 'sendcmd',
  //       component: () => import('@/components/command/sendcmd.vue'),
  //     },
  //     ...dataNavRoutes,
  //     ...historyNavRoutes,
  //   ],
  // },

  // 404 page must be placed at the end !!!
  // { path: '*', hidden: true, component: () => import('@/components/layout/Error404.vue') },
  {
    path: '/:pathMatch(.*)*', //  Vue Router 4 使用  /:pathMatch(.*)*  替代了原来的 '*'
    name: 'NotFound', // 建议添加一个 name
    component: () => import('@/layouts/Error404.vue'),
    meta: {
      hidden: true, //  保留 hidden 属性，方便后续使用
    },
  },
]
// 创建路由实例
const router = createRouter({
  history: createWebHashHistory(), // 使用 hash 模式 (兼容性更好)
  routes, // 路由配置
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  bfglob.console.log('router.beforeEach', to, from)
  bfglob.console.log('login status', bfglob.isLogin)

  // 提示用户升级浏览器页面
  if (to.name === 'upgrade') {
    next()
    return
  }

  if (to.query?.sysId) {
    if (from.query.sysId && to.query.sysId !== from.query.sysId) {
      location.reload()
      return
    }

    if (!bfglob.isLogin) {
      if (to.name === LoginRouteName) {
        next()
      } else {
        next({ name: LoginRouteName, query: { sysId: to.query.sysId } })
      }
      return
    }

    next()
    return
  }

  // 防止死循环：如果你正跳转的路径已经是当前路径且 sysId 是默认值，就不跳了
  if (to.query?.sysId !== bfglob.sysId) {
    next({
      name: to.name || undefined,
      path: to.path,
      params: to.params,
      query: { ...to.query, sysId: bfglob.sysId || '00' },
      replace: true,
    })
    return
  }

  next() // fallback
})

/**
 * vue-router >= 4.1.4，不再使用params传递参数
 * 为用兼容当前vue2的项目语法，自定义一个路由参数的hook
 * @returns {{
 * setRouteParams: (function(routeKey: string, params: any): void),
 * getRouteParams: (function(routeKey: string, {clear: boolean}): any),
 * }}
 */
export function useRouteParams() {
  const prefix = 'vue_router_params_'

  function setRouteParams(routeKey, params) {
    sessionStorage.setItem(`${prefix}${routeKey}`, JSON.stringify(params))
  }

  function getRouteParams(routeKey, { clear = true } = {}) {
    let params = {}
    const paramsJson = sessionStorage.getItem(`${prefix}${routeKey}`)
    if (!paramsJson) return params

    // 清除参数
    if (clear) sessionStorage.removeItem(`${prefix}${routeKey}`)

    try {
      params = JSON.parse(paramsJson)
    } catch (e) {
      console.error('getRouteParams error:', e)
    }
    return params
  }

  return {
    setRouteParams,
    getRouteParams,
  }
}

export default router
