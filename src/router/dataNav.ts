import { computed } from 'vue'
import i18n from '@/modules/i18n'

const { t } = i18n.global

export interface NavItemConfig {
  label: string
  order: number // 菜单排序值
  activeIconPath: string
  inactiveIconPath: string
  defaultChildPath?: string // 默认子路由路径
}

const toIconPath = (name: string) => ({
  activeIconPath: `images/manage/${name}_selected.svg`,
  inactiveIconPath: `images/manage/${name}.svg`,
})

export const navItemConfig = computed<Record<string, NavItemConfig>>(() => ({
  Orgs: {
    label: 'nav.orgData',
    order: 1,
    ...toIconPath('org'),
  },
  Jobs: {
    label: 'nav.postData',
    order: 2,
    ...toIconPath('job'),
  },
  Users: {
    label: 'nav.userData',
    order: 3,
    ...toIconPath('user'),
  },
  controllerManage: {
    label: 'nav.ctrlData',
    order: 4,
    ...toIconPath('controller'),
    defaultChildPath: 'Controllers',
  },
  deviceManage: {
    label: 'dialog.deviceDataTitle',
    order: 5,
    ...toIconPath('device'),
    defaultChildPath: 'Devices',
  },
  phoneManage: {
    label: 'nav.phoneManage',
    order: 6,
    ...toIconPath('phone'),
    defaultChildPath: 'GatewayFilter',
  },
  patrolManage: {
    label: 'nav.patrolManage',
    order: 7,
    ...toIconPath('patrol'),
    defaultChildPath: 'LinePoint',
  },
  IOT: {
    label: 'nav.iotManage',
    order: 8,
    ...toIconPath('iot'),
  },
  otherManage: {
    label: 'nav.otherOperations',
    order: 9,
    ...toIconPath('other'),
    defaultChildPath: 'MapPoints',
  },
}))
