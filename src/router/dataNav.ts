import { computed } from 'vue'
import i18n from '@/modules/i18n'

const { t } = i18n.global

export interface NavItemConfig {
  label: string
  order: number // 菜单排序值
  activeIconPath: string
  inactiveIconPath: string
  default?: string // 默认进入的菜单
  childFilesName?: string // 子菜单的文件夹名称
  childrenDir?: string
  defaultChildPath?: string
}

const toIconPath = (name: string) => ({
  activeIconPath: `images/manage/${name}_selected.svg`,
  inactiveIconPath: `images/manage/${name}.svg`,
})

export const navItemConfig = computed<Record<string, NavItemConfig>>(() => {
  return {
    Orgs: {
      label: t('nav.orgData'),
      order: 1,
      name: 'Orgs',
      ...toIconPath('org'),
    },
    Jobs: {
      label: t('nav.postData'),
      order: 2,
      name: 'Jobs',
      ...toIconPath('job'),
    },
    Users: {
      label: t('nav.userData'),
      order: 3,
      name: 'Users',
      ...toIconPath('user'),
    },
    controllerManage: {
      label: t('nav.ctrlData'),
      order: 4,
      name: 'controllerManage',
      ...toIconPath('controller'),
      default: 'Controllers', // 默认进入controllerChildren/Controllers.vue
    },
    deviceManage: {
      label: t('dialog.deviceDataTitle'),
      order: 5,
      name: 'deviceManage',
      ...toIconPath('device'),
      default: 'Devices', // 默认进入devicesChildren/Devices.vue
    },
    phoneManage: {
      label: t('nav.phoneManage'),
      order: 6,
      name: 'phoneManage',
      ...toIconPath('phone'),
      default: 'GatewayFilter', // 默认进入phoneChildren/GatewayFilter.vue
    },
    patrolManage: {
      label: t('nav.patrolManage'),
      order: 7,
      name: 'patrolManage',
      ...toIconPath('patrol'),
      default: 'LinePoint', // 默认进入patrolChildren/LinePoint.vue
    },
    IOT: {
      label: t('nav.iotManage'),
      order: 8,
      name: 'IOT',
      ...toIconPath('iot'),
    },
    otherManage: {
      label: t('nav.otherOperations'),
      order: 9,
      name: 'otherManage',
      ...toIconPath('other'),
      default: 'MapPoints', // 默认进入otherChildren/MapPoints.vue
    },
  }
})
