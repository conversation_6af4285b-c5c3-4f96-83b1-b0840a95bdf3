<template>
  <div class="terminal-page-wrapper">
    <data-form-editor
      ref="formEditor"
      class="page-terminal"
      editor-class="terminal-editor"
      :title="$t('dialog.deviceDataTitle')"
      :tableName="dataTable.name"
      :data="dataTable.body"
      :column="dthead"
      :detailHead="dtdetailHead"
      :detailBodyName="dataTable.detailBodyName"
      :detail-render="detailRender"
      :getNewData="getNewData"
      :parseDataForEdit="parseDataForEdit"
      :checkDataDisable="checkDataDisable"
      :before-action="beforeEditAction"
      :getFormRef="() => $refs.dataEditorForm"
      @row-delete="onDelete"
      @row-update="onUpdate"
      @row-new="onNew"
      @row-dblclick="tableRowDbclick"
    >
      <template #form="{ formData, isNewStatus }">
        <el-form
          ref="dataEditorForm"
          :model="formData"
          :label-width="labelWidth"
          :rules="getRules(formData, isNewStatus)"
          :validate-on-rule-change="false"
          class="grid grid-cols-1 data-editor-form"
        >
          <el-form-item :label="$t('dialog.parentOrg')" prop="orgId">
            <el-select
              v-model="formData.orgId"
              :placeholder="$t('dialog.select')"
              filterable
              clearable
              :no-match-text="$t('dialog.noMatchText')"
            >
              <el-option
                v-for="item in selOrgList"
                :key="item.rid"
                :label="item.label"
                :value="item.rid"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('dialog.virOrg')" prop="virOrgsArr">
            <el-select
              v-model="formData.virOrgsArr"
              multiple
              filterable
              :placeholder="$t('dialog.select')"
              :disabled="
                notSupportedVirtualOrgTypes.includes(formData.deviceType)
              "
            >
              <el-option
                v-for="(item, index) in selOrgVirtualList"
                :key="index"
                :label="item.label"
                :value="item.rid"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('dialog.terminalName')" prop="selfId">
            <el-input v-model="formData.selfId" :maxlength="16" />
          </el-form-item>
          <el-form-item
            v-if="formData.deviceType === DeviceTypesEnum.UserCard"
            :label="$t('iot.devId')"
            prop="dmrId"
          >
            <el-input v-model="formData.dmrId" :maxlength="10" />
          </el-form-item>
          <el-form-item v-else label="DMRID" prop="dmrId">
            <generate-dmrId v-model="formData.dmrId" />
          </el-form-item>
          <el-form-item :label="$t('dialog.priority')" prop="priority">
            <el-select
              v-model="formData.priority"
              filterable
              :disabled="
                notSupportedVirtualOrgTypes.includes(formData.deviceType)
              "
            >
              <el-option
                v-for="(item, index) in priorityOpts"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('dialog.deviceType')" prop="deviceType">
            <div
              class="w-full select-btn-box"
              :class="{
                'has-button': formData.deviceType === DeviceTypes.PocDevice,
              }"
            >
              <el-select
                v-model="formData.deviceType"
                filterable
                :placeholder="$t('dialog.select')"
                :no-match-text="$t('dialog.noMatchText')"
                class="my-select"
                popper-class="my-select-popper"
                @change="val => deviceTypeChanged(val, formData)"
              >
                <el-option
                  v-for="item in deviceTypes"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                  :disabled="item.disabled"
                />
              </el-select>
              <el-tooltip
                v-if="formData.deviceType === DeviceTypes.PocDevice"
                effect="dark"
                :content="$t('dialog.pocDeviceManage')"
                placement="top"
              >
                <el-button
                  type="primary"
                  size="small"
                  icon="edit"
                  @click="editPocSettings(isNewStatus, formData)"
                />
              </el-tooltip>
            </div>
          </el-form-item>
          <el-form-item
            v-if="isEditingProchatDevice()"
            class="prochat-device-associated-name"
          >
            <template #label>
              <span
                class="form-item-label"
                :title="$t('dialog.prochatDeviceAssociatedName')"
                >{{ $t('dialog.prochatDeviceAssociatedName') }}</span
              >
            </template>
            <el-input
              v-model="JSON.parse(formData.setting).prochatName"
              type="text"
              :disabled="true"
            />
          </el-form-item>
          <prochat-device-select
            v-if="formData.deviceType === DeviceTypes.ProchatDevice"
            @update-prochat-info="onUpdateProchatInfo"
          />
          <el-form-item
            v-if="formData.deviceType === DeviceTypes.SipProtocolDevice"
            class="form-item-ellipsis"
          >
            <template #label>
              <span
                class="form-item-label"
                :title="$t('dialog.sipProtocolDevicePassword')"
                >{{ $t('dialog.sipProtocolDevicePassword') }}</span
              >
            </template>
            <el-input
              v-model="sipProtocolDevicePassword"
              minlength="6"
              show-password
            />
          </el-form-item>
          <el-form-item
            v-if="formData.deviceType === DeviceTypes.GeneralDmr"
            class="custom-form-item"
          >
            <el-checkbox
              v-model="formData.traditionalDmrAllowNetCall"
              :true-value="1"
              :false-value="0"
            >
              <span v-text="$t('dialog.allowOnlineCalls')" />
            </el-checkbox>
          </el-form-item>
          <el-form-item
            v-if="formData.deviceType === DeviceTypesEnum.VirtualClusterDevice"
            :label="$t('writeFreq.ownGroup')"
            prop="devGroup"
          >
            <el-select
              v-model="formData.devGroup"
              filterable
              clearable
              :placeholder="$t('dialog.select')"
              :no-match-text="$t('dialog.noMatchText')"
            >
              <el-option
                v-for="item in selOrgListWithOwnGroup"
                :key="item.rid"
                :label="item.label"
                :value="item.dmrId"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="fixedDevices.includes(formData.deviceType)"
            :label="$t('dialog.lngLat')"
          >
            <lon-lat v-model="formData.lonLat" />
          </el-form-item>
          <el-form-item :label="$t('dialog.exclusiveUser')" prop="deviceUser">
            <el-select
              v-model="formData.deviceUser"
              filterable
              clearable
              :placeholder="$t('dialog.select')"
              :no-match-text="$t('dialog.noMatchText')"
              :disabled="
                formData.deviceType === DeviceTypesEnum.PhoneRepeater ||
                formData.deviceType === DeviceTypesEnum.ProchatDevice
              "
            >
              <el-option
                v-for="(item, index) in userRids"
                :key="index"
                :label="item.label"
                :value="item.rid"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            v-show="supportedBlackWhiteListTypes.includes(formData.deviceType)"
            :label="$t('dialog.blackWhiteList')"
            prop="gatewayFilterRid"
          >
            <el-select
              v-model="formData.gatewayFilterRid"
              filterable
              clearable
              :placeholder="$t('dialog.select')"
              :no-match-text="$t('dialog.noMatchText')"
            >
              <el-option
                v-for="(item, index) in gatewayList"
                :key="index"
                :label="item.label"
                :value="item.rid"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="!isNewStatus"
            :label="$t('dialog.privilegeDevice')"
          >
            <div class="w-full select-btn-box has-button">
              <el-select
                :value="firstPrivilege"
                :placeholder="$t('dialog.select')"
                :no-data-text="$t('dialog.noMatchText')"
                class="my-select"
                collapse-tags
                popper-class="my-select-popper"
              >
                <el-option-group
                  v-if="authGpsDataDevicesList.length > 0"
                  :label="$t('dialog.alreadyApplied')"
                >
                  <el-option
                    v-for="item in authGpsDataDevicesList"
                    :key="item.appDmrid + '-' + item.grantDeviceDmrid"
                    class="privilege-option"
                    :label="item.appDmrid + '-' + item.grantDeviceDmrid"
                    :value="item.appDmrid + '-' + item.grantDeviceDmrid"
                  >
                    <div class="privilege-item-name">
                      <p>
                        {{ $t('dialog.authDevices') }}:
                        {{ getSelfId(item.grantDeviceDmrid) }}
                      </p>
                      <p>
                        {{ $t('dialog.grantUser') }}:
                        {{
                          getGrantUserName(item.grantUserRid) ??
                          item.grantUserName
                        }}
                      </p>
                      <p>
                        {{ $t('auth.expireTime') }}：{{
                          item.expireTime.split(' ')[0]
                        }}
                      </p>
                    </div>
                    <div class="clear-privilege-box">
                      <el-tooltip
                        effect="dark"
                        :open-delay="500"
                        :content="$t('dialog.delPrivilegeDevice')"
                        placement="top"
                      >
                        <el-icon
                          class="clear-privilege-btn"
                          size="small"
                          @click.stop.prevent="
                            delete_privilege_device_item_auth(item)
                          "
                        >
                          <CircleClose />
                        </el-icon>
                      </el-tooltip>
                    </div>
                  </el-option>
                </el-option-group>
                <el-option-group
                  v-if="devicePrivilegeList.length > 0"
                  :label="$t('dialog.alreadyAuth')"
                >
                  <el-option
                    v-for="item in devicePrivilegeList"
                    :key="item.appDmrid + '-' + item.grantDeviceDmrid"
                    class="privilege-option"
                    :label="item.appDmrid + '-' + item.grantDeviceDmrid"
                    :value="item.appDmrid + '-' + item.grantDeviceDmrid"
                  >
                    <div class="privilege-item-name">
                      <p>
                        {{ $t('dialog.reqDevice') }}：{{
                          getSelfId(item.appDmrid)
                        }}
                      </p>
                      <p>
                        {{ $t('dialog.grantUser') }}：{{
                          getGrantUserName(item.grantUserRid) ??
                          item.grantUserName
                        }}
                      </p>
                      <p>
                        {{ $t('auth.expireTime') }}:
                        {{ item.expireTime.split(' ')[0] }}
                      </p>
                    </div>
                    <div class="clear-privilege-box">
                      <el-tooltip
                        effect="dark"
                        :open-delay="500"
                        :content="$t('dialog.delPrivilegeDevice')"
                        placement="top"
                      >
                        <el-icon
                          class="clear-privilege-btn"
                          size="small"
                          @click.stop.prevent="
                            delete_privilege_device_item_apply(item)
                          "
                        >
                          <Close />
                        </el-icon>
                      </el-tooltip>
                    </div>
                  </el-option>
                </el-option-group>
              </el-select>
              <el-tooltip
                effect="dark"
                :content="$t('dialog.delAllPrivilegeDevice')"
                placement="top"
              >
                <el-button
                  type="warning"
                  size="small"
                  icon="close"
                  @click="delete_privilege_device"
                />
              </el-tooltip>
            </div>
          </el-form-item>
          <el-form-item
            v-if="checkDeviceTypeIsPocOrMobile(formData.deviceType)"
            class="rgps-form-item"
          >
            <el-checkbox
              v-model="rgpsMode"
              :true-value="1"
              :false-value="0"
              @change="val => rgpsModeChange(formData, val)"
            >
              <span v-text="$t('dialog.rgpsMode')" />
            </el-checkbox>
            <el-popover
              placement="right"
              width="350"
              trigger="hover"
              :content="$t('dialog.rpgsModeInfo')"
            >
              <template #reference>
                <el-icon class="ml-2 cursor-pointer" size="16">
                  <QuestionFilled />
                </el-icon>
              </template>
            </el-popover>
          </el-form-item>
          <el-form-item class="rgps-form-item">
            <el-checkbox
              v-model="formData.allowCallOffline"
              :true-value="1"
              :false-value="0"
            >
              <span v-text="$t('dialog.allowCallOffline')" /> </el-checkbox
            ><el-popover
              placement="right"
              width="350"
              trigger="hover"
              :content="$t('dialog.allowCallOfflineInfo')"
            >
              <template #reference>
                <el-icon class="ml-2 cursor-pointer" size="16">
                  <QuestionFilled />
                </el-icon>
              </template>
            </el-popover>
          </el-form-item>
          <el-form-item :label="$t('dialog.notes')" prop="note">
            <el-input
              v-model="formData.note"
              type="textarea"
              resize="none"
              :rows="3"
              :maxlength="128"
            />
          </el-form-item>
        </el-form>
      </template>

      <template #form-footer="{ onClose, onConfirm, isNewStatus }">
        <div class="flex justify-center gap-3 dialog-footer">
          <el-button class="w-32" @click="onClose">
            {{ $t('dialog.cancel') }}
          </el-button>
          <el-button
            class="w-32"
            type="primary"
            @click="onConfirm(isNewStatus)"
            v-text="$t('dialog.confirm')"
          />
          <template v-if="!isNewStatus">
            <el-button
              class="w-32"
              type="warning"
              @click="openChannelSetting"
              v-text="$t('dialog.channelManager')"
            />
          </template>
        </div>
      </template>
    </data-form-editor>
    <!-- 信道管理 -->
    <channel-settings
      v-if="channelVisible"
      v-model:visible="channelVisible"
      v-model:device="editRow"
    />
    <poc-settings
      v-if="pocSettingVisible"
      v-model:device="editRow"
      v-model:visible="pocSettingVisible"
      :is-new-status="pocSettingDialogIsNewStatus"
      @sync-poc-setting="updatePocSetting"
    />
  </div>
</template>

<script>
import bfproto from '@/modules/protocol'
import dbCmd from '@/modules/protocol/db.pb.cmd'
import bfprocess from '@/utils/bfprocess'
import bfutil, {
  assignFixedDeviceLonLat,
  CannotEditDeviceTypes,
  DefOrgRid,
  DeviceTypes,
  fixedDevices,
  formatDmrIdLabel,
  notSupportedChannelTypes,
  supportedBlackWhiteListTypes,
} from '@/utils/bfutil'
import maputil from '@/utils/map'
import bfNotify, { messageBox, Types } from '@/utils/notify'
import bfTime from '@/utils/time'
import validateRules from '@/utils/validateRules'
import vueMixin from '@/utils/vueMixin'
import { v1 as uuid } from 'uuid'
import {
  checkLicenseWithModuleName,
  getAuthModuleI18nKey,
  getLicense,
  LicenseModuleNames,
} from '@/utils/bfAuth'
import DataFormEditor, {
  EditStatus,
} from '@/components/common/DataFormEditor.vue'
import ChannelSettings from '@/components/common/ChannelSettings.vue'
import {
  gaddedProchatDevice,
  initGaddedProchatDevice,
  prochatUserRid,
  setProchatDeviceData,
} from '@/utils/prochatDeviceInfoList'
import { cloneDeep, debounce } from 'lodash'
import bfCrypto from '@/utils/crypto'
import { DefaultFormData as DefaultProchatFormData } from '@/components/common/prochatGatewaySetting.vue'
import { useRouteParams } from '@/router'
import { defineAsyncComponent } from 'vue'
import { QuestionFilled } from '@element-plus/icons-vue'

const licenses = getLicense().lic?.licenses ?? {}
// 是否有常规DMR终端接入授权
const hasTraditionalDmrAuth = checkLicenseWithModuleName(
  licenses,
  LicenseModuleNames.ModTraditionalDmr,
)
// 是否有SVT接入授权
const hasSvtAuth = checkLicenseWithModuleName(
  licenses,
  LicenseModuleNames.ModSvt,
)

const dbSubject = `db.${bfglob.sysId}`
const defaultData = {
  orgId: bfutil.getBaseDataOrgId(),
  virOrgs: '',
  selfId: bfutil.getBaseSelfId(),
  deviceUser: '',
  dmrId: '',
  deviceType: 0,
  setting: '{}',
  note: '',
  priority: 1,
  gatewayFilterRid: '',
  devGroup: '', // 使用dmrId
  traditionalDmrAllowNetCall: 1,
  channelLastModifyTime: bfTime.nowUtcTime(),
  channel: '{"channels": []}',
  lastRfConfigTime: bfTime.nowUtcTime(),
  lastRfWriteTime: bfTime.nowUtcTime(),

  // 以下为辅助参数
  virOrgsArr: [], // 为了方便处理虚拟单位，添加的属性
  lonLat: {
    lon: '',
    lat: '',
  },
  channels: [],
  pocSetting: '{}',
  allowCallOffline: 0, // 默认不允许离线呼叫
}

// 存储formData为key validateDmrId为value的弱映射
const validateDmrIdWeakMap = new WeakMap()
const { getRouteParams } = useRouteParams()
let pocOriginalPassword = ''

export default {
  name: 'BfDevices',
  mixins: [vueMixin],
  components: {
    QuestionFilled,
    ChannelSettings,
    DataFormEditor,
    generateDmrId: defineAsyncComponent(
      () => import('@/components/common/generateDmrId'),
    ),
    lonLat: defineAsyncComponent(
      () => import('@/components/common/lonLat.vue'),
    ),
    prochatDeviceSelect: defineAsyncComponent(
      () => import('@/components/common/prochatDeviceSelect.vue'),
    ),
    PocSettings: defineAsyncComponent(
      () => import('@/components/common/PocSetting.vue'),
    ),
  },
  data() {
    return {
      dataTable: {
        name: 'devicesTable',
        body: bfutil.objToArray(bfglob.gdevices.getAll()),
        detailBodyName: 'channels',
      },
      selOrgList: bfglob.gorgData.getList(),
      selOrgVirtualList: bfglob.gorgData.getList(1),
      userRids: bfglob.guserData.getList(),
      gatewayList: bfglob.gatewayFilter.getList(),
      editRow: {},
      channelVisible: false,
      // sip协议终端的密码，
      sipProtocolDevicePassword: '',
      devicePrivilegeList: [],
      authGpsDataDevicesList: [],
      pocSettingVisible: false,
      pocSettingDialogIsNewStatus: true,
      rgpsMode: 0,
    }
  },
  methods: {
    delete_privilege_device_item_apply(msgObj, showNotify = true) {
      return this.delete_privilege_device_item(msgObj, showNotify)
    },
    delete_privilege_device_item_auth(msgObj, showNotify = true) {
      return this.delete_privilege_device_item(msgObj, showNotify)
    },
    delete_privilege_device_item(msgObj, showNotify = true) {
      if (bfutil.notEditDataPermission()) {
        return
      }
      return bfproto
        .sendMessage(
          dbCmd.DB_APP_MAP_PRIVILEGE_DEVICE_DELETE,
          msgObj,
          'db_app_map_privilege_device',
          dbSubject,
        )
        .then(rpc_cmd_obj => {
          if (rpc_cmd_obj.resInfo === '+OK') {
            showNotify &&
              bfNotify.messageBox(
                this.$t('dialog.delPrivilegeDeviceSuccess'),
                'success',
              )
            bfglob.gapppMapPivilegeDevice.delete(msgObj.rid)
            return Promise.resolve(msgObj.rid)
          }
        })
        .catch(err => {
          showNotify &&
            bfNotify.messageBox(
              this.$t('dialog.delPrivilegeDeviceFail'),
              'error',
            )
          bfglob.console.log('delPrivilegeDeviceFail', err)
          return Promise.reject(false)
        })
    },
    delete_privilege_device() {
      if (bfutil.notEditDataPermission()) {
        return
      }
      const delPromisesApply = this.devicePrivilegeList.map(item => {
        return this.delete_privilege_device_item_apply(item, false)
      })
      const delPromiseAuth = this.authGpsDataDevicesList.map(item => {
        return this.delete_privilege_device_item_auth(item, false)
      })
      Promise.allSettled([...delPromiseAuth, ...delPromisesApply]).then(res => {
        const successRid = []
        res.forEach(r => {
          if (r.status === 'fulfilled') {
            successRid.push(r.value)
          }
        })
        const delLen = delPromiseAuth.length + delPromisesApply.length
        if (successRid.length === delLen) {
          this.authGpsDataDevicesList = []
          this.devicePrivilegeList = []
          bfNotify.messageBox(
            this.$t('dialog.delPrivilegeDeviceSuccess'),
            'success',
          )
        } else {
          this.authGpsDataDevicesList = this.authGpsDataDevicesList.filter(
            p => !successRid.includes(p.rid),
          )
          this.devicePrivilegeList = this.devicePrivilegeList.filter(
            p => !successRid.includes(p.rid),
          )
          bfNotify.messageBox(this.$t('dialog.delPrivilegeDeviceFail'), 'error')
        }
      })
    },
    getSelfId(dmrid) {
      return bfglob.gdevices.getDataByIndex(dmrid)?.selfId ?? dmrid
    },
    getGrantUserName(rid) {
      return bfglob.guserData.get(rid).userName ?? ''
    },
    // 打开编辑框之前的操作
    beforeEditAction(status, data) {
      if (bfutil.notEditDataPermission()) {
        return Promise.reject('No permission')
      }
      this.editRow = data
      if (status === EditStatus.Edit) {
        this.devicePrivilegeList = bfutil
          .objToArray(bfglob.gapppMapPivilegeDevice.getAll())
          .filter(p => p.grantDeviceDmrid === data.dmrId)
        this.authGpsDataDevicesList = bfutil
          .objToArray(bfglob.gapppMapPivilegeDevice.getAll())
          .filter(p => p.appDmrid === data.dmrId)
      }
      if (status === EditStatus.Add) {
        data.dmrId = data.dmrId === '' ? '00080000' : data.dmrId
        while (bfglob.gdevices.getDataByIndex(data.dmrId)) {
          data.dmrId = bfutil.dmrIdAutoIncrement(data.dmrId)
          if (data.dmrId === '000FFFFF') {
            break
          }
        }
        this.editRow.pocSetting = '{}'
      }
      return Promise.resolve(true)
    },
    async onDelete(row) {
      await this.delete_device_data(row, dbCmd.DB_DEVICE_DELETE)
    },
    async onUpdate(row, done) {
      const oldData = bfglob.gdevices.get(row.rid)

      if (row.deviceType === DeviceTypes.ProchatDevice) {
        if (!this.validateProchat(row, oldData)) {
          return
        }
        row.deviceUser = prochatUserRid
      }

      // 如果修改了 prochat 终端的类型，清除关联的prochat配置
      if (
        oldData.deviceType === DeviceTypes.ProchatDevice &&
        row.deviceType !== DeviceTypes.ProchatDevice
      ) {
        try {
          const prochatSetting = JSON.parse(row.setting)
          gaddedProchatDevice.delete(prochatSetting.prochatID)
          delete prochatSetting.prochatID
          delete prochatSetting.prochatName
          row.setting = JSON.stringify(prochatSetting)
        } catch (e) {
          row.setting = '{}'
        }
      }

      if (row.deviceType === DeviceTypes.PocDevice) {
        try {
          row.pocSetting = this.editRow.pocSetting
          const pocSetting = JSON.parse(row.pocSetting || '{}')
          if (pocSetting.pocConfig?.rgps !== this.rgpsMode) {
            row.pocSettingLastModifyTime = bfTime.nowUtcTime()
            pocSetting.pocConfig.rgps = this.rgpsMode
          }
          row.pocSetting = JSON.stringify(pocSetting)
          if (!(pocSetting?.txGroupDmrid && pocSetting?.password)) {
            bfNotify.messageBox(
              this.$t('dialog.pocFormErrorTip'),
              Types.warning,
              { customClass: 'poc-confirm-warn' },
            )
            this.editPocSettings(true, row)
            return
          }
        } catch (e) {
          bfutil.console.log('parse pocSetting error', e, row.pocSetting)
        }
      }

      if (row.deviceType === DeviceTypes.SipProtocolDevice) {
        if (this.sipProtocolDevicePassword === '') {
          bfNotify.messageBox(
            this.$t('msgbox.sipProtocolDevicePasswordIsNull'),
            Types.error,
          )
          return
        }
      }

      if (row.deviceType === DeviceTypes.VirtualClusterDevice && !hasSvtAuth) {
        const i18nKey = getAuthModuleI18nKey(LicenseModuleNames.ModSvt)
        messageBox(
          this.$t('auth.noSpecifiedModuleAuth', { module: this.$t(i18nKey) }),
          Types.error,
        )
        return
      }

      const isOk = await this.update_device_data(row, dbCmd.DB_DEVICE_UPDATE)
      if (!isOk) return
      done()
    },
    // addNewCb：存在这个回调函数则需要继续添加新的一行
    async onNew(row, done, addNewCb) {
      if (row.deviceType === DeviceTypes.ProchatDevice) {
        if (!this.validateProchat(row)) {
          return
        }
        row.deviceUser = prochatUserRid
      }
      // store password field in setting
      if (row.deviceType === DeviceTypes.SipProtocolDevice) {
        if (this.sipProtocolDevicePassword === '') {
          bfNotify.messageBox(
            this.$t('msgbox.sipProtocolDevicePasswordIsNull'),
            Types.error,
          )
          return
        }
      }
      if (row.deviceType === DeviceTypes.PocDevice) {
        try {
          row.pocSetting = this.editRow.pocSetting
          const pocSetting = JSON.parse(row.pocSetting || '{}')
          if (!(pocSetting?.txGroupDmrid && pocSetting?.password)) {
            bfNotify.messageBox(
              this.$t('dialog.pocFormErrorTip'),
              Types.warning,
              { customClass: 'poc-confirm-warn' },
            )
            this.editPocSettings(true, row)
            return
          }
          pocOriginalPassword = pocSetting.password
          // 确认添加时，才将密码加密编码。否则在还未确定添加时，二次点开poc终端管理弹窗，会展示加密后密码
          pocSetting.password = bfCrypto.sha256(row.dmrId + pocSetting.password)
          pocSetting.pocConfig.rgps = this.rgpsMode
          row.pocSetting = JSON.stringify(pocSetting)
        } catch (e) {
          bfutil.console.log('parse pocSetting error', e, row.pocSetting)
        }
      }

      if (row.deviceType === DeviceTypes.VirtualClusterDevice && !hasSvtAuth) {
        const i18nKey = getAuthModuleI18nKey(LicenseModuleNames.ModSvt)
        messageBox(
          this.$t('auth.noSpecifiedModuleAuth', { module: this.$t(i18nKey) }),
          Types.error,
        )
        return
      }

      const isOk = await this.add_device_data(row, dbCmd.DB_DEVICE_INSERT)
      if (!isOk) {
        if (row.deviceType === DeviceTypes.PocDevice) {
          try {
            const s = JSON.parse(row.pocSetting)
            s.password = pocOriginalPassword
            row.pocSetting = JSON.stringify(s)
          } catch (e) {
            // no-empty
          }
        }
        return
      }
      if (addNewCb) {
        const __data = this.getNewData()
        __data.deviceType = row.deviceType
        __data.dmrId =
          row.deviceType === DeviceTypes.UserCard
            ? (parseInt(row.dmrId, 16) + 1)
                .toString(16)
                .padStart(10, '0')
                .toUpperCase()
            : bfutil.dmrIdAutoIncrement(row.dmrId)
        __data.selfId = bfutil.customNumberIncrement(row.selfId)
        __data.orgId = row.orgId
        __data.priority = row.priority
        if (__data.deviceType === DeviceTypes.PocDevice) {
          try {
            const s = JSON.parse(row.pocSetting)
            s.password = pocOriginalPassword
            __data.pocSetting = JSON.stringify(s)
          } catch (e) {
            // no-empty
          }
        }
        // 重置标签页数据
        // bfutil.resetForm(this, 'dataEditorForm')
        addNewCb(__data)
        return
      }
      done()
    },
    updatePocSetting(pocSettingJson, lastModifyTime) {
      this.editRow.pocSetting = pocSettingJson
      this.editRow.pocSettingLastModifyTime = lastModifyTime
    },
    // 返回一个新的默认参数对象
    getNewData() {
      return cloneDeep(defaultData)
    },
    parseDataForEdit(formData) {
      // 没有指定用户，则清空该属性，避免出现默认的rid
      if (formData.deviceUser === bfutil.DefOrgRid) {
        formData.deviceUser = ''
      }
      // 没有指定黑白名单，则清空该属性，避免出现默认的rid
      if (formData.gatewayFilterRid === bfutil.DefOrgRid) {
        formData.gatewayFilterRid = ''
      }

      if (formData.deviceType === DeviceTypes.SipProtocolDevice) {
        this.sipProtocolDevicePassword =
          JSON.parse(formData.setting).password ?? ''
      }

      try {
        if (formData.deviceType === DeviceTypes.ProchatDevice) {
          return
        }
        const setting = JSON.parse(formData.setting)
        formData.lonLat = setting.lonLat ?? formData.lonLat
        const channelSetting = JSON.parse(formData.channel)
        formData.channels = channelSetting.channels ?? formData.channels
        if (this.checkDeviceTypeIsPocOrMobile(formData.deviceType)) {
          this.rgpsMode = setting?.rgps ?? 0
        }
      } catch (e) {
        bfglob.console.error('devices.vue parseDataForEdit catch:', e)
      }
    },
    /**
     * 检查一行数据的操作列是否禁用，例如没有权限
     * @param {Record<string, any>} row 被检查的数据
     * @param {Record<string, any>} meta datatables的列配置的render方法的meta参数
     * @returns {boolean}
     */
    checkDataDisable(row, meta) {
      return CannotEditDeviceTypes.includes(row.deviceType)
    },
    getRules(formData, isNewStatus) {
      const deviceUserRules = [validateRules.required('blur')]
      // const devGroupRules = [validateRules.required('blur')]
      if (!validateDmrIdWeakMap.has(formData)) {
        const validateDmrId = validateRules.validateDmrId({
          encodeMsgType: 'db_device',
          decodeMsgType: 'db_device_list',
          command: dbCmd.DB_DEVICE_GETBY,
          dataManager: bfglob.gdevices,
        })
        validateDmrIdWeakMap.set(formData, validateDmrId)
      }

      return {
        orgId: [validateRules.required('blur')],
        selfId: [validateRules.required('blur')],
        dmrId: [
          validateRules.required('blur'),
          {
            validator: (rule, value, callback) => {
              if (value !== formData.dmrId) {
                return callback()
              }
              const device = bfglob.gdevices.getDataByIndex(value)
              if (device && device.rid === formData.rid) {
                return callback()
              }

              const validate = validateDmrIdWeakMap.get(formData)
              if (validate) {
                validate(rule, value, callback)
              } else {
                callback()
              }
            },
            trigger: 'change',
          },
        ],
        // 指挥坐席类型，必须指定专属用户
        deviceUser:
          formData.deviceType === DeviceTypes.CmdAgent ? deviceUserRules : [],
        // devGroup: formData.deviceType === DeviceTypes.VirtualClusterDevice ? devGroupRules : [],
        devGroup: [],
      }
    },
    setPropertyWithDeviceType(target, deviceType) {
      // 设备类型是网关，屏蔽一些参数设置，使用默认值
      if (this.notSupportedVirtualOrgTypes.includes(deviceType)) {
        target.virOrgsArr = []
        target.priority = 1
      }

      if (deviceType === DeviceTypes.PhoneRepeater) {
        target.deviceUser = ''
      } else if (deviceType === DeviceTypes.UserCard) {
        target.dmrId = ''
      }
    },
    deviceTypeChanged(val, formData) {
      this.setPropertyWithDeviceType(formData, val)

      // 不是指挥坐席，清除专属用户表单校验结果
      if (val !== DeviceTypes.CmdAgent) {
        this.clearValidate('dataEditorForm', ['deviceUser'])
      }
      // 不是虚拟集群终端，清除归属组
      if (val !== DeviceTypes.VirtualClusterDevice) {
        formData.devGroup = ''
      }
      // poc 终端
      if (val === DeviceTypes.PocDevice) {
        formData.pocSetting = this.editRow.pocSetting || '{}'
        this.editRow = formData
        this.pocSettingVisible = true
      } else {
        formData.pocSetting = '{}'
      }
      if (val === DeviceTypes.UserCard) {
        this.$refs.dataEditorForm.clearValidate()
      }
    },
    openChannelSetting() {
      // 电话网关、指挥坐席等类型不需要设置信道信息
      if (this.isExcludeType(this.editRow.deviceType)) {
        const typeName = this.getDeviceTypeName(this.editRow.deviceType)
        bfNotify.messageBox(
          this.$t('msgbox.telephoneNotSetChannelData', { type: typeName }),
        )
        return
      }

      // 打开信道管理窗口
      this.channelVisible = true
    },
    isExcludeType(type) {
      return notSupportedChannelTypes.includes(type)
    },
    tableRowDbclick(data) {
      bfNotify.messageBox(this.$t('dialog.devicePointJumpTip'))
      data && maputil.dbclickJumpToMarker(data, 1)
    },
    // 检测固定位置的终端添加经纬度时是否要创建marker
    checkFixedDeviceIsCreateMarker(data) {
      if (!fixedDevices.includes(data.deviceType)) {
        return
      }

      data = assignFixedDeviceLonLat(data)
      const marker = bfglob.gdevices.getMarker(data.rid)
      if (!marker) {
        bfprocess.createDeviceMarker(data)
        return
      }

      bfglob.map &&
        marker.setLngLat([data.lastLon, data.lastLat]).addTo(bfglob.map)
    },
    wrapperDmrId(data) {
      // 物联终端ID为5个字节，10位长度字符串
      if (data.deviceType === DeviceTypes.UserCard) {
        if (data.dmrId.length < 10) {
          data.dmrId = data.dmrId.padStart(10, '0')
        }
        data.dmrId = data.dmrId.toUpperCase()
      }
    },
    add_device_data(data, add_cmd) {
      data.virOrgs = data.virOrgsArr.join(',')
      const settings = this.fixedDevices.includes(data.deviceType)
        ? { lonLat: data.lonLat }
        : JSON.parse(data.setting)
      if (data.deviceType === DeviceTypes.SipProtocolDevice) {
        settings.password = this.sipProtocolDevicePassword
      }
      // poc和网络对讲终端的setting添加rgps属性
      if (this.checkDeviceTypeIsPocOrMobile(data.deviceType)) {
        settings.rgps = this.rgpsMode
      }
      const msgObj = {
        ...data,
        rid: uuid(),
        orgId: data.orgId || DefOrgRid,
        deviceUser: data.deviceUser || DefOrgRid,
        setting: JSON.stringify(settings),
        channel: JSON.stringify({ channels: [] }),
        channelLastModifyTime: bfTime.nowUtcTime(),
        gatewayFilterRid: data.gatewayFilterRid || bfutil.DefOrgRid,
        lastRfConfigTime: bfTime.nowUtcTime(),
        lastRfWriteTime: bfTime.nowUtcTime(),
        pocSettingLastModifyTime: bfTime.nowUtcTime(),
      }
      this.wrapperDmrId(msgObj)

      if (msgObj.deviceType === DeviceTypes.ProchatDevice) {
        msgObj.deviceUser = prochatUserRid
      }

      return bfproto
        .sendMessage(add_cmd, msgObj, 'db_device', dbSubject)
        .then(rpc_cmd_obj => {
          bfglob.console.log('add device res:', rpc_cmd_obj)
          const isOk = rpc_cmd_obj.resInfo === '+OK'
          if (isOk) {
            bfNotify.messageBox(this.$t('msgbox.addSuccess'), 'success')
            bfglob.emit('add_global_deviceData', msgObj)
            this.checkFixedDeviceIsCreateMarker(msgObj)

            // add to gaddedProchatDevice
            if (data.deviceType === DeviceTypes.ProchatDevice) {
              const prochatID = JSON.parse(data.setting).prochatID
              gaddedProchatDevice.set(prochatID, data.dmrId)
            }

            defaultData.deviceType = msgObj.deviceType
            defaultData.dmrId =
              msgObj.deviceType === DeviceTypes.UserCard
                ? (parseInt(msgObj.dmrId, 16) + 1)
                    .toString(16)
                    .padStart(10, '0')
                    .toUpperCase()
                : bfutil.dmrIdAutoIncrement(msgObj.dmrId)
            defaultData.selfId = bfutil.customNumberIncrement(msgObj.selfId)
            defaultData.orgId = msgObj.orgId
            defaultData.priority = msgObj.priority

            // 添加查询日志
            const note =
              this.$t('dialog.add') +
              msgObj.selfId +
              ' / ' +
              msgObj.dmrId +
              this.$t('msgbox.deviceData')
            bfglob.emit('addnote', note)
          } else {
            if (rpc_cmd_obj.resInfo.includes('db_device_self_id_key')) {
              bfNotify.warningBox(this.$t('msgbox.repeatDevsName'))
            } else if (rpc_cmd_obj.resInfo.includes('db_device_dmr_id_key')) {
              bfNotify.warningBox(this.$t('msgbox.repeatDMRID'))
            } else if (rpc_cmd_obj.resInfo.includes('licence is full')) {
              bfNotify.warningBox(this.$t('msgbox.licenceIsFull'))
            } else {
              bfNotify.messageBox(this.$t('msgbox.addError'), 'error')
            }
          }
          return Promise.resolve(isOk)
        })
        .catch(err => {
          bfglob.console.warn('add device timeout:', err)
          bfNotify.messageBox(this.$t('msgbox.addError'), 'error')
          return Promise.resolve(false)
        })
    },
    update_device_data(data, up_db_cmd) {
      data.virOrgs = data.virOrgsArr.join(',')
      const settings = JSON.parse(data.setting || '{}')
      if (this.fixedDevices.includes(data.deviceType)) {
        settings.lonLat = data.lonLat
      } else {
        delete settings.lonLat
      }
      if (data.deviceType === DeviceTypes.SipProtocolDevice) {
        settings.password = this.sipProtocolDevicePassword
      }
      const msgObj = {
        ...data,
        deviceUser: data.deviceUser || bfutil.DefOrgRid,
        setting: JSON.stringify(settings),
        gatewayFilterRid: data.gatewayFilterRid || bfutil.DefOrgRid,
        lastRfConfigTime: data.lastRfConfigTime,
        lastRfWriteTime: data.lastRfWriteTime,
        // 修正信道设置过的数据
        channel:
          this.editRow.channel ||
          data.channel ||
          JSON.stringify({ channels: [] }),
        channelLastModifyTime:
          this.editRow.channelLastModifyTime ||
          data.channelLastModifyTime ||
          bfTime.nowUtcTime(),
      }
      this.wrapperDmrId(data)

      if (msgObj.deviceType === DeviceTypes.ProchatDevice) {
        msgObj.deviceUser = prochatUserRid
      }

      return bfproto
        .sendMessage(up_db_cmd, msgObj, 'db_device', dbSubject)
        .then(rpc_cmd_obj => {
          bfglob.console.log('update device res:', rpc_cmd_obj)
          const isOk = rpc_cmd_obj.resInfo === '+OK'
          if (isOk) {
            bfNotify.messageBox(this.$t('msgbox.upSuccess'), 'success')

            // update GaddedProchatDevice
            if (data.deviceType === DeviceTypes.ProchatDevice) {
              const oldData = bfglob.gdevices.get(data.rid)
              const oldProchatID = JSON.parse(oldData.setting).prochatID
              const prochatID = JSON.parse(data.setting).prochatID
              if (oldProchatID !== prochatID) {
                gaddedProchatDevice.delete(oldProchatID)
              }
              gaddedProchatDevice.set(prochatID, data.dmrId)
            }

            // 更新全局组织机构数据
            bfglob.emit('update_global_deviceData', msgObj)
            this.checkFixedDeviceIsCreateMarker(msgObj)

            // 添加查询日志
            const note =
              this.$t('dialog.update') +
              msgObj.selfId +
              ' / ' +
              msgObj.dmrId +
              this.$t('msgbox.deviceData')
            bfglob.emit('addnote', note)
          } else {
            if (rpc_cmd_obj.resInfo.includes('db_device_self_id_key')) {
              bfNotify.warningBox(this.$t('msgbox.repeatDevsName'))
            } else if (rpc_cmd_obj.resInfo.includes('db_device_dmr_id_key')) {
              bfNotify.warningBox(this.$t('msgbox.repeatDMRID'))
            } else {
              bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
            }
          }
          return Promise.resolve(isOk)
        })
        .catch(err => {
          bfglob.console.warn('update device timeout:', err)
          bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
          return Promise.resolve(false)
        })
    },
    delete_device_data(data, del_cmd) {
      return bfproto
        .sendMessage(del_cmd, data, 'db_device', dbSubject)
        .then(rpc_cmd_obj => {
          bfglob.console.log('delete device res:', rpc_cmd_obj)
          const isOk = rpc_cmd_obj.resInfo === '+OK'
          if (isOk) {
            bfNotify.messageBox(this.$t('msgbox.delSuccess'), 'success')
            bfglob.emit('delete_global_deviceData', data)

            if (data.deviceType === DeviceTypes.ProchatDevice) {
              const prochatID = JSON.parse(data.setting).prochatID
              gaddedProchatDevice.delete(prochatID)
            }

            // 添加查询日志
            const note =
              this.$t('dialog.delete') +
              data.selfId +
              ' / ' +
              data.dmrId +
              this.$t('msgbox.deviceData')
            bfglob.emit('addnote', note)
          } else {
            bfNotify.messageBox(this.$t('msgbox.delError'), 'error')
          }
          return Promise.resolve(isOk)
        })
        .catch(err => {
          bfglob.console.warn('delete device timeout:', err)
          bfNotify.messageBox(this.$t('msgbox.delError'), 'error')
          return Promise.resolve(false)
        })
    },
    getDeviceTypeName(type) {
      let deviceTypeName = ''
      for (const k in this.deviceTypes) {
        const item = this.deviceTypes[k]
        if (item.value === type) {
          deviceTypeName = item.label
          break
        }
      }
      return deviceTypeName
    },
    getSelOrgListItemByDmrId(dmrId) {
      for (const k in this.selOrgList) {
        const item = this.selOrgList[k]
        if (item.dmrId === dmrId) {
          return item
        }
      }
      return undefined
    },
    clearValidate(ref = '', prop = []) {
      const targetForm = this.$refs[ref]
      targetForm && targetForm.clearValidate(prop)
    },
    // 同步dataTable数据
    upsetDataTableBody() {
      this.dataTable.body = bfutil.objToArray(bfglob.gdevices.getAll())
    },
    onAddNewDevice(params) {
      this.$nextTick(() => {
        this.$refs.formEditor?.addNewData()
        this.$refs.formEditor.formData.dmrId = params.dmrId
      })
    },
    onUpdateProchatInfo(val) {
      setProchatDeviceData(this.$refs.formEditor?.formData, val)
    },
    isEditingProchatDevice() {
      return (
        this.$refs.formEditor?.formData.deviceType ===
          DeviceTypes.ProchatDevice && this.$refs.formEditor?.editStatus === 2
      )
    },
    // 验证 prochat 终端增加和修改是否允许
    // 如果是增加操作，oldData 为空
    validateProchat(data, oldData = null) {
      // 如果 device 的 setting 字段没有 prohcatID, 则不允许
      let setting
      try {
        setting = JSON.parse(data.setting)
      } catch (e) {
        bfNotify.messageBox(
          this.$t('msgbox.associatedProchatDeviceNotSet'),
          'error',
        )
        return false
      }
      if (!setting.prochatID) {
        bfNotify.messageBox(
          this.$t('msgbox.associatedProchatDeviceNotSet'),
          'error',
        )
        return false
      }
      // 如果 prochat 的 msID 已映射，则不允许
      const prochatID = JSON.parse(data.setting).prochatID
      if (oldData) {
        const oldProchatID = JSON.parse(oldData.setting).prochatID
        if (oldProchatID === prochatID) {
          return true
        }
      }
      if (gaddedProchatDevice.has(prochatID)) {
        bfNotify.messageBox(this.$t('msgbox.prochatDeviceIsExisted'), 'error')
        return false
      }

      return true
    },
    detailRender(row, defaultRender) {
      // prochat终端配置
      if (row.deviceType === DeviceTypes.ProchatDevice) {
        let setting
        try {
          setting = JSON.parse(row.setting)
        } catch (e) {
          setting = { ...DefaultProchatFormData }
        }

        return `<div class="prochat-info">
              <div class="prochat-info-item">
                <span class="label">${this.$t('dialog.prochatDeviceAssociatedName')}:</span>
                <span class="value">${setting.prochatName || ''}</span>
              </div>
           </div>`
      }
      if (row.deviceType === DeviceTypes.PocDevice) {
        let setting
        try {
          setting = JSON.parse(row.pocSetting)
        } catch (e) {
          bfglob.console.log('detailRender json parse error', e, row.pocSetting)
        }
        const txGroup =
          bfglob.gorgData.getDataByIndex(setting.txGroupDmrid)?.orgShortName ??
          setting.txGroupDmrid
        const rxGroups = setting.rxGroupDmrids.map(
          dmrId => bfglob.gorgData.getDataByIndex(dmrId)?.orgShortName ?? dmrId,
        )
        const individualContacts = setting.pocIndividualContacts.map(
          dmrId => bfglob.gdevices.getDataByIndex(dmrId)?.selfId ?? dmrId,
        )
        const groupContacts = setting.pocGroupContacts.map(
          dmrId => bfglob.gorgData.getDataByIndex(dmrId)?.orgShortName ?? dmrId,
        )
        const contacts = [...individualContacts, ...groupContacts]
        return ` <div class="poc-device-detail flex flex-col w-min px-2 py-1 text-left border">
                     <div class="flex mb-1">
                       <div class="title font-bold">${this.$t('dialog.sendGroup')}</div>
                       <div>${txGroup}</div>
                     </div>
                     <div class="flex mb-1">
                       <div class="title font-bold">${this.$t('dialog.rxGroup')}</div>
                       <div class="content">${rxGroups.join(', ')}</div>
                     </div>
                     <div class="flex">
                       <div class="title font-bold">${this.$t('dialog.addressBook')}</div>
                       <div class="content content-contacts" title="${contacts.join(',')}">${contacts.join(', ')}</div>
                     </div>
                   </div>`
      }

      return defaultRender()
    },
    editPocSettings(isNewStatus, formData) {
      this.pocSettingDialogIsNewStatus = isNewStatus
      this.pocSettingVisible = true
      this.editRow = formData
    },
    rgpsModeChange(formData, val) {
      try {
        const setting = JSON.parse(formData.setting || '{}')
        setting.rgps = val
        formData.setting = JSON.stringify(setting)
      } catch (e) {
        bfglob.console.warn('poc data JSON.parse fail', e, formData.setting)
      }
    },
    checkDeviceTypeIsPocOrMobile(type) {
      return type === DeviceTypes.PocDevice || type === DeviceTypes.MobileDevice
    },
  },
  mounted() {
    initGaddedProchatDevice()
    bfglob.on('add_global_deviceData', this.upsetDataTableBody)
    bfglob.on('update_global_deviceData', this.upsetDataTableBody)
    bfglob.on('delete_global_deviceData', this.upsetDataTableBody)
    bfglob.on('add_new_device', this.onAddNewDevice)
  },
  activated() {
    this.$route.params = getRouteParams(this.$route.name)
    if (this.$route.params.dmrId) {
      this.onAddNewDevice(this.$route.params)
    }
  },
  watch: {
    fullscreen: {
      handler(newVal) {
        bfglob.emit(this.dataTable.name, newVal)
      },
    },
  },
  computed: {
    selOrgListWithOwnGroup() {
      const dynamicOwnGroup = {
        dmrId: '',
        icon: 'icon-organize',
        isOrg: true,
        label: this.$t('dialog.dynamicOwnGroup'),
        rid: bfutil.DefOrgRid,
      }
      return { [bfutil.DefOrgRid]: dynamicOwnGroup, ...this.selOrgList }
    },
    firstPrivilege() {
      return this.getSelfId(
        this.devicePrivilegeList[0]?.appDmrid ??
          this.authGpsDataDevicesList[0]?.grantDeviceDmrid ??
          '',
      )
    },
    DeviceTypes() {
      return DeviceTypes
    },
    notSupportedVirtualOrgTypes() {
      return [DeviceTypes.PhoneRepeater, DeviceTypes.UserCard]
    },
    fullscreen() {
      return !(this.$root.layoutLevel > 0)
    },
    dthead() {
      return [
        {
          title: this.$t('dialog.parentOrg'),
          data: 'orgShortName',
          width: '100px',
        },
        {
          title: this.$t('dialog.terminalName'),
          data: 'selfId',
          width: this.isFR ? '120px' : '100px',
        },
        {
          title: this.$t('dialog.deviceDMRID'),
          data: 'dmrId',
          width: this.isFR ? '165px' : '135px',
          render: (data, type, row) => {
            return row.deviceType === DeviceTypes.UserCard
              ? data
              : formatDmrIdLabel(data)
          },
        },
        {
          title: this.$t('dialog.priority'),
          data: 'priority',
          width: '60px',
          render: (data, type, row, meta) => {
            const key = `dialog.${row.priority === 1 ? 'low' : row.priority === 2 ? 'mid' : 'high'}`
            return row.priority > 0 ? this.$t(key) : this.$t('dialog.nothing')
          },
        },
        {
          title: this.$t('dialog.deviceType'),
          data: null,
          width: this.isFR || this.isEN ? '120px' : '100px',
          render: (data, type, row, meta) => {
            data.deviceTypeName = this.getDeviceTypeName(row.deviceType)
            return data.deviceTypeName
          },
        },
        {
          title: this.$t('writeFreq.ownGroup'),
          data: 'devGroup',
          width: this.isFR || this.isEN ? '120px' : '100px',
          render: data => {
            if (!data) {
              return ''
            }

            const option = this.getSelOrgListItemByDmrId(data)
            return option?.label ?? data
          },
        },
        {
          title: this.$t('dialog.blackWhiteList'),
          data: 'gatewayFilterName',
          width: this.isFR ? '160px' : this.isEN ? '140px' : '100px',
        },
        {
          title: this.$t('dialog.exclusiveUser'),
          data: 'deviceUserName',
          width: this.isFR || this.isEN ? '140px' : '100px',
        },
        {
          title: this.$t('dialog.virOrg'),
          data: 'virOrgsName',
          width: this.locale === 'en' ? '135px' : '100px',
        },
        {
          title: this.$t('dialog.notes'),
          data: 'note',
          width: '150px',
        },
      ]
    },
    dlgTitle() {
      return this.$t('dialog.deviceDataTitle')
    },
    dtdetailHead() {
      return [
        {
          title: this.$t('dialog.index'),
          data: null,
          defaultContent: '',
          width: '60px',
          render: (data, type, row, meta) => {
            return meta.row + 1
          },
        },
        {
          title: this.$t('dialog.channel'),
          data: 'no',
          width: '80px',
        },
        {
          title: this.$t('dialog.sendGroup'),
          data: null,
          width: this.$i18n.locale === 'en' ? '135px' : '100px',
          render: (data, type, row, meta) => {
            // 判断是否为全呼
            if (row.sendGroup === bfglob.fullCallDmrId) {
              return this.$t('dialog.fullCall')
            }
            const org = bfglob.gorgData.getDataByIndex(row.sendGroup)
            return org ? org.orgShortName : ''
          },
        },
        {
          title: this.$t('dialog.listenGroup'),
          data: null,
          class: 'listenGroup-cell text-left',
          render: (data, type, row, meta) => {
            return (row.listenGroup || [])
              .map(item => {
                const org = bfglob.gorgData.getDataByIndex(item)
                return org ? org.orgShortName : ''
              })
              .filter(item => {
                // 过滤空字符串
                return !!item
              })
              .join(',')
          },
        },
      ]
    },
    labelWidth() {
      return this.isFR || this.isEN ? '140px' : '100px'
    },
    priorityOpts() {
      const options = [
        {
          label: this.$t('dialog.low'),
          value: 1,
        },
        {
          label: this.$t('dialog.mid'),
          value: 2,
        },
        {
          label: this.$t('dialog.high'),
          value: 3,
        },
      ]

      return options
    },
    deviceTypes() {
      const deviceTypes = [
        {
          value: DeviceTypes.Device, // 对讲机
          label: this.$t('dialog.interphone'),
        },
        {
          value: DeviceTypes.Mobile, // 车载台
          label: this.$t('dialog.DMRDevice'),
        },
        {
          value: DeviceTypes.CmdAgent, // 指挥坐席
          label: this.$t('dialog.commandAgent'),
        },
        {
          value: DeviceTypes.MobileDevice, // 网络对讲终端
          label: this.$t('dialog.mobileDevice'),
        },
        {
          value: DeviceTypes.PhoneRepeater, // 电话网关终端
          label: this.$t('dialog.telephoneGateway'),
        },
        // {
        //   value: 5,
        //   label: this.$t('dialog.internetGateway')
        // },
        {
          value: DeviceTypes.AnalogGateway, // 模拟网关终端
          label: this.$t('dialog.analogGatewayTerminal'),
        },
        {
          value: DeviceTypes.DigitalGateway, // 数字网关终端
          label: this.$t('dialog.digitalGatewayTerminal'),
        },
        {
          value: DeviceTypes.FixedMobile, // 基地台(固定位置的车台)
          label: this.$t('dialog.baseMobileRadio'),
        },
        {
          value: DeviceTypes.VirtualClusterDevice, // 11:虚拟集群对讲手台
          label: this.$t('dialog.virtualClusterDevice'),
        },
        {
          value: DeviceTypes.MeshDevice, // 13:Mesh终端
          label: this.$t('dialog.meshDevice'),
        },
        {
          value: DeviceTypes.ProchatDevice, // 14:prochat终端
          label: this.$t('dialog.prochatDevice'),
          disabled: false,
        },
        {
          value: DeviceTypes.SipProtocolDevice, // 16:sip协议终端
          label: this.$t('dialog.sipProtocolDevice'),
          disabled: false,
        },
        {
          value: DeviceTypes.PocDevice,
          label: this.$t('dialog.pocDevice'),
          disabled: false,
        },
      ]

      // 判断是否授权常规终端接入
      if (hasTraditionalDmrAuth) {
        deviceTypes.push({
          value: DeviceTypes.GeneralDmr, // 9:传统常规dmr手台
          label: this.$t('dialog.generalDmr'),
        })
      }

      if (bfglob.sysIniConfig.iotEnable) {
        deviceTypes.push({
          value: DeviceTypes.UserCard, // 物联巡查终端(人员卡)
          label: this.$t('iot.iotInspectDevice'),
        })
      }

      deviceTypes.push(
        {
          value: DeviceTypes.MeshGateway, // 12:Mesh网关终端
          label: this.$t('dialog.meshGatewayDevice'),
          disabled: true,
        },
        {
          value: DeviceTypes.VirtualRepeater, // 中继虚拟终端(带插话功能的中继自动创建)
          label: this.$t('dialog.repeaterVirtualTerminal'),
          disabled: true,
        },
        {
          value: DeviceTypes.SipGatewayDevice, // 10:sip网关终端
          label: this.$t('dialog.sipGatewayDevice'),
          disabled: true,
        },
        {
          value: DeviceTypes.ProchatGatewayDevice, //15:prochat网关终端
          label: this.$t('dialog.prochatGatewayDevice'),
          disabled: true,
        },
      )

      return deviceTypes
    },
    // 固定位置的终端类型列表
    fixedDevices() {
      return fixedDevices
    },
    DeviceTypesEnum() {
      return DeviceTypes
    },
    supportedBlackWhiteListTypes() {
      return supportedBlackWhiteListTypes
    },
    pocContactLineClamp() {
      return this.isMobile ? 4 : 2
    },
  },
}
</script>

<style lang="scss">
.terminal-editor.data-form-dialog .el-form {
  max-width: 500px;
}

.org_img {
  width: 36px;
  height: 36px;

  img {
    width: 100%;
    height: 100%;
    vertical-align: top;
    border-radius: 50%;
  }
}

.listenGroup-cell {
  padding: 4px 10px !important;
  text-align: left;
  max-width: 800px;
}

.terminal-page-wrapper .prochat-info {
  display: flex;
  padding: 8px 16px;

  .prochat-info-item {
    &:not(:first-child) {
      margin-left: 32px;
    }

    &.hidden {
      display: none;
    }
  }
}

.prochat-device-associated-name .form-item-label {
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.privilege-option {
  height: fit-content !important;
  display: flex;
  justify-content: space-between;
  margin-bottom: 2px;
}

.select-btn-box {
  display: flex;

  &.has-button .my-select .el-select__wrapper {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .el-button {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    height: 32px;
  }
}

.privilege-item-name {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 6px;
  font-size: 12px;

  p {
    margin: 0;
  }
}

.my-select-popper ul.el-select-group li:last-child {
  .privilege-item-name,
  .clear-privilege-box {
    border-bottom: none;
    margin-bottom: 0;
  }
}

.my-select-popper ul.el-select-group__wrap::after {
  background-color: #fff;
}

.privilege-option:last-child {
  border-bottom: none;
}

.clear-privilege-btn {
  font-size: 24px;
  color: rgb(230, 162, 60);
}

.clear-privilege-box {
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
}

.poc-confirm-warn {
  z-index: 3000 !important;
}

.poc-device-detail {
  .title {
    width: fit-content;
    padding-right: 10px;
  }

  .content {
    width: calc(100vw - 130px);
    display: -webkit-box;
    -webkit-line-clamp: v-bind(pocContactLineClamp);
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-wrap: break-word;
    white-space: normal !important;
    //white-space: normal;
    //overflow-wrap: break-word;
  }
}

.rgps-form-item .el-form-item__content {
  display: flex;
}
</style>
