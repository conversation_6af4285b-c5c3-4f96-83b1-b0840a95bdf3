<template>
  <section class="w-full h-[35px] text-white flex flex-col relative page-header-container">
    <div class="w-full h-[10px] flex-none"><!-- 顶部留白 --></div>
    <i class="w-[6px] h-[6px] absolute top-[14px] left-[10px] header-title-icon"></i>
    <div class="relative flex-auto flex page-header">
      <div class="w-[360px] header-title">
        <h3 class="m-0 p-0 text-[24px] translate-x-[26px] translate-y-[-12px] title-text">{{ t(unref(route.meta.navItemConfig)?.label) || route.name }}</h3>
      </div>
      <slot></slot>
    </div>
  </section>
</template>

<script setup lang="ts">
  import { unref } from 'vue'
  import { useRoute } from 'vue-router'
  import { useI18n } from 'vue-i18n'

  const route = useRoute()
  const { t } = useI18n()
</script>

<style lang="scss" scoped>
  .page-header-container {
    .header-title-icon {
      filter: drop-shadow(0 0 16px rgba(27, 169, 255, 0.36));
      border-top: 2px solid #fff;
      border-left: 2px solid #fff;
    }

    .page-header {
      background:
        linear-gradient(to right, rgba(65, 90, 156, 0.1216), rgba(65, 90, 156, 0.1216)),
        linear-gradient(to bottom, rgba(70, 162, 255, 0.18), rgba(70, 162, 255, 0));

      // 顶部边框线
      &::before {
        content: '';
        position: absolute;
        inset: 0;
        top: -1px;
        height: 1px;
        background: linear-gradient(to right, rgba(111, 179, 255, 0.251), rgba(111, 179, 255, 0.251));
      }

      // 底部边框线
      &::after {
        content: '';
        position: absolute;
        inset: 0;
        bottom: -1px;
        height: 1px;
        background: linear-gradient(to right, rgba(117, 176, 255, 0.051), rgba(117, 176, 255, 0.051));
      }

      .header-title {
        background: linear-gradient(to right, rgba(92, 176, 255, 0.38), rgba(91, 173, 255, 0.22) 20%, rgba(92, 176, 255, 0));

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          width: 32%;
          background: linear-gradient(to right, rgba(92, 176, 255, 0.38), rgba(91, 173, 255, 0));
        }

        .title-text {
          font-family: YouSheBiaoTiHei;
        }
      }
    }
  }
</style>
