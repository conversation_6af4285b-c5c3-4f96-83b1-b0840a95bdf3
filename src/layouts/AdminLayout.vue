<template>
  <el-container class="common-bg w-full h-full !flex-col app-layout">
    <!-- 顶部导航 -->
    <BGHead class="flex-none" />

    <!-- 路由页面 -->
    <el-container class="flex-auto gap-[45px]">
      <bf-data-nav class="w-[292px]" />

      <router-view v-slot="{ Component }">
        <transition name="fade-transform" mode="out-in">
          <!-- 使用 keep-alive 缓存路由组件，必须是单一组件 -->
          <keep-alive>
            <el-main class="page-container !pr-[38px] !p-0 flex flex-col">
              <page-header>
                <div v-if="childRoutes.length > 0" class="flex items-center gap-4 z-[100]">
                  <el-tag
                    v-for="child in childRoutes"
                    :key="child.name"
                    class="!border-none text-white text-[14px] font-medium cursor-pointer"
                    :style="getTagStyle(child)"
                    @click="handleChildRouteClick(child)"
                  >
                    {{ getChildRoute<PERSON>abel(child) }}
                  </el-tag>
                </div>
              </page-header>
              <component :is="Component" class="!p-0" />
            </el-main>
          </keep-alive>
        </transition>
      </router-view>
    </el-container>
  </el-container>
</template>

<script setup>
  import '@/modules/dataManager'
  import BGHead from '@/layouts/BFHead.vue'
  import BfDataNav from '@/layouts/BfDataNav.vue'
  import PageHeader from '@/layouts/PageHeader.vue'
  import bfprocess from '@/utils/bfprocess.js'
  import { useRoute, useRouter } from 'vue-router'
  import { computed } from 'vue'
  import { useI18n } from 'vue-i18n'

  const route = useRoute()
  const router = useRouter()
  const { t } = useI18n()
  bfprocess.loginedAfterFunc()

  // 获取当前路由的子路由
  const childRoutes = computed(() => {
    // 获取当前二级路由名称（如 controllerManage, deviceManage 等）
    const currentParentRoute = route.matched[1]?.name
    if (!currentParentRoute) return []

    // 从路由配置中获取子路由
    const parentRouteConfig = router
      .getRoutes()
      .find(r => r.path === '/dataManage')
      ?.children?.find(child => child.name === currentParentRoute)

    return parentRouteConfig?.children || []
  })

  // 子路由名称映射
  const childRouteLabels = {
    // 设备管理子路由
    Controllers: t('nav.ctrlData'),

    // 终端管理子路由
    Devices: t('dialog.deviceDataTitle'),

    // 电话管理子路由
    GatewayFilter: t('nav.phoneBlackWhiteList'),
    GatewayPermission: t('nav.phoneDeviceAuth'),
    PredefinedPhoneBook: t('nav.predefinedPhoneBook'),
    ShortNumberMapping: t('nav.phoneGatewayMapping'),

    // 巡逻管理子路由
    LinePoint: t('nav.patrolPointManage'),
    Lines: t('nav.patrolRouteManage'),
    Rules: t('nav.patrolRuleManage'),

    // 其他操作子路由
    MapPoints: t('nav.mapPointData'),
    DynamicGroup: t('dynamicGroup.title'),
  }

  // 获取子路由的显示标签
  const getChildRouteLabel = child => {
    return childRouteLabels[child.name] || child.name
  }

  // 获取标签样式
  const getTagStyle = child => {
    const isActive = route.name === child.name

    if (isActive) {
      // 激活状态的橙色渐变
      return 'background: linear-gradient(90deg, rgba(253, 161, 19, 0.0001) 0%, #fda215 50.16%, rgba(254, 159, 15, 0.0001) 100%)'
    } else {
      // 非激活状态的蓝色渐变
      return 'background: linear-gradient(90deg, rgba(20, 186, 255, 0.00006) 0%, rgba(20, 186, 255, 0.6) 50.16%, rgba(20, 186, 255, 0.00006) 100%)'
    }
  }

  // 处理子路由点击
  const handleChildRouteClick = child => {
    const currentParentRoute = route.matched[1]?.name
    if (!currentParentRoute) return

    const targetPath = `/dataManage/${currentParentRoute}/${child.name}`
    router.push({
      path: targetPath,
      query: route.query, // 保持查询参数
    })
  }
</script>

<style scoped lang="scss"></style>
