<template>
  <el-container class="common-bg w-full h-full !flex-col app-layout">
    <!-- 顶部导航 -->
    <BGHead class="flex-none" />

    <!-- 路由页面 -->
    <el-container class="flex-auto gap-[45px]">
      <bf-data-nav class="w-[292px]" />

      <router-view v-slot="{ Component }">
        <transition name="fade-transform" mode="out-in">
          <!-- 使用 keep-alive 缓存路由组件，必须是单一组件 -->
          <keep-alive>
            <el-main class="page-container !pr-[38px] !p-0 flex flex-col">
              <page-header>
                <div class="flex items-center gap-4 z-[100]">
                  <el-tag
                    class="!border-none text-white text-[14px] font-[500] cursor-pointer"
                    style="background: linear-gradient(90deg, rgba(20, 186, 255, 0.00006) 0%, rgba(20, 186, 255, 0.6) 50.16%, rgba(20, 186, 255, 0.00006) 100%)"
                  >
                    二级菜单
                  </el-tag>
                  <el-tag
                    class="!border-none text-white text-[14px] font-medium cursor-pointer"
                    style="background: linear-gradient(90deg, rgba(253, 161, 19, 0.0001) 0%, #fda215 50.16%, rgba(254, 159, 15, 0.0001) 100%)"
                  >
                    二级菜单
                  </el-tag>
                  <el-tag
                    class="!border-none text-white text-[14px] font-medium cursor-pointer"
                    style="background: linear-gradient(90deg, rgba(20, 186, 255, 0.00006) 0%, rgba(20, 186, 255, 0.6) 50.16%, rgba(20, 186, 255, 0.00006) 100%)"
                  >
                    二级菜单
                  </el-tag>
                </div>
              </page-header>
              <component :is="Component" class="!p-0" />
            </el-main>
          </keep-alive>
        </transition>
      </router-view>
    </el-container>
  </el-container>
</template>

<script setup>
  import '@/modules/dataManager'
  import BGHead from '@/layouts/BFHead.vue'
  import BfDataNav from '@/layouts/BfDataNav.vue'
  import PageHeader from '@/layouts/PageHeader.vue'
  import bfprocess from '@/utils/bfprocess.js'
  import { useRoute } from 'vue-router'

  const route = useRoute()
  console.log('当前路由:', route)

  bfprocess.loginedAfterFunc()
</script>

<style scoped lang="scss"></style>
