<template>
  <div class="mt-[28px] pl-[40px]">
    <template v-for="navItem in navList" :key="navItem.name">
      <MenuBtn :nav-item="navItem" :is-active="isActive(navItem.path)" @click="handleMenuClick" />
    </template>
  </div>
</template>

<script setup lang="ts">
  import { RouteRecordRaw, useRouter, useRoute } from 'vue-router'
  import { computed } from 'vue'
  import MenuBtn from '@/components/common/MenuBtn.vue'
  import { type NavItemConfig, navItemConfig } from '@/router/dataNav'

  const router = useRouter()
  const route = useRoute()
  const routes =
    router.getRoutes().find(item => {
      return item.path === '/dataManage'
    })?.children || []

  // 菜单配置对象参数
  const navList = computed<Array<NavItemConfig & RouteRecordRaw>>(() => {
    const list = routes.map(item => {
      return {
        ...item,
        ...(navItemConfig.value[item.path] || {}),
      }
    })
    list.sort((a, b) => {
      return a.order - b.order
    })
    return list
  })

  const isActive = (path: string) => {
    return route.path.includes(path)
  }

  const handleMenuClick = navItem => {
    router.push(navItem)
  }
</script>

<style scoped></style>
