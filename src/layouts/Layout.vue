<template>
  <el-container class="h-full w-full app-layout">
    <!-- 页面顶部 -->
    <bf-head
      ref="header"
      @open-menu-item="openMenuItem"
      @head-height="onHeadHeight"
    />

    <!-- 路由页面 -->
    <router-view v-slot="{ Component }">
      <transition name="fade-transform" mode="out-in">
        <keep-alive>
          <component :is="Component" />
        </keep-alive>
      </transition>
    </router-view>

    <!-- 对话框页面 -->
    <component
      :is="comp"
      v-for="(comp, index) in asyncComponents"
      :key="index"
      :ref="comp.name"
    />
    <bf-deviceStatetable
      v-for="item in stateTables"
      :key="'statsTable' + item.name"
      :ref="item.name"
      :title="item.title"
      :name="item.name"
      :device="item.device"
    />
    <bf-bcxxResult
      v-for="item in bcxxResult"
      :key="'stats' + item.name"
      :ref="'stats' + item.name"
      :name="item.name"
      :result="item.result"
    />
    <bf-emergency-dialog
      v-for="item in bc18AlarmDialogs"
      :key="'bc18' + item.name"
      :ref="'bc18' + item.name"
      :name="item.name"
      :device="item.device"
      :cmdTime="item.cmdTime"
      :dbRid="item.dbRid"
    />
  </el-container>
</template>

<script>
import '@/modules/dataManager'
import bfprocess from '@/utils/bfprocess'
import {
  displayFirst,
  notRepeaterWfPermission,
  notSendCmdPermission,
} from '@/utils/bfutil'
import { warningBox } from '@/utils/notify'
import {
  checkLicenseWithAlert,
  checkRemainingExpirationTime,
} from '@/utils/bfAuth'
import { markRaw, defineAsyncComponent } from 'vue'

const metaPages = import.meta.glob([
  '@/components/dataDialog/*.vue',
  '@/components/historyDialog/*.vue',
  '@/components/secondary/*.vue',
  '@/components/command/*.vue',
])

export default {
  name: 'BfMain',
  provide() {
    return {
      mainApp: this,
    }
  },
  data() {
    return {
      asyncComponents: [],
      mapWidth: window.innerWidth - 260,
      treeWidth: 260,
      stateTables: [],
      bcxxResult: [],
      bc18AlarmDialogs: [],
    }
  },
  mounted() {
    // 订阅打开设备状态表消息
    bfglob.on('show_device_state', (tableObj, openSource) => {
      const __component = this.get_component(tableObj.name)
      if (!__component) {
        return
      }

      // 更新对话框
      if (openSource === 1) {
        __component.visible = true
      }
      // 如果状态表没有打开，则不更新状态数据
      if (__component.visible) {
        for (const i in this.stateTables) {
          const item = this.stateTables[i]
          if (item.name === tableObj.name) {
            this.stateTables[i] = Object.assign(this.stateTables[i], tableObj)
            break
          }
        }
        this.stateTables = [...this.stateTables]
      }
    })
    bfglob.on('create_device_status_table', device => {
      let title = device.selfId
      if (device.userName) {
        title += '/' + device.userName
      }
      const tableObj = {
        title: title,
        name: device.rid,
        device: device,
      }

      const oldComponent = this.stateTables
        .filter(data => {
          return data.name === device.rid
        })
        .shift()

      if (oldComponent) {
        bfglob.emit('show_device_state', tableObj, 1)
      } else {
        this.stateTables.push(tableObj)
      }
    })
    // 显示bcxx指令结果弹框消息
    bfglob.on(
      'show_bcxx_result',
      function (resObj) {
        this.load_dialog_of_bcxx(this.bcxxResult, 'stats', resObj)
      }.bind(this),
    )
    bfglob.on(
      'bcxx_destroyDialog',
      function (name) {
        this.destroyDialog_of_bcxx(this.bcxxResult, name)
      }.bind(this),
    )
    // 订阅bc18紧急报警弹框消息
    bfglob.on(
      'show_alarm_dialog',
      function (device, cmdTime, dbRid) {
        const _dialog = {
          name: device.rid,
          device: device,
          cmdTime: cmdTime,
          dbRid: dbRid,
        }
        this.load_dialog_of_bcxx(this.bc18AlarmDialogs, 'bc18', _dialog)
      }.bind(this),
    )
    bfglob.on(
      'alarm_destroyDialog',
      function (name) {
        this.destroyDialog_of_bcxx(this.bc18AlarmDialogs, name)
      }.bind(this),
    )

    bfglob.on('openMenuItem', (dlgName, cb) => {
      this.openMenuItem(dlgName, cb)
    })
  },
  methods: {
    onHeadHeight(height) {
      this.$el?.style?.setProperty('--head-width', `${height}px`)
    },
    getAsyncComponent(name) {
      return new Promise(resolve => {
        this.$nextTick(async () => {
          const refVm = this.$refs[name]
          if (!refVm) {
            // return resolve(undefined)
            return await this.getAsyncComponent(name)
          }
          if (Array.isArray(refVm)) {
            return resolve(refVm[0])
          }
          return resolve(refVm)
        })
      })
    },
    hasComponent(name) {
      for (let i = 0; i < this.asyncComponents.length; i++) {
        const comp = this.asyncComponents[i]
        if (comp.name === name) {
          return true
        }
      }
      return false
    },
    async loadAsyncComponent(path) {
      const comp = await metaPages[`/src/components/${path}.vue`]().then(
        res => {
          const comp = markRaw(res.default || res)
          if (!comp.name) {
            comp.name = path.split('/').pop()
          }
          return comp
        },
      )

      let isVisible = true
      if (!this.hasComponent(comp.name)) {
        this.asyncComponents.push(comp)

        // 第一次加载组件，如果是日志组件，则不显示
        if (path.includes('notes')) {
          isVisible = false
        }
      }

      const vm = await this.getAsyncComponent(comp.name)
      if (isVisible && typeof vm.visible === 'boolean') {
        vm.visible = true
      }
      // displayFirst(vm.$el.firstElementChild)

      return vm
    },
    isSuper() {
      // 如果是root下的账户，不允许发送命令,并弹出警告框
      if (bfglob.userInfo.isSuper) {
        warningBox(this.$t('msgbox.superAdminWarning'))
        return true
      }
      return false
    },
    // 加载打开的对话框
    openMenuItem(path, cb) {
      const dialogName = path.split('/').pop()
      // 操作权限
      const methods = {
        sendcmd: notSendCmdPermission,
        repeaterWriteFrequency: notRepeaterWfPermission,
        // "interphoneWriteFrequency": notIntercomWfPermission
      }
      if (methods[dialogName] && methods[dialogName]()) {
        return
      }
      if (dialogName.includes('sendcmd') && this.isSuper()) {
        return
      }

      this.loadAsyncComponent(path).then(vm => {
        typeof cb === 'function' && cb(vm)
      })
    },
    get_component(name) {
      let __component = this.$refs[name]
      if (__component instanceof Array) {
        __component = this.$refs[name][0]
      }
      return __component
    },
    load_dialog_of_bcxx(target, ref_str, source) {
      if (this[source.name]) {
        // 更新对话框
        for (const i in target) {
          let item = target[i]
          if (item.name === source.name) {
            item = Object.assign(item, source)
            displayFirst(
              this.$refs[ref_str + item.name]?.[0]?.$el?.firstElementChild,
            )
            break
          }
        }
      } else {
        // 创建对话框
        this[source.name] = Date.now()
        target.push(source)
      }
    },
    destroyDialog_of_bcxx(target, name) {
      for (const i in target) {
        const item = target[i]
        if (item.name === name) {
          this[name] = null
          target.splice(i, 1)
          break
        }
      }
    },
  },
  components: {
    bfHead: defineAsyncComponent(
      () => import('@/components/layout/BfHead.vue'),
    ),
    bfDeviceStatetable: defineAsyncComponent(
      () => import('@/components/secondary/deviceStateTable.vue'),
    ),
    bfBcxxResult: defineAsyncComponent(
      () => import('@/components/secondary/bcxxResult.vue'),
    ),
    bfEmergencyDialog: defineAsyncComponent(
      () => import('@/components/secondary/emergencyDialog.vue'),
    ),
  },
  computed: {
    fullscreen() {
      return this.$root.layoutLevel === 0
    },
  },
  async beforeMount() {
    // this.openMenuItem('layout/notes')

    // 如果没有授权,或授权过期，则不再请求数据
    if (checkLicenseWithAlert()) {
      // 判断是否要提示续期
      checkRemainingExpirationTime()
    } else {
      // 没有授权
      bfglob.emit('cancel-login-loading')
    }
  },
  beforeCreate() {
    const loginLoading = ElLoading.service({
      lock: true,
      text: this.$t('loginDlg.loading'),
      spinner: 'loading',
      background: 'rgba(0,0,0, 0.7)',
      customClass: 'loginLoading',
    })
    // 取消loading动画
    bfglob.once('cancel-login-loading', () => {
      loginLoading.close()
    })

    bfprocess.loginedAfterFunc()
  },
}
</script>

<style lang="scss">
.app-layout {
  --head-width: 40px;
  min-height: 100vh;
  flex-direction: column;
}

.el-cascader-menu {
  .el-cascader-menu__wrap {
    min-height: 60px;
    height: 100%;

    .el-cascader-menu__list {
      max-height: 50vh;
    }
  }
}

.el-menu--horizontal .el-menu .el-menu-item.is-active {
  color: #409eff;
}

.bf-drag .el-dialog {
  position: fixed;
  margin: 0;
  display: flex;
  flex-direction: column;
}

.el-dialog.is-fullscreen {
  width: 100% !important;
  height: 100% !important;
  top: unset !important;
  left: unset !important;
}

.el-dialog.is-fullscreen .el-dialog__body {
  overflow: auto;
}

.el-card__body {
  box-sizing: border-box;
  padding: 10px 15px;
  height: 100%;
}

.el-dialog--full {
  bottom: 0;
}

#bfnotes .el-dialog {
  min-height: 420px;
}

#bfnotes .el-dialog .el-dialog__body {
  position: relative;
}

#bfnotes .el-dialog .el-card,
#bfsendcmd .el-dialog .el-card {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  border-radius: 0 0 4px 4px;
  box-shadow: none;
}

.el-select-dropdown__item {
  height: 26px;
  line-height: 26px;
}

.el-select {
  width: 100%;
}

.resolve_dmrId .el-input.is-disabled .el-input__inner {
  cursor: pointer;
}

.el-message {
  min-width: 200px !important;
}

.el-table .cell {
  font-size: 14px;
  line-height: 20px;
  padding: 0;
  text-align: center;
}

.el-table td,
.el-table th {
  height: 30px;
}

.el-notification {
  padding: 10px 30px 10px 10px;
  display: flex;
}

.el-notification .el-notification__icon {
  flex: none;
  align-self: center;
}

.el-notification.sms-notification .el-notification__icon.iconfont {
  font-size: 30px;
  color: #67c23a;
}

.el-notification .el-notification__group {
  flex: auto;
  align-self: center;
}

.el-notification .el-notification__group .sms-senderName,
.el-notification .el-notification__group .sms-sendTime {
  font-size: 12px;
  color: #909399;
}

.el-notification .el-notification__group .sms-content {
  margin-bottom: 6px;
}

.el-notification .el-notification__group .el-notification__title,
.el-notification .el-notification__group .el-notification__content {
  text-align: left;
}

.el-notification .el-notification__group .el-notification__content {
  word-break: break-word;
}

.el-notification__closeBtn {
  top: 10px;
  right: 10px;
}

.head_btns {
  position: absolute;
  top: -3px;
  right: 33px;
}

.btns_table {
  position: absolute;
  right: 0;
  bottom: 0;
}

.btns_table button {
  min-width: 110px;
}

.data_btns {
  text-align: center;
  margin-top: 10px;
}

.data_btns button {
  width: 30%;
}

.bf-btn-plus {
  order: 10;
}

.bf-btn-minus {
  order: 20;
}

.siteNotify-content {
  display: flex;
  align-items: center;
}

.siteNotify-image {
  height: 40px;
  width: auto;
  max-width: 120px;
  vertical-align: middle;
  margin-right: 8px;
  flex: auto;
}

.siteNotify-text {
  flex: auto;
}

.first-dialog:not(.not-first) {
  z-index: 5000 !important;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.8);
}

.other-dialog:not(.not-first) {
  z-index: 2000 !important;
}
</style>
